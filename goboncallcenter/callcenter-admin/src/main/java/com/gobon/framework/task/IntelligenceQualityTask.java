package com.gobon.framework.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.gobon.common.Common;
import com.gobon.framework.aspectj.lang.annotation.DataSource;
import com.gobon.framework.aspectj.lang.enums.DataSourceType;
import com.gobon.framework.datasource.DynamicDataSourceContextHolder;
import com.gobon.framework.security.LoginUser;
import com.gobon.project.conversation.service.ICallcenterRecordDetailService;
import com.gobon.project.intelligencequality.constant.QualityRedisConstant;
import com.gobon.project.intelligencequality.domain.CallcenterQualitySamplingRule;
import com.gobon.project.intelligencequality.domain.param.QualityScanParam;
import com.gobon.project.intelligencequality.domain.param.SamplingRuleParam;
import com.gobon.project.intelligencequality.domain.param.ScoringRuleParam;
import com.gobon.project.intelligencequality.domain.vo.QualityCallInfoVo;
import com.gobon.project.intelligencequality.domain.vo.QualityScoringRuleVo;
import com.gobon.project.intelligencequality.executor.QualityScoringExecutor;
import com.gobon.project.intelligencequality.service.ICallcenterQualityInfoService;
import com.gobon.project.intelligencequality.service.ICallcenterQualitySamplingRuleService;
import com.gobon.project.intelligencequality.service.ICallcenterQualityScoringRuleService;
import com.gobon.project.keyword.service.ICallcenterKeywordService;
import com.gobon.project.networkquality.domain.vo.NetworkQualityChatVO;
import com.gobon.project.networkquality.domain.vo.NetworkQualityDetailVO;
import com.gobon.project.networkquality.mapper.NetworkQualityMapper;
import com.gobon.project.quality.domain.entity.CallcenterQualityTaskEntity;
import com.gobon.project.quality.enums.QualityTaskRunStatusEnums;
import com.gobon.project.quality.mapper.CallcenterQualityTaskDetailMapper;
import com.gobon.project.quality.mapper.CallcenterQualityTaskMapper;
import com.gobon.project.system.service.ISysConfigService;
import com.google.common.util.concurrent.AtomicDouble;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 智能质检任务
 */
@Slf4j
@Component("intelligenceQualityTask")
public class IntelligenceQualityTask {

    /**
     * 整数正则表达式
     */
    private static final Pattern PATTERN_INTEGER = Pattern.compile("^[1-9]\\d*$");
    /**
     * 随机抽样模式
     */
    private static final String QUALITY_MODE_RANGE = "0";
    /**
     * 满意度抽样过滤模式
     */
    private static final String QUALITY_MODE_SATISFACTION_FILTER = "1";
    /**
     * 全量抽样模式
     */
    private static final String QUALITY_MODE_FULL = "2";
    /**
     * 质检评分目标对象(技能组)
     */
    private static final String QUALITY_TARGET_SKILL = "1";
    /**
     * 质检评分目标对象(客服人员)
     */
    private static final String QUALITY_TARGET_AGENT = "2";
    /**
     * 上个月数据
     */
    private static final String QUALITY_EXEC_OF_MONTH = "1";
    /**
     * 过去7天数据
     */
    private static final String QUALITY_EXEC_OF_WEEK = "2";
    /**
     * 昨天数据
     */
    private static final String QUALITY_EXEC_OF_DAY = "3";

    /**
     * 一次性抽样
     */
    private static final String QUALITY_EXEC_ONCE = "4";

    /**
     * 智能质检redis key 前缀
     */
    private static final String REDIS_QUALITY_PREFIX = "quality:";

    @Autowired
    ICallcenterQualitySamplingRuleService callcenterQualitySamplingRuleService;
    @Autowired
    ICallcenterQualityScoringRuleService callcenterQualityScoringRuleService;
    @Autowired
    ICallcenterRecordDetailService callcenterRecordDetailService;
    @Autowired
    ICallcenterKeywordService callcenterKeywordService;
    @Autowired
    ICallcenterQualityInfoService callcenterQualityInfoService;
    @Resource
    CallcenterQualityTaskMapper callcenterQualityTaskMapper;
    @Resource
    CallcenterQualityTaskDetailMapper callcenterQualityTaskDetailMapper;
    @Resource
    NetworkQualityMapper networkQualityMapper;

    @Resource
    RedisTemplate redisTemplate;

    @Resource
    ISysConfigService sysConfigService;

    /**
     * 扫描通话记录
     *
     * @param samplingRuleIdString 抽样规则ID
     * @param domain               所属域
     * @param loginUser
     * @param platForm   1 热线  2网络
     * @param all   是否  检测全部
     */
    /*@Transactional(rollbackFor = Exception.class)*/
    public void scanCallRecordParam(String samplingRuleIdString, String domain, LoginUser loginUser, String platForm,Boolean all) {
        long startTime = System.currentTimeMillis();
        //任务开始执行

        CallcenterQualityTaskEntity callcenterQualityTaskEntity = new CallcenterQualityTaskEntity();
        callcenterQualityTaskEntity.setRunStatus(QualityTaskRunStatusEnums.RUN.getCode());
        callcenterQualityTaskEntity.setStartTime(new Date());
        callcenterQualityTaskEntity.setCreateTime(new Date());
        callcenterQualityTaskEntity.setCreateBy(loginUser.getUser() == null?null:loginUser.getUser().getUserName());
        callcenterQualityTaskEntity.setCreateById(loginUser.getUser() == null?null:loginUser.getUser().getUserId());
        callcenterQualityTaskEntity.setCarryPerson(loginUser.getUser() == null?null:loginUser.getUser().getNickName());
        callcenterQualityTaskEntity.setDelFlag("0");
        callcenterQualityTaskEntity.setTotalNum(0);
        callcenterQualityTaskEntity.setCheckNum(0);
        callcenterQualityTaskEntity.setPlatForm(platForm);
        callcenterQualityTaskMapper.insert(callcenterQualityTaskEntity);
        try{
            Assert.notNull(samplingRuleIdString, "质检失败: 抽样规则ID不能为空");
            Assert.notNull(domain, "质检失败: 所属域不能为空.");
            final Long samplingRuleId = Long.valueOf(samplingRuleIdString);
            final SamplingRuleParam param = new SamplingRuleParam();
            param.setAddrArea(domain);
            param.setSamplingRuleId(samplingRuleId);
            param.setPlatForm(platForm);



            // 查询抽样规则
            final CallcenterQualitySamplingRule samplingRule =
                    callcenterQualitySamplingRuleService.selectQualitySamplingRuleById(param);
            Assert.notNull(samplingRule, "质检失败: 无法查询指定智能质检抽样规则.");
            callcenterQualityTaskEntity.setSampleRuleId(samplingRule.getId());
            callcenterQualityTaskEntity.setSampleRuleName(samplingRule.getRuleName());
            final String mode = samplingRule.getSamplingModeCode();
            final String samplingCode;
            if (!QUALITY_MODE_FULL.equals(mode)) {
                samplingCode = callcenterQualitySamplingRuleService.selectQualitySamplingCode(samplingRuleId,
                        QUALITY_MODE_RANGE.equals(mode) ? Common.SAMPLING_NUMBER.getCode() :
                                Common.SATISFACTION_FILTER.getCode(), domain);
            } else {
                samplingCode = null;
            }
            // 查询评分规则及明细
            final ScoringRuleParam ruleParam = new ScoringRuleParam();
            ruleParam.setLoadBehaviorRule(true);
            ruleParam.setId(samplingRule.getScoringRuleId());
            ruleParam.setAddrArea(domain);
            final QualityScoringRuleVo scoringRule = callcenterQualityScoringRuleService.selectQualityScoringRule(ruleParam);
            Assert.notNull(scoringRule, "质检失败: 无法查询指定智能质检评分规则.");
            Assert.notEmpty(scoringRule.getScoringRuleItems(), "质检失败: 无法查询指定智能质检评分规则明细项列表.");
            callcenterQualityTaskEntity.setScoringRuleId(scoringRule.getId());
            callcenterQualityTaskEntity.setScoringRuleName(scoringRule.getRuleName());
            callcenterQualityTaskMapper.updateById(callcenterQualityTaskEntity);
            // 执行智能质检评分操作
            scoringRule.transform().transformMap().parse().parseAppealTime();
            // log.info("规则信息: {}", JSONObject.toJSONString(scoringRule.getScoringRuleMap(), SerializerFeature.DisableCircularReferenceDetect));
            CompletableFuture<Void>  future = doQualityScoring(samplingRule, samplingCode, scoringRule, domain,callcenterQualityTaskEntity,platForm,all);
            if(future != null){
                future.thenRun(()->{
                    callcenterQualityTaskEntity.setFinishTime(new Date());
                    callcenterQualityTaskEntity.setRunStatus(QualityTaskRunStatusEnums.SUCCESS.getCode());
                    long endTime = System.currentTimeMillis();
                    callcenterQualityTaskEntity.setEndTime(new Date());
                    callcenterQualityTaskEntity.setUsedTime(new BigDecimal((endTime - startTime) / (1000 * 60)));
                    callcenterQualityTaskMapper.updateById(callcenterQualityTaskEntity);//更新质检任务数量
                    callcenterQualityTaskMapper.updateTaskNum(callcenterQualityTaskEntity.getId());
                }).exceptionally(ex->{
                    callcenterQualityTaskEntity.setErrorLog(ex.getMessage());
                    callcenterQualityTaskEntity.setRunStatus(QualityTaskRunStatusEnums.FAIL.getCode());
                    long endTime = System.currentTimeMillis();
                    callcenterQualityTaskEntity.setEndTime(new Date());
                    callcenterQualityTaskEntity.setUsedTime(new BigDecimal((endTime - startTime) / (1000 * 60)));
                    callcenterQualityTaskMapper.updateById(callcenterQualityTaskEntity);
                    return null;
                });
            }
        }catch (Exception e){
            callcenterQualityTaskEntity.setErrorLog(e.getMessage());
            callcenterQualityTaskEntity.setRunStatus(QualityTaskRunStatusEnums.FAIL.getCode());
            long endTime = System.currentTimeMillis();
            callcenterQualityTaskEntity.setEndTime(new Date());
            callcenterQualityTaskEntity.setUsedTime(new BigDecimal((endTime - startTime) / (1000 * 60)));
            callcenterQualityTaskMapper.updateById(callcenterQualityTaskEntity);
            e.printStackTrace();
            log.error("执行智能质检失败："+e.getMessage());
        }finally {
            redisTemplate.delete(QualityRedisConstant.QUALITY_PROCESS_LOCK_KEY);
            redisTemplate.opsForValue().set(QualityRedisConstant.QUALITY_PROCESS_LOCK_TIME,"LOCKTIME",2,TimeUnit.MINUTES);
        }


    }

    /**
     * 执行智能质检评分操作
     *
     * @param samplingRule                {@link CallcenterQualitySamplingRule}
     * @param samplingCode                抽样范围code
     * @param scoringRule                 {@link QualityScoringRuleVo}
     * @param domain                      所属域(租户)
     * @param callcenterQualityTaskEntity 质检任务
     * @param platForm
     * @param all
     * @return
     * @throws Exception
     */
    private CompletableFuture<Void> doQualityScoring(final CallcenterQualitySamplingRule samplingRule, final String samplingCode,
                                                     final QualityScoringRuleVo scoringRule, final String domain,
                                                     CallcenterQualityTaskEntity callcenterQualityTaskEntity, String platForm, Boolean all) throws Exception {

        //查询网络质检小时数
        String wlHours = sysConfigService.selectConfigByKey("wl_hours");


        final QualityScanParam param = assembleParam(samplingRule, samplingCode, scoringRule,platForm);
        param.setDomain(domain);
        param.setSampleRuleId(samplingRule.getId());
        param.setScoringRuleId(scoringRule.getId());
        param.setAll(all);

        param.setWlHour(StrUtil.isNotBlank(wlHours)?Integer.valueOf(wlHours):2);

        log.info("智能质检任务参数: {}", param);

        //只抽取呼入的进行质检 修改为
        param.setRecordType(0);
        AtomicDouble progress = new AtomicDouble(0);
        redisTemplate.opsForValue().set(REDIS_QUALITY_PREFIX + "progress:" + samplingRule.getId(), (int)progress.get(), 30, TimeUnit.MINUTES);


        //todo   修改为根据平台查询对应  台账信息或通话信息
        List<QualityCallInfoVo> qualityCallInfos = new ArrayList<>();

        List<NetworkQualityDetailVO> networkQualityVOS = null;

        if(callcenterQualityTaskEntity.getPlatForm().equals("1")){
            qualityCallInfos = loadCallRecord(param);
        }else{
            networkQualityVOS = loadNetworkQualityVOList(param);
            if(param.getAll()){
                List<String> netWorkMulIds = networkQualityVOS.stream().map(NetworkQualityDetailVO::getMulId).collect(Collectors.toList());
                //取出已经生成得网络质检记录
                List<String> mulIds = callcenterQualityInfoService.getByNetWorkMulIds(netWorkMulIds);
                //排除已经生成得网络质检记录
                if(CollUtil.isNotEmpty(mulIds)){
                    networkQualityVOS = networkQualityVOS.stream().filter(item->!mulIds.contains(item.getMulId())).collect(Collectors.toList());
                }
                if(CollUtil.isEmpty(networkQualityVOS)){
                    log.info("网络质检任务执行完成，暂无检测到新的记录");
                    //直接抛异常中断
                    throw new Exception("网络质检任务执行完成，暂无检测到新的记录");
                }
            }
        }



        List<QualityCallInfoVo> finalQualityCallInfos = qualityCallInfos;
        List<NetworkQualityDetailVO> finalNetworkQualityVOS = networkQualityVOS;
        Consumer<Integer> progressConsumer = (i) -> {
            int size = CollUtil.isNotEmpty(finalQualityCallInfos)? finalQualityCallInfos.size() : finalNetworkQualityVOS.size();
            double nextValue = i == 0 ? progress.get() + 1 :  progress.get() + (100f / size / 2) * i;
            if(nextValue >= 100) {
                progress.set(100);
            } else {
                progress.set(nextValue);
            }

            ThreadUtil.sleep(2000);

            redisTemplate.opsForValue().set(REDIS_QUALITY_PREFIX + "progress:" + samplingRule.getId(), (int)progress.get(), 30, TimeUnit.MINUTES);
        };

        if ( (qualityCallInfos != null && !qualityCallInfos.isEmpty()) || (networkQualityVOS != null && !networkQualityVOS.isEmpty())) {
            // 查询质检信息
            return QualityScoringExecutor.of(domain, samplingRule, scoringRule, qualityCallInfos,networkQualityVOS,callcenterQualityTaskEntity,callcenterQualityTaskDetailMapper,
                    callcenterKeywordService, callcenterQualityInfoService,networkQualityMapper,callcenterQualityTaskEntity.getPlatForm()).doQualityScoring(progressConsumer);
        }
        throw new Exception("智能质检任务失败：无抽检通话信息");

    }

    @DataSource(DataSourceType.FWORACLE)
    public List<NetworkQualityDetailVO> loadNetworkQualityVOList(QualityScanParam param) {
        try{
            DynamicDataSourceContextHolder.setDataSourceType(DataSourceType.FWORACLE.name());
            List<NetworkQualityDetailVO> networkQualityVOS = networkQualityMapper.locdNetworkQualityVOList(param);
            //查询聊天记录
            if(CollUtil.isNotEmpty(networkQualityVOS)){
                networkQualityVOS.forEach(item->{
                    List<NetworkQualityChatVO> chatList = networkQualityMapper.getChatList(item.getConsultationId(), item.getMulId());
                    item.setChatVOS(chatList);
                });
            }

            return networkQualityVOS;
        }finally {
            DynamicDataSourceContextHolder.clearDataSourceType();
        }
    }

    /**
     * 组装参数
     *
     * @param samplingRule {@link CallcenterQualitySamplingRule}
     * @param samplingCode 抽样范围code
     * @param scoringRule  {@link QualityScoringRuleVo}
     * @param platForm
     */
    private QualityScanParam assembleParam(final CallcenterQualitySamplingRule samplingRule, final String samplingCode,
                                           final QualityScoringRuleVo scoringRule, String platForm) {
        final QualityScanParam param = new QualityScanParam();
        final String mode = samplingRule.getSamplingModeCode();
        final String target = scoringRule.getScoringObject();
        param.setQualityType(0);
        // 模式
        if (QUALITY_MODE_RANGE.equals(mode)) {
            Assert.isTrue(PATTERN_INTEGER.matcher(samplingCode).matches(), "质检失败: 无效随机抽样参数.");
            param.setRange(Integer.valueOf(samplingCode));
        } else if (QUALITY_MODE_SATISFACTION_FILTER.equals(mode)) {
            Assert.isTrue(StringUtils.hasText(samplingCode), "质检失败: 无效过滤抽样参数.");
            param.setSatisfactionItem(new ArrayList<>(Arrays.asList(samplingCode.split(","))));
            if (samplingCode.contains("not")) {
                param.setIncludeNotEvaluate(1);
            }
        }
        // param.setRange(5);
        param.setMode(mode);

        if(platForm == null || !"2".equals(platForm)){
            // 质检目标对象
            if (QUALITY_TARGET_SKILL.equals(target)) {
                final String skills = scoringRule.getScoringSkillIds();
                Assert.isTrue(StringUtils.hasText(skills), "质检失败: 质检评分规则中未指定对应的评分技能组.");
                param.setSkillString(skills);
                param.setSkills(Stream.of(skills.split(",")).map(Long::valueOf).collect(Collectors.toList()));
            } else if (QUALITY_TARGET_AGENT.equals(target)) {
                final String agents = scoringRule.getScoringPeople();
                Assert.isTrue(StringUtils.hasText(agents), "质检失败: 质检评分规则中未指定对应的客服人员.");
                param.setAgents(Stream.of(agents.split(",")).map(Long::valueOf).collect(Collectors.toList()));
            }
        }

        param.setTarget(target);
        param.setDialogueNumber(samplingRule.getQualityRange());
        // param.setDialogueNumber(2);
        // 质检范围(时间)
        if (QUALITY_EXEC_ONCE.equals(samplingRule.getFrequencyCode())) {
            param.setStartTime(samplingRule.getRecordStartTime());
            param.setEndTime(samplingRule.getRecordEndTime());
        } else {
            final LocalDateTime[] times = calculateTime(samplingRule.getFrequencyCode());
            param.setStartTime(toDate(times[0]));
            param.setEndTime(toDate(times[1]));
        }

        return param;
    }
    
    private Date toDate(final LocalDateTime dateTime) {
        return Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 根据质检时间范围计算相对应的开始、结束时间
     * @param range 质检时间范围
     * @return 开始时间和结束时间
     */
    private LocalDateTime[] calculateTime(final String range) {
        final LocalDateTime[] times = new LocalDateTime[2];
        final int duration = QUALITY_EXEC_OF_DAY.equals(range) ? 1 : QUALITY_EXEC_OF_WEEK.equals(range) ? 7 : 30;
        // 获取当前日期(yyyy-MM-dd 00:00:00)
        final LocalDateTime currentTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        // 测试用
        /*final LocalDate date = LocalDate.of(2020, 9, 27);
        final LocalDateTime currentTime = LocalDateTime.of(date, LocalTime.MIN);*/
        /*final LocalDate date = LocalDate.of(2020, 10, 27);
        final LocalDateTime currentTime = LocalDateTime.of(date, LocalTime.MIN);*/
        if (duration == 1 || duration == 7) {
            // 昨天或7天前的时间
            times[0] = currentTime.plusDays(-duration);
            times[1] = currentTime;
        } else {
            // 上个月的开始时间和当前月的开始时间
            times[0] = currentTime.plusMonths(-1).with(TemporalAdjusters.firstDayOfMonth());
            times[1] = currentTime.with(TemporalAdjusters.firstDayOfMonth());
        }
        return times;
    }

    /**
     * 查询记录
     * @param param {@link QualityScanParam}
     * @return {@link QualityCallInfoVo}
     */
    private List<QualityCallInfoVo> loadCallRecord(final QualityScanParam param) {
        return callcenterRecordDetailService.selectQualityCallInfos(param);
    }
}
