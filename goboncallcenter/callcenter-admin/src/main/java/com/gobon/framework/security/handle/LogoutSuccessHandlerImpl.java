package com.gobon.framework.security.handle;

import com.alibaba.fastjson.JSON;
import com.gobon.common.constant.Constants;
import com.gobon.common.constant.HttpStatus;
import com.gobon.common.utils.ServletUtils;
import com.gobon.common.utils.StringUtils;
import com.gobon.framework.redis.RedisCache;
import com.gobon.framework.security.LoginUser;
import com.gobon.framework.security.service.TokenService;
import com.gobon.framework.web.domain.AjaxResult;
import com.gobon.project.system.domain.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 自定义退出处理类 返回成功
 *
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class LogoutSuccessHandlerImpl implements LogoutSuccessHandler {

    @Autowired
    private TokenService tokenService;

    @Autowired
    private RedisCache redisCache;


    /**
     * 退出处理
     *
     * @return
     */
    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication)
            throws IOException, ServletException {

      /*  LoginUser loginUser = tokenService.getLoginUser(request);
        // 更新服务状态
        if (loginUser != null) {
            SysUser user = loginUser.getUser();
            String addrArea = user.getAddrArea();
            if(StringUtils.isNotEmpty(addrArea)) {
                String key = String.format(Constants.CALL_USERS_KEY,addrArea,user.getUserId());
                try {
                    redisCache.deleteObject(key);
                } catch (Exception e) {
                    log.error("*******删除CALL_USERS_KEY失败*******",e);
                }
            }
        }*/

//        if (StringUtils.isNotNull(loginUser))
//        {
//            String userName = loginUser.getUsername();
//            // 删除用户缓存记录
//            tokenService.delLoginUser(loginUser.getToken());
//            // 得到用户登入时的sessionID
////            String sessionId = redisCache.getCacheObject(Constants.LOGIN_SESSIONID);
////            redisCache.deleteObject(Constants.LOGIN_SESSIONID);
//            // 记录用户退出日志
//            AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginUser.getToken(),userName, Constants.LOGOUT, "退出成功"));
//        }
        ServletUtils.renderString(response, JSON.toJSONString(AjaxResult.error(HttpStatus.SUCCESS, "退出成功")));
    }
}
