package com.gobon.common.utils;

import com.gobon.common.constant.Constants;
import com.gobon.common.constant.HttpStatus;
import com.gobon.common.exception.CustomException;
import com.gobon.common.spring.SpringUtils;
import com.gobon.framework.redis.RedisCache;
import com.gobon.framework.security.LoginUser;
import com.gobon.framework.security.service.TokenService;
import com.gobon.project.callcenter.domain.CustomerServiceInfo;
import com.gobon.project.system.domain.SysUser;
import com.gobon.project.tool.mobile.RegionApi;
import com.gobon.project.tool.mobile.entity.MobileRegion;
import com.google.common.base.Strings;

import java.util.*;

import static java.util.stream.Collectors.toMap;

/**
 * 公共类
 *
 * <AUTHOR>
 * @date 2020/3/24
 */
public class CommonUtils {

    private CommonUtils() {
    }

    private static volatile CommonUtils instance;

    public static CommonUtils getInstance() {
        if (instance == null) {
            synchronized (CommonUtils.class) {
                if (instance == null) {
                    instance = new CommonUtils();
                }
            }
        }
        return instance;
    }

    /**
     * 获取登录用户所属域
     */
    public String getDomain() {
        try {
            return this.getLoginUser().getUser().getAddrArea();
        } catch (Exception e) {
            throw new CustomException("获取登录用户信息异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取登录用户信息
     */
    public LoginUser getLoginUser() {
        try {
            return SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
        } catch (Exception e) {
            e.printStackTrace();
            throw new CustomException("获取登录用户信息异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取登录用户信息
     */
    public LoginUser getLoginUser(String token) {
        try {
            return SpringUtils.getBean(TokenService.class).getLoginUser(token);
        } catch (Exception e) {
            e.printStackTrace();
            throw new CustomException("获取登录用户信息异常:token" + token, HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取用户具体信息
     */
    public SysUser getSysUser() {
        try {
            return this.getLoginUser().getUser();
        } catch (Exception e) {
            e.printStackTrace();
            throw new CustomException("获取用户具体信息异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取用户具体信息
     */
    public SysUser getSysUser(String token) {
        try {
            return this.getLoginUser(token).getUser();
        } catch (Exception e) {
            e.printStackTrace();
            throw new CustomException("用户已退出,redis token已失效 获取不到用户信息，token" + token, HttpStatus.UNAUTHORIZED);
        }
    }

    public Map<String, String> getQueryString(String queryString) {
        if (Strings.isNullOrEmpty(queryString)) {
            return Collections.emptyMap();
        }
        return Arrays.stream(queryString.split("&"))
                .map(this::splitQueryParameter).collect(toMap(AbstractMap.SimpleImmutableEntry::getKey, AbstractMap.SimpleImmutableEntry::getValue));
    }

    public AbstractMap.SimpleImmutableEntry<String, String> splitQueryParameter(String it) {
        final int idx = it.indexOf("=");
        final String key = idx > 0 ? it.substring(0, idx) : it;
        final String value = idx > 0 && it.length() > idx + 1 ? it.substring(idx + 1) : null;
        return new AbstractMap.SimpleImmutableEntry<>(key, value);
    }

    /**
     * 根据使用域和用户id获取callUser
     *
     * @param addrArea
     * @param userId
     * @return
     */
    public CustomerServiceInfo getCustomerServiceByAddrAreaAndUserId(String addrArea, Long userId) {
        if (StringUtils.isEmpty(addrArea) || userId == null) {
            return null;
        }
        String key = String.format(Constants.CUSTOMER_SERVICE_INFO, addrArea, userId);
        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        return redisCache.getCacheObject(key);
    }

    /**
     * 补零 电话号码
     */
    public String getRealPhone(String userPhone) {
        String realPhone = "";
        if (org.apache.commons.lang3.StringUtils.isEmpty(userPhone)) {
            return realPhone;
        }
        if (userPhone.length() == 4) {
            return realPhone;
        }

        if (userPhone.length() >= 11) {
            MobileRegion mobileRegion = RegionApi.getMobileRegionFromLocalDat(userPhone);
            if (mobileRegion != null) {
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(mobileRegion.getRealMobile())) {
                    realPhone = mobileRegion.getRealMobile().substring(1);
                }
            }
        } else {
            realPhone = userPhone;
        }
        return StringUtils.isEmpty(realPhone) ? userPhone : realPhone;
    }

    public static String getRandomStr() {
        Random random = new Random();
        return String.format("%04d", random.nextInt(10000));
    }

}
