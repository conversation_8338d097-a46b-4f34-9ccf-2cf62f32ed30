package com.gobon.project.ivrnavigation.mapper;

import com.gobon.project.ivrnavigation.domain.SysNavigationChilder;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *  导航子节点Mapper接口
 * 
 * <AUTHOR>
 * @date 2020-04-14
 */
public interface SysNavigationChilderMapper 
{
    /**
     * 查询导航节点
     *
     * @param id 导航节点ID
     * @return 导航节点
     */
    public SysNavigationChilder selectSysNavigationChilderById(String id);

    /**
     * 查询导航节点列表
     *
     * @param sysNavigationChilder 导航节点
     * @return 导航节点集合
     */
    public List<SysNavigationChilder> selectSysNavigationChilderList(SysNavigationChilder sysNavigationChilder);

    /**
     * 新增导航节点
     *
     * @param sysNavigationChilder 导航节点
     * @return 结果
     */
    public int insertSysNavigationChilder(SysNavigationChilder sysNavigationChilder);

    /**
     * 修改导航节点
     *
     * @param sysNavigationChilder 导航节点
     * @return 结果
     */
    public int updateSysNavigationChilder(SysNavigationChilder sysNavigationChilder);

    /**
     * 删除导航节点
     *
     * @param id 导航节点ID
     * @return 结果
     */
    public int deleteSysNavigationChilderById(String id);

    /**
     * 批量删除导航节点
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSysNavigationChilderByIds(String[] ids);


    /**
     * 查询该导航的按键key(不允许重复)
     */
    List<String>  getNavigationKeys(@Param("parentId") Long parentId);
}
