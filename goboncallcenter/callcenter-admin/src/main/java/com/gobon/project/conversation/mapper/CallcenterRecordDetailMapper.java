package com.gobon.project.conversation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gobon.framework.aspectj.lang.annotation.DataSource;
import com.gobon.framework.aspectj.lang.enums.DataSourceType;
import com.gobon.project.conversation.domain.*;
import com.gobon.project.conversation.domain.param.*;
import com.gobon.project.conversation.domain.vo.*;
import com.gobon.project.customer.domain.param.VisitorParam;
import com.gobon.project.intelligencequality.domain.param.QualityScanParam;
import com.gobon.project.intelligencequality.domain.vo.QualityCallInfoVo;
import com.gobon.project.intelligencequality.domain.vo.QualityCallSoundVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 呼入呼出通话记录明细Mapper接口
 *
 * <AUTHOR>
 * @date 2020-03-23
 */
@Mapper
public interface CallcenterRecordDetailMapper extends BaseMapper<CallcenterRecordDetailEntity>
{
    /**
     * 查询呼入呼出通话记录明细
     *
     * @param id 呼入呼出通话记录明细ID
     * @return 呼入呼出通话记录明细
     */
    public CallcenterRecordDetailVO selectCallcenterRecordDetailById(Long id);

    /**
     * api查询呼入呼出通话记录明细
     *
     * @param id 通话id
     * @return 呼入呼出通话记录明细
     */
    CallRecordDetailslApiVO apiSelectCallcenterRecordDetailById(Long id);

    /**
     * 查询呼入呼出通话记录明细
     *
     * @param id 呼入呼出通话记录明细ID
     * @return 呼入呼出通话记录明细
     */
    public CallcenterRecordDetailVO selectManagelById(Long id);

    /**
     * 根据CallId查询呼入呼出通话记录明细
     *
     * @param callId freeswitch_CALL_ID
     * @param callPhone 主叫
     * @param userPhone 被叫
     * @return 呼入呼出通话记录明细
     */
    public CallcenterRecordDetail selectCallcenterRecordByFreeswitchCallId(@Param("callId") String callId, @Param("callPhone") String callPhone, @Param("userPhone") String userPhone);

    /**
     * 查询呼入呼出通话记录明细
     *
     * @param traceId 通道ID
     * @return 呼入呼出通话记录明细
     */
    public CallcenterRecordDetail selectCallcenterRecordByTraceId(@Param("traceId") String traceId);

    /**
     * 查询呼入呼出通话记录明细（最近一条）
     *
     * @param traceId 通道ID
     * @return 呼入呼出通话记录明细
     */
    public CallcenterRecordDetail selectCallcenterRecordLastByTraceId(@Param("traceId") String traceId);

    /**
     * 查询呼入呼出通话记录明细（最近一条） 无多方求助
     *
     * @param traceId 通道ID
     * @return 呼入呼出通话记录明细
     */
    public CallcenterRecordDetail selectCallcenterRecordLastNoPartyByTraceId(@Param("traceId") String traceId);

    /**
     * 查询呼入呼出监听通话记录明细（最近一条）
     *
     * @param traceId 通道ID
     * @return 呼入呼出通话记录明细
     */
    public CallcenterRecordDetail selectCallcenterRecordLastMonitorByTraceId(@Param("traceId") String traceId);

    /**
     * 查询呼入呼出通话记录明细
     *
     * @param freeswitchDetailId freeswitch 每一通电话唯一ID
     * @return 呼入呼出通话记录明细
     */
    public CallcenterRecordDetail selectCallcenterRecordByFreeswitchDetailId(@Param("freeswitchDetailId") String freeswitchDetailId);

    /**
     * 查询呼入呼出通话记录明细-一对一
     *
     * @param traceId freeswitch 每一通电话唯一ID
     * @return 呼入呼出通话记录明细
     */
    public CallcenterRecordDetail selectCallcenterRecordByFreeswitchTraceId(@Param("traceId") String traceId);

    /**
     * 查询呼入呼出通话记录明细
     *
     * @param id 通话记录明细ID
     * @return 呼入呼出通话记录明细
     */
    public CallcenterRecordDetail selectCallcenterRecordById(Long id);

    /**
     * 查询通话记录集合
     *
     * @param param
     * @return 通话记录集合
     */
    public List<CallcenterRecordDetailVO> selectCallcenterListByPhone(CallcenterRecordDetailListParam param);

    /**
     * 查询待提交通话记录集合
     *
     * @param
     * @return 查询待提交通话记录集合
     */
    public List<StaySubmitListVO> getStayList(CallcenterRecordDetailListParam param);

    /**
     * 根据 主通话id 查询的所有通话记录
     *
     * @param
     * @return 通话记录-多人通话信息VO
     */
    public List<CallcenterMultipartyVO> getCallcenterMultipartyVO(CallcenterMultipartyParam param);

    /**
     * 根据freeswitchId 查询的所有通话记录
     *
     * @param
     * @return 通话记录-多人通话信息VO
     */
    public List<CallcenterMultipartyVO> getCallcenterMultipartyVOByMultipartyId(CallcenterMultipartyParam param);

    /**
     * 根据 主通话id 查询的所有通话录音（会议只保留一条）
     *
     * @param
     * @return 通话记录-多人通话录音VO
     */
    public List<SoundVO> getSoundVO(CallcenterMultipartyParam param);

    /**
     * 查询子通话记录（转接, 拦截, 强拆）相关信息
     *
     * @param
     * @return 通话记录-转接、拦截、监听 介入对象VO
     */
    public RelationVO getRelationVO(CallcenterMultipartyParam param);

    /**
     * 管理端 查询通话记录列表
     *
     * @param param 通话记录入参
     * @return 通话记录集合
     */
    public List<CallcenterRecordDetailVO> selectManagelList(CallcenterRecordDetailListParam param);

    /**
     * 根据电话号码查询通话记录
     *
     * @param phoneNumber 电话号码
     * @return 通话记录集合
     */
    public List<CallcenterRecordDetailVO> selectRecordListByPhone(String phoneNumber);

    /**
     * 查询坐席通话记录
     *
     * @param param 通话记录入参
     * @return
     */
    public List<CallcenterRecordDetailSimpleVO> selectSeatsList(CallcenterRecordDetailListParam param);

    /**
     * 呼损导出
     *
     * @param param 通话记录入参
     * @return
     */
    List<CallcenterRecordDetailHsVO> selectSeatsListHs(CallcenterRecordDetailListParam param);


    /**
     * 查询坐席通话记录-不满意
     *
     * @param param 通话记录入参
     * @return
     */
    List<CallcenterRecordDetailNotGoodVO> selectSeatsListNotGood(CallcenterRecordDetailListParam param);

    /**
     * 查询监听列表
     *
     * @param param 监听列表入参
     * @return 通话/呼入中列表VO
     */
    public List<MonitorConversationVO> getMonitorList(MonitorParam param);

    /**
     * 新增呼入呼出通话记录明细
     *
     * @param callcenterRecordDetail 呼入呼出通话记录明细
     * @return 结果
     */
    public int insertCallcenterRecordDetail(CallcenterRecordDetail callcenterRecordDetail);

    /**
     * 新增呼入呼出通话记录明细-异常
     *
     * @param callcenterRecordDetail 呼入呼出通话记录明细
     * @return 结果
     */
    void insertAbnormalCallcenterRecordDetail(CallcenterRecordDetailEntity callcenterRecordDetail);

    /**
     * 修改呼入呼出通话记录明细（监听）
     *
     * @param callcenterRecordDetail 呼入呼出通话记录明细
     * @return 结果
     */
    public int updateCallcenterRecordDetailMonitor(CallcenterRecordDetail callcenterRecordDetail);

    /**
     * 修改呼入呼出通话记录明细
     *
     * @param callcenterRecordDetail 呼入呼出通话记录明细
     * @return 结果
     */
    public int updateCallcenterRecordDetail(CallcenterRecordDetail callcenterRecordDetail);

    /**
     * 新增用户时所有当前号码的通话记录添加联系人信息
     *
     * @param param 添加联系人信息参数
     * @return 结果
     */
    public int addCustomer(CustomerParam param);

    /**
     * 修改用户时所有当前号码的通话记录添加联系人信息
     *
     * @param param 联系人信息参数
     * @return 结果
     */
    public int updateCustomer(CustomerParam param);

    /**
     * 获取个人统计
     *
     * @param
     * @return 结果
     */
    public ConversationStatisticsVO getPersonalStatistics(StatisticsParam param);

    /**
     * 获取管理端首页统计
     *
     * @param
     * @return 结果
     */
    public ConversationStatisticsVO getManageStatistics(StatisticsParam param);

    /**
     * 获取咨询语种统计
     *
     * @param
     * @return 结果
     */
    public List<ConsultLanguageStatisticsVO> getConsultLanguageStatistics(StatisticsParam param);

    /**
     * 根据id更新录音文字信息
     * @param id
     * @param text
     * @return
     */
    int updateSoundText ( @Param( "id" ) Long id, @Param( "text" ) String text );

    /**
     * 根据主通话记录id查询最新的通话详情记录并用客服电话分组
     * @param recordId
     * @param multipartyId
     * @return
     */
    List<CallcenterRecordDetail> selectNewCallcenterRecordDetailByRecordIdAndMultipartyId (Long recordId, Long multipartyId);



    /**
     * 根据明细id获取主表记录
     * @param detailId
     * @return
     */
    CallcenterRecord getByDetailId( Long detailId );


    /**
     * 根据通道id获取通话信息
     *
     * @param traceId 通道id
     * @return 客户信息
     */
    CallcenterRecordDetail selectDetailByTraceId( String traceId );

    /**
     * 根据通话主记录id和类型查询通话详情记录
     * @param recordId
     * @param type
     * @return
     */
    List<CallcenterRecordDetail> selectCallcenterRecordDetailByRecordIdAndType(Long recordId, int type);

    /**
     * 根据TraceId查询通话详情记录
     * @param traceId
     * @return
     */
    List<CallcenterRecordDetail> selectCallcenterRecordDetailListByTraceId(String traceId);

    /**
     * 根据通道id 和用户按下评价数字更新所有记录的评价
     * @param recordId
     * @param satisfId
     * @param statisfName
     */
    int updateStatisfiedByRecordId( @Param( "recordId" ) Long recordId, @Param( "satisfId" ) Long satisfId,
                                   @Param( "statisfName" ) String statisfName, @Param( "statisfCode" ) String statisfCode);

    /**
     *获取客服访客量
     *
     * @return 结果
     */
    String getVisitor(VisitorParam param);

    /**
     * 通话明细记录进入邀评
     * @param traceId 通道ID
     */
    int updateSatisfStateByTraceId(@Param( "traceId" ) String traceId);

    /**
     * 通话明细记录进入邀评
     * @param recordId 主记录id
     */
    int updateSatisfStateByRecordId(@Param( "recordId" )  Long recordId);

    /**
     * 通话明细记录进入邀评
     * @param id 主记录id
     */
    int updateSatisfStateById(@Param( "id" )  Long id);

    /**
     * 根据recordId查询通话详情记录
     * @param recordId
     * @return
     */
    CallcenterRecordDetail selectCallcenterRecordDetailListByRecordIdOrderById(Long recordId);

    /**
     * 插入 录音分割
     * @param callcenterRecordDetailSound 呼入呼出通话记录明细对象分割录音
     * @return
     */
    int insertCallcenterRecordDetailSound(CallcenterRecordDetailSound callcenterRecordDetailSound);

    /**
     * 插入 录音转换结果
     * @param item
     * @return
     */
    int insertCallcenterRecordDetailSoundAsrResult(CallcenterRecordDetailSoundAsrresultEntity item);

    /**
     * 修改 录音分割
     * @param callcenterRecordDetailSound 呼入呼出通话记录明细对象分割录音
     * @return
     */
    int updateCallcenterRecordDetailSound(CallcenterRecordDetailSound callcenterRecordDetailSound);

    /**
     * 根据 通话记录明细id 查询 录音分割
     * @param callRecordDetailId 呼入呼出通话记录明细id
     * @return
     */
    @DataSource(value = DataSourceType.SOUND)
    List<CallcenterRecordDetailSound> selectCallcenterRecordDetailSoundByCallRecordDetailId(Long callRecordDetailId);

    /**
     * 根据主通话id查询所有通话详情
     * @param recordId
     * @return
     */
    List<CallcenterRecordDetail> selectCallcenterRecordDetailListByRecordId(Long recordId);

    /**
     * 根据主记录id查询最新一条详情
     * @param recordId
     * @return
     */
    CallcenterRecordDetail selectCallcenterRecordDetailByRecordId(Long recordId);

    /**
     * 查询接听的 相应数据
     * @param param
     * @return
     */
    List<AudioHandleVO> selectNeededData(PyParam param);

    /**
     * 更新录音转文字状态
     * @param id
     * @param state
     * @return
     */
    int updateSountTextState( @Param( "id" ) Long id, @Param( "state" ) Integer state );

    /**
     * 查询待智能质检列表
     * @param param {@link QualityScanParam}
     * @return {@link QualityScanParam}列表
     */
    List<QualityCallInfoVo> selectQualityCallInfos(@Param("param") QualityScanParam param);

    /**
     * 查看录音详情
     * @param id 通话记录详情ID
     * @return {@link QualityCallSoundVo}列表
     */
    @DataSource(value = DataSourceType.SOUND)
    List<QualityCallSoundVo> selectQualityCallSound(@Param("callDetailId") Long id);


    /**
     * 简单查询通话记录集合
     * @return 通话记录集合
     */
    public List<CallcenterRecordDetailVO> selectList(CallcenterRecordDetailListParam param);

    /**
     * 查询录音分割没有角色区分的通话记录集合
     * @return 通话记录集合
     */
    public List<CallcenterRecordDetailVO> selectNoConversationRoleList();

    /**
     * 记录异常原因
     * @param param
     */
    void recordAbnormalCauses(AbnormalCausesParam param);

    /**
     * 根据通话id获取通话详情集合
     * @param param
     */
    List<CallRecordDetailslSimpleVO> selectDetailListByRecordId( CallcenterRecordDetailListParam param );


    /**
     * 获取通话详情
     * @param id
     * @return
     */
    CallRecordDetailslSimpleVO getRecordDetailById(Long id);

    /**
     * 根据 客户号码 获取客户相关统计
     * @param
     */
    CustomerStatisticsVO getCustomerStatistics(String phone );

    /**
     * 更新五天前的记录为满意
     * @return
     */
    Integer updateDefaultPraise();

    /**
     * 查询五天前未评价的记录
     * @return
     */
    List<CallcenterRecordDetailVO> getDefaultPraiseList();

    /**
     * 查询五天前未评价的记录
     * @return
     */
    CallcenterRecordDetailVO getLastRecord(String traceId);

    /**
     * 根据call_record_detail_id 删除语音转文字记录
     * @param detailId
     * @return
     */
    @DataSource(value = DataSourceType.SOUND)
    int deleteSoundsByDetailId(@Param("detailId") Long detailId);

    /**
     *  根据call_record_detail_id 删除语音转文字结果
     * @param detailId
     * @return
     */
    int deleteSoundAsrResultByDetailId(@Param("detailId") Long detailId);

    /**
     * 更新一对一整体挂断方
     * @param traceId
     * @param endPerson
     * @return
     */
    int updateDetailRealHangup(@Param("traceId") String traceId, @Param("endPerson") Integer endPerson);

    /**
     *
     */
    List<CallcenterRecordDetailEntity> getNewAsrDetailList(LocalDateTime startDate, LocalDateTime endDate);


    List<CallcenterRecordDetailEntity> getNoMarkDetailList(LocalDateTime startDate, LocalDateTime endDate);
    /**
     * 根据通话详情获取所有通过详情
     * @param detailId
     * @return
     */
    List<CallcenterRecordDetail> selectAllCallcenterRecordDetailByDetailId(@Param("detailId") Long detailId);

    List<CallcenterRecordDetailEntity> selectRecordDetailData(@Param("limit") Integer limit, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    List<CallcenterRecordDetailEntity> selectBatchById(@Param("ids") Long[] ids);
}
