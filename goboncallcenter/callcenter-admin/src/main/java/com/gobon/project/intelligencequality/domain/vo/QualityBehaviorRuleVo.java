package com.gobon.project.intelligencequality.domain.vo;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.gobon.common.constant.LLMConstants;
import com.gobon.common.utils.LLMApiUtil;
import com.gobon.project.intelligencequality.executor.QualityScoringContext;
import com.gobon.project.intelligencequality.service.ICallcenterQualityScoringRuleService;
import com.gobon.project.intelligencequality.service.impl.CallcenterQualityScoringRuleServiceImpl;
import com.gobon.project.system.service.ISysConfigService;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class QualityBehaviorRuleVo implements Serializable {

    private static final long serialVersionUID = -5109137698347396279L;
    private static final Pattern PATTERN_INTEGER = Pattern.compile("^[1-9]\\d*$");


    /**
     * ID
     */
    private Long id;
    /**
     * 值
     */
    private String ruleValue;
    /**
     * 值
     */
    private Integer[] ruleValues;
    /**
     * 相关词语(欢迎语/结束语)
     */
    private List<QualityBehaviorRuleItemVo> behaviorRuleItems;

    private ICallcenterQualityScoringRuleService callcenterQualityScoringRuleService;

    public QualityBehaviorRuleVo parse() {
        if (this.ruleValue != null && !this.ruleValue.isBlank()) {
            try {
                this.ruleValues = Arrays.stream(this.ruleValue.split(","))
                        .filter(it -> PATTERN_INTEGER.matcher(it).matches())
                        .map(Integer::valueOf).toArray(Integer[]::new);
            } catch (Exception ignore) {
                // ignore
            }
        }
        return this;
    }

    /**
     * 检查指定字符串集合是否在欢迎语/结束语中，包含相似语句
     *
     * @param items 指定字符串集合
     * @return boolean
     */
    public boolean matcher(final List<String> items) {
        StringBuilder callRecords = new StringBuilder();

        if (items != null && !items.isEmpty()) {
            items.forEach(it -> callRecords.append("- ").append(it).append("\n"));
        }

        StringBuilder welcome = new StringBuilder();

        if (this.behaviorRuleItems != null && !this.behaviorRuleItems.isEmpty()) {
            this.behaviorRuleItems.forEach(it -> {
                welcome.append("- ").append(it.getRuleSentence()).append("\n");

                it.parse();
                if (it.getSimilitudeItems() != null && !it.getSimilitudeItems().isEmpty()) {
                    it.getSimilitudeItems().forEach(s -> welcome.append("- ").append(s).append("\n"));
                }
            });

        }

        String prompt = String.format(callcenterQualityScoringRuleService.getConfigPrompt(LLMConstants.LLM_QUALITY_KEYWORD_PROMPT), callRecords, welcome);
        //由于可能出现Ai判别问题 这边进行多次匹配提高准确率  单反有一次匹配上则成功
        Integer matchTimes = 0;
        Boolean result = false;
        do {
            matchTimes++;
            result = callcenterQualityScoringRuleService.matchMeaningRules(prompt).isMatch();
            if (result) {
                break;
            }
        } while (matchTimes < 3);
        return result;
    }
}
