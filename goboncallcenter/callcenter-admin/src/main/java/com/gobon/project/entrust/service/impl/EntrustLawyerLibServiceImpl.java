package com.gobon.project.entrust.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gobon.common.constant.SqlConstant;
import com.gobon.common.utils.SecurityUtils;
import com.gobon.common.utils.bean.BeanCopierUtils;
import com.gobon.framework.aspectj.lang.annotation.DataSource;
import com.gobon.framework.aspectj.lang.enums.DataSourceType;
import com.gobon.framework.datasource.DynamicDataSourceContextHolder;
import com.gobon.framework.redis.RedisCache;
import com.gobon.project.entrust.domain.EntrustCallback;
import com.gobon.project.entrust.domain.EntrustLawyerLib;
import com.gobon.project.entrust.domain.EntrustLawyerLibBusiness;
import com.gobon.project.entrust.domain.EntrustRegistration;
import com.gobon.project.entrust.domain.param.EntrustLawyerLibAddParam;
import com.gobon.project.entrust.domain.param.EntrustLawyerLibQueryParam;
import com.gobon.project.entrust.domain.param.EntrustLawyerLibUpdateParam;
import com.gobon.project.entrust.domain.vo.EntrustLawyerLibCustomerVO;
import com.gobon.project.entrust.domain.vo.EntrustLawyerLibVO;
import com.gobon.project.entrust.domain.vo.FwLawyerInfoVO;
import com.gobon.project.entrust.domain.vo.LawyerCallCountVO;
import com.gobon.project.entrust.mapper.EntrustLawyerLibMapper;
import com.gobon.project.entrust.service.*;
import com.gobon.project.report.service.IReportQueryService;
import com.gobon.project.system.domain.SysUser;
import com.gobon.project.system.domain.entity.SysUserEntity;
import com.gobon.project.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 委托服务律师资源库 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Service
@Slf4j
public class EntrustLawyerLibServiceImpl extends ServiceImpl<EntrustLawyerLibMapper, EntrustLawyerLib> implements IEntrustLawyerLibService {

    @Resource
    private ISysUserService userService;
    @Resource
    private FwOracleService fwOracleService;
    @Resource
    private IEntrustLawyerLibBusinessService entrustLawyerLibBusinessService;
    @Resource
    private IEntrustRegistrationService entrustRegistrationService;
    @Resource
    private IEntrustCallbackService entrustCallbackService;
    @Resource
    private RedisCache redisCache;
    @Resource
    @Lazy
    private IEntrustService entrustService;

    @Resource
    private IReportQueryService reportQueryService;

    @Override
    public List<EntrustLawyerLibVO> getList(EntrustLawyerLibQueryParam param) {
        List<EntrustLawyerLibVO> list = baseMapper.getList(param);
        for (EntrustLawyerLibVO entrustLawyerLibVO : list) {
            entrustLawyerLibVO.setBusinessTypeDesc(entrustService.getEntrustBusinessType(entrustLawyerLibVO.getBusinessType()));
        }
        return list;
    }

    @Override
    public EntrustLawyerLibVO getDetailById(Long id) {
        EntrustLawyerLibVO result = new EntrustLawyerLibVO();
        EntrustLawyerLib byId = getById(id);
        BeanCopierUtils.copyProperties(byId, result);
        List<EntrustLawyerLibBusiness> list = entrustLawyerLibBusinessService.list(Wrappers.<EntrustLawyerLibBusiness>lambdaQuery().eq(EntrustLawyerLibBusiness::getUserId, byId.getUserId()));
        if (CollectionUtils.isNotEmpty(list)) {
            result.setBusinessTypeList(list.stream().map(EntrustLawyerLibBusiness::getBusinessType).collect(Collectors.toList()));
        }
        result.setAvatar(userService.getById(result.getUserId()).getAvatar());
        return result;
    }

    @Override
    public EntrustLawyerLibVO getDetailByUserId(Long userId) {
        EntrustLawyerLibVO result = new EntrustLawyerLibVO();
        EntrustLawyerLib byId = lambdaQuery().eq(EntrustLawyerLib::getUserId, userId).eq(EntrustLawyerLib::getDelFlag, SqlConstant.NOT_DELETE_FLAG).last(SqlConstant.SQL_GETONE).one();
        BeanCopierUtils.copyProperties(byId, result);
        List<EntrustLawyerLibBusiness> list = entrustLawyerLibBusinessService.list(Wrappers.<EntrustLawyerLibBusiness>lambdaQuery().eq(EntrustLawyerLibBusiness::getUserId, byId.getUserId()));
        if (CollectionUtils.isNotEmpty(list)) {
            List<String> lawyerBusiness = new ArrayList<>();
            for (EntrustLawyerLibBusiness entrustLawyerLibBusiness : list) {
                lawyerBusiness.add(entrustService.getEntrustBusinessType(entrustLawyerLibBusiness.getBusinessType()));
            }
            result.setBusinessTypeList(lawyerBusiness);
        }
        result.setAvatar(userService.getById(result.getUserId()).getAvatar());
        return result;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void add(EntrustLawyerLibAddParam param) {
        if (CollectionUtils.isEmpty(param.getAddLibUserIds())) {
            return;
        }
        for (Long addLibUserId : param.getAddLibUserIds()) {
            String addLibUserIdStr = "add_entrust_lib:" + addLibUserId;
            if (redisCache.getCacheObject("add_entrust_lib:") != null) {
                continue;
            }
            redisCache.setCacheObject(addLibUserIdStr, "1", 1, TimeUnit.MINUTES);
            Integer count = lambdaQuery().eq(EntrustLawyerLib::getUserId, addLibUserId).eq(EntrustLawyerLib::getDelFlag, SqlConstant.NOT_DELETE_FLAG).count();
            if (count > 0) {
                continue;
            }
            EntrustLawyerLib lawyerLib = new EntrustLawyerLib();
            SysUserEntity userEntity = userService.getById(addLibUserId);
            lawyerLib.setUserId(userEntity.getUserId());
            lawyerLib.setUserNumber(userEntity.getUserNumber());
            lawyerLib.setHwNickName(userEntity.getNickName());
            lawyerLib.setUserStatus(userEntity.getStatus());
            if (StringUtils.isNotBlank(userEntity.getIdCard())) {
                FwLawyerInfoVO fwLawyerInfoByIdCard = fwOracleService.getFwLawyerInfoByIdCard(reportQueryService.getQueryByName("fw_get_lawyer_info"), userEntity.getIdCard());
                if (fwLawyerInfoByIdCard != null) {
                    lawyerLib.setNickName(fwLawyerInfoByIdCard.getUserName());
                    lawyerLib.setSex(fwLawyerInfoByIdCard.getSex());
                    lawyerLib.setPhoneNumber(fwLawyerInfoByIdCard.getMobile());
                    lawyerLib.setFwUserId(fwLawyerInfoByIdCard.getId());
                    lawyerLib.setLicenseNumber(fwLawyerInfoByIdCard.getPracticeNumber());
                    lawyerLib.setWorkPlace(fwLawyerInfoByIdCard.getPracticeCity());
                    if (StringUtils.isNotBlank(fwLawyerInfoByIdCard.getPracticeYears())) {
                        lawyerLib.setWorkYear(Integer.valueOf(fwLawyerInfoByIdCard.getPracticeYears()));
                    }
                    lawyerLib.setWorkOrg(fwLawyerInfoByIdCard.getPracticeOrgName());
                }
            } else {
                lawyerLib.setNickName(userEntity.getNickName());
            }
            setUserCallCount(lawyerLib);
            lawyerLib.setCreateTime(new Date());
            lawyerLib.setCreateByName(SecurityUtils.getNickNameAndNumber());
            lawyerLib.setCreateById(SecurityUtils.getUserId());
            lawyerLib.setDelFlag(SqlConstant.NOT_DELETE_FLAG);
            setScore(lawyerLib);
            lawyerLib.setScore(BigDecimal.valueOf(lawyerLib.getTotalScore() / 20.0).setScale(1, RoundingMode.FLOOR));
            save(lawyerLib);
        }
        // 设置排名
        rankLawyer();
    }

    @Override
    public List<SysUser> getCanAddUser(SysUser user) {
        return baseMapper.getCanAddUser(user);
    }

    @Override
    public void update(EntrustLawyerLibUpdateParam param) {
        EntrustLawyerLib lawyerLib = new EntrustLawyerLib();
        EntrustLawyerLib byId = getById(param.getId());
        BeanCopierUtils.copyProperties(param, lawyerLib);
        lawyerLib.setUpdateTime(new Date());
        lawyerLib.setUpdateByName(SecurityUtils.getNickNameAndNumber());
        lawyerLib.setUpdateById(SecurityUtils.getUserId());
        lawyerLib.setUserId(byId.getUserId());
        setUserCallCount(lawyerLib);
        updateById(lawyerLib);
        EntrustLawyerLib newData = getById(lawyerLib.getId());
        setScore(newData);
        newData.setScore(BigDecimal.valueOf(newData.getTotalScore() / 20.0).setScale(1, RoundingMode.FLOOR));
        updateById(newData);

        // 更新用户业务类型
        entrustLawyerLibBusinessService.remove(Wrappers.<EntrustLawyerLibBusiness>lambdaQuery().eq(EntrustLawyerLibBusiness::getUserId, byId.getUserId()));
        if (CollectionUtils.isNotEmpty(param.getBusinessType())) {
            for (String businessId : param.getBusinessType()) {
                EntrustLawyerLibBusiness entrustLawyerLibBusiness = new EntrustLawyerLibBusiness();
                entrustLawyerLibBusiness.setBusinessType(businessId);
                entrustLawyerLibBusiness.setUserId(byId.getUserId());
                entrustLawyerLibBusinessService.save(entrustLawyerLibBusiness);
            }
        }

        // 设置排名
        rankLawyer();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long[] ids) {
        if (ArrayUtils.isNotEmpty(ids)) {
            for (Long id : ids) {
                EntrustLawyerLib byId = getById(id);
                byId.setDelFlag(SqlConstant.DELETE_FLAG);
                byId.setDelByName(SecurityUtils.getNickNameAndNumber());
                updateById(byId);
            }
        }
    }

    @Override
    public List<EntrustLawyerLibCustomerVO> getPushLawyerList(Long entrustId) {
        return baseMapper.getPushLawyerList(entrustId);
    }

    @Override
    public LawyerCallCountVO getLawyerCallCount(Long userId) {
        return baseMapper.getLawyerCallCount(userId);
    }

    @Override
    public BigDecimal getQualityAvgScore(Long userId) {
        return baseMapper.getQualityAvgScore(userId);
    }

    @Override
    public void countLawyerCallInfoTask() {
        lambdaQuery().eq(EntrustLawyerLib::getDelFlag, SqlConstant.NOT_DELETE_FLAG).list().forEach(lawyerLib -> {
            setUserCallCount(lawyerLib);
            setScore(lawyerLib);
            lawyerLib.setScore(BigDecimal.valueOf(lawyerLib.getTotalScore() / 20.0).setScale(1, RoundingMode.FLOOR));
            updateById(lawyerLib);
        });
        // 设置分值
        // 设置排名
        rankLawyer();
    }

    @Override
    public void syncFwLawyerInfoTask() {
        lambdaQuery().eq(EntrustLawyerLib::getDelFlag, SqlConstant.NOT_DELETE_FLAG).list().forEach(lawyerLib -> {
            if (StringUtils.isNotBlank(lawyerLib.getFwUserId())) {
                SysUserEntity byId = userService.getById(lawyerLib.getUserId());
                FwLawyerInfoVO fwLawyerInfoByIdCard = fwOracleService.getFwLawyerInfoByIdCard(reportQueryService.getQueryByName("fw_get_lawyer_info"), byId.getIdCard());
                if (fwLawyerInfoByIdCard!= null) {
                    lawyerLib.setNickName(fwLawyerInfoByIdCard.getUserName());
                    lawyerLib.setSex(fwLawyerInfoByIdCard.getSex());
                    lawyerLib.setPhoneNumber(fwLawyerInfoByIdCard.getMobile());
                    lawyerLib.setFwUserId(fwLawyerInfoByIdCard.getId());
                    lawyerLib.setLicenseNumber(fwLawyerInfoByIdCard.getPracticeNumber());
                    lawyerLib.setWorkPlace(fwLawyerInfoByIdCard.getPracticeCity());
                    if (fwLawyerInfoByIdCard.getPracticeYears() != null) {
                        lawyerLib.setWorkYear(Integer.valueOf(fwLawyerInfoByIdCard.getPracticeYears()));
                    }
                    lawyerLib.setWorkOrg(fwLawyerInfoByIdCard.getPracticeOrgName());
                    updateById(lawyerLib);
                }
            }
        });
    }

    @Override
    public void calcScore() {
        lambdaQuery().eq(EntrustLawyerLib::getDelFlag, SqlConstant.NOT_DELETE_FLAG).list().forEach(lawyerLib -> {
            setScore(lawyerLib);
            lawyerLib.setScore(BigDecimal.valueOf(lawyerLib.getTotalScore() / 20.0).setScale(1, RoundingMode.FLOOR));
            updateById(lawyerLib);
        });
        // 设置分值
        // 设置排名
        rankLawyer();
    }


    public void setUserCallCount(EntrustLawyerLib lawyerLib) {
        Integer userCount = entrustRegistrationService.lambdaQuery().eq(EntrustRegistration::getUserId, lawyerLib.getUserId()).count();
        if (userCount != 0) {
            Integer notGoodCount = entrustCallbackService.lambdaQuery().eq(EntrustCallback::getLawyerUserId, lawyerLib.getUserId())
                    .like(EntrustCallback::getEvaluate, "不满意").count();
            lawyerLib.setEntrustAcceptTotal(userCount);
            lawyerLib.setEntrustSatisfaction(new BigDecimal((userCount - notGoodCount)).divide(new BigDecimal(userCount), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
        } else {
            lawyerLib.setEntrustAcceptTotal(0);
            lawyerLib.setEntrustSatisfaction(new BigDecimal(0));
        }
//        Integer userMonthCount = entrustRegistrationService.lambdaQuery().eq(EntrustRegistration::getUserId, lawyerLib.getUserId())
//                .apply(" create_time > DATE_FORMAT(DATE_SUB(now(),INTERVAL 30 day), '%Y-%m-%d 00:00:00')").count();
//        if (userCount != 0) {
//            Integer monthNotGoodCount = entrustCallbackService.lambdaQuery().eq(EntrustCallback::getLawyerUserId, lawyerLib.getUserId())
//                    .like(EntrustCallback::getEvaluate, "不满意").apply(" create_time > DATE_FORMAT(DATE_SUB(now(),INTERVAL 30 day), '%Y-%m-%d 00:00:00')").count();
//            lawyerLib.setMonthEntrustSatisfaction(new BigDecimal((userMonthCount - monthNotGoodCount)).divide(new BigDecimal(userMonthCount), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
//        } else {
//            lawyerLib.setMonthEntrustSatisfaction(new BigDecimal(0));
//        }
        LawyerCallCountVO lawyerCallCount = getLawyerCallCount(lawyerLib.getUserId());
        lawyerLib.setAcceptTotal(lawyerLib.getEntrustAcceptTotal() + (lawyerLib.getPersonalAcceptTotal() == null ? 0: lawyerLib.getPersonalAcceptTotal()));
        if (lawyerCallCount == null) {
            lawyerLib.setConsultSatisfaction(new BigDecimal(0));
            lawyerLib.setMonthConsultSatisfaction(new BigDecimal(0));
            lawyerLib.setConsultTime(new BigDecimal(0));
            lawyerLib.setConsultTotal((lawyerLib.getVolunteerConsultTotal() == null ? 0: lawyerLib.getVolunteerConsultTotal()));
            lawyerLib.setMonthConsultTotal((lawyerLib.getMonthVolunteerConsultTotal()) == null ? 0: lawyerLib.getMonthVolunteerConsultTotal());
            lawyerLib.setMonthDutyConsultTotal(0);
            lawyerLib.setDutyConsultTotal(0);
        } else {
            lawyerLib.setConsultSatisfaction(lawyerCallCount.getGoodRate());
            lawyerLib.setMonthConsultSatisfaction(lawyerCallCount.getMonthGoodRate());
            lawyerLib.setConsultTime(lawyerCallCount.getAnswerCallTime());
            lawyerLib.setConsultTotal(lawyerCallCount.getAnswerCount() + (lawyerLib.getVolunteerConsultTotal() == null ? 0: lawyerLib.getVolunteerConsultTotal()));
            lawyerLib.setMonthConsultTotal(lawyerCallCount.getMonthAnswerCount() + (lawyerLib.getMonthVolunteerConsultTotal() == null ? 0: lawyerLib.getMonthVolunteerConsultTotal()));
            lawyerLib.setMonthDutyConsultTotal(lawyerCallCount.getMonthAnswerCount());
            lawyerLib.setDutyConsultTotal(lawyerCallCount.getAnswerCount());
        }
        lawyerLib.setMonthQualityScore(getQualityAvgScore(lawyerLib.getUserId()));
        System.out.println(lawyerLib);
    }


    /**
     * 排名
     */
    public void rankLawyer() {
        List<EntrustLawyerLib> list = lambdaQuery().eq(EntrustLawyerLib::getDelFlag, SqlConstant.NOT_DELETE_FLAG)
                .orderByDesc(EntrustLawyerLib::getTotalScore, EntrustLawyerLib::getMonthVolunteerConsultTotal, EntrustLawyerLib::getMonthDutyConsultTotal)
                .list();
        if (CollectionUtils.isNotEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                EntrustLawyerLib lawyerLib = list.get(i);
                lambdaUpdate().set(EntrustLawyerLib::getRankNumber, i + 1).eq(EntrustLawyerLib::getId, lawyerLib.getId()).update();
            }
        }
    }

    /**
     * 设置分数
     */
    @Override
    public void setScore(EntrustLawyerLib entrustLawyerLib) {
        Integer totalScore = 0;
        // 行业认证
        totalScore += Math.min((entrustLawyerLib.getIndustryCertification() == null ? 0: entrustLawyerLib.getIndustryCertification()), 5);
        log.info("{} 行业认证：{}", entrustLawyerLib.getNickName(), Math.min((entrustLawyerLib.getIndustryCertification() == null ? 0: entrustLawyerLib.getIndustryCertification()), 5));
        // 执业年限
        totalScore += (entrustLawyerLib.getWorkYear() == null ? 0: (entrustLawyerLib.getWorkYear() >= 5 ? 2 : 1));
        log.info("{} 执业年限：{}", entrustLawyerLib.getNickName(), (entrustLawyerLib.getWorkYear() == null ? 0: (entrustLawyerLib.getWorkYear() >= 5 ? 2 : 1)));
        // 个人承办案件量
        if (entrustLawyerLib.getPersonalAcceptTotal() != null) {
            if (entrustLawyerLib.getPersonalAcceptTotal() >= 15) {
                totalScore += 3;
                log.info("{} 个人承办案件量：{}", entrustLawyerLib.getNickName(), 3);
            } else if (entrustLawyerLib.getPersonalAcceptTotal() >= 10) {
                totalScore += 2;
                log.info("{} 个人承办案件量：{}", entrustLawyerLib.getNickName(), 2);
            } else if (entrustLawyerLib.getPersonalAcceptTotal() >= 5) {
                totalScore += 1;
                log.info("{} 个人承办案件量：{}", entrustLawyerLib.getNickName(), 1);
            }
        }
        // 有偿法律服务业务承办量
        if (entrustLawyerLib.getEntrustAcceptTotal() != null) {
            if (entrustLawyerLib.getEntrustAcceptTotal() >= 15) {
                totalScore += 5;
                log.info("{} 有偿法律服务业务承办量：{}", entrustLawyerLib.getNickName(), 5);
            } else if (entrustLawyerLib.getEntrustAcceptTotal() >= 12) {
                totalScore += 4;
                log.info("{} 有偿法律服务业务承办量：{}", entrustLawyerLib.getNickName(), 4);
            } else if (entrustLawyerLib.getEntrustAcceptTotal() >= 9) {
                totalScore += 3;
                log.info("{} 有偿法律服务业务承办量：{}", entrustLawyerLib.getNickName(), 3);
            } else if (entrustLawyerLib.getEntrustAcceptTotal() >= 6) {
                totalScore += 2;
                log.info("{} 有偿法律服务业务承办量：{}", entrustLawyerLib.getNickName(), 2);
            } else if (entrustLawyerLib.getEntrustAcceptTotal() >= 3) {
                totalScore += 1;
                log.info("{} 有偿法律服务业务承办量：{}", entrustLawyerLib.getNickName(), 1);
            }
        }
        Integer tempTotalScore = 0;
        // 月值班咨询服务
        if (entrustLawyerLib.getMonthDutyConsultTotal() != null) {
            BigDecimal rate = new BigDecimal(entrustLawyerLib.getMonthDutyConsultTotal());
            BigDecimal rateStan = new BigDecimal(50);
            BigDecimal bigDecimal = rate.divide(rateStan, 0, RoundingMode.FLOOR);
            tempTotalScore += Math.min(bigDecimal.intValue(), 45);
            log.info("{} 值班咨询服务：{}", entrustLawyerLib.getNickName(), Math.min(bigDecimal.intValue(), 45));
        } else {
            log.info("{} 值班咨询服务：{}", entrustLawyerLib.getNickName(), 0);
        }
        // 月志愿咨询服务量
        if (entrustLawyerLib.getMonthVolunteerConsultTotal() != null) {
            BigDecimal rate = new BigDecimal(entrustLawyerLib.getMonthVolunteerConsultTotal());
            BigDecimal rateStan = new BigDecimal(6);
            BigDecimal bigDecimal = rate.divide(rateStan, 0, RoundingMode.FLOOR);
            tempTotalScore += Math.min(bigDecimal.intValue(), 45);
            log.info("{} 志愿咨询服务量：{}", entrustLawyerLib.getNickName(), Math.min(bigDecimal.intValue(), 45));
        } else {
            log.info("{} 志愿咨询服务量：{}", entrustLawyerLib.getNickName(), 0);
        }
        log.info("{} 值班咨询服务 + 志愿咨询服务量：{}", entrustLawyerLib.getNickName(), Math.min(tempTotalScore, 45));
        totalScore += Math.min(tempTotalScore, 45);
        // 咨询满意度
        if (entrustLawyerLib.getMonthConsultSatisfaction() != null) {
            if (entrustLawyerLib.getMonthConsultSatisfaction().compareTo(new BigDecimal(95)) >= 0) {
                totalScore += 15;
                log.info("{} 咨询满意度：{}", entrustLawyerLib.getNickName(), 15);
            } else {
                totalScore += 15;
                BigDecimal rateStan = new BigDecimal(95);
                BigDecimal bigDecimal = rateStan.subtract(entrustLawyerLib.getMonthConsultSatisfaction()).setScale(0, RoundingMode.CEILING);
                totalScore -= Math.min(bigDecimal.intValue(), 15);
                log.info("{} 咨询满意度：{}", entrustLawyerLib.getNickName(), 15-Math.min(bigDecimal.intValue(), 15));
            }
        } else {
            log.info("{} 咨询满意度：{}", entrustLawyerLib.getNickName(), 0);
        }
        // 有偿法律服务满意度
        Integer userCount = entrustRegistrationService.lambdaQuery().eq(EntrustRegistration::getUserId, entrustLawyerLib.getUserId())
                .apply(" create_time > DATE_FORMAT(DATE_SUB(now(),INTERVAL 30 day), '%Y-%m-%d 00:00:00')")
                .count();
        Integer evaluateCount = entrustCallbackService.lambdaQuery().eq(EntrustCallback::getLawyerUserId, entrustLawyerLib.getUserId())
                .apply(" create_time > DATE_FORMAT(DATE_SUB(now(),INTERVAL 30 day), '%Y-%m-%d 00:00:00')")
                .isNotNull(EntrustCallback::getEvaluate).count();
        Integer notGoodCount = entrustCallbackService.lambdaQuery().eq(EntrustCallback::getLawyerUserId, entrustLawyerLib.getUserId())
                .apply(" create_time > DATE_FORMAT(DATE_SUB(now(),INTERVAL 30 day), '%Y-%m-%d 00:00:00')")
                .like(EntrustCallback::getEvaluate, "不满意").count();
        if (userCount > 0 && evaluateCount > 0 && notGoodCount == 0) {
            totalScore += 15;
            entrustLawyerLib.setMonthEntrustSatisfaction(new BigDecimal(15));
            log.info("{} 有偿法律服务满意度：{}", entrustLawyerLib.getNickName(), 15);
        } else if (userCount == 0 || evaluateCount == 0) {
            totalScore += 9;
            entrustLawyerLib.setMonthEntrustSatisfaction(new BigDecimal(9));
            log.info("{} 有偿法律服务满意度：{}", entrustLawyerLib.getNickName(), 9);
        } else {
            Integer entrustSatisfactionSore = 15;
            int entrustSatisfactionFinalSore = entrustSatisfactionSore - (notGoodCount * 3);
            totalScore += (Math.max(entrustSatisfactionFinalSore, 0));
            entrustLawyerLib.setMonthEntrustSatisfaction(new BigDecimal((Math.max(entrustSatisfactionFinalSore, 0))));
            log.info("{} 有偿法律服务满意度：{}", entrustLawyerLib.getNickName(), (Math.max(entrustSatisfactionFinalSore, 0)));
        }
        // 质检评分
        if (entrustLawyerLib.getMonthQualityScore() != null && entrustLawyerLib.getMonthQualityScore().intValue() != 0) {
            if (entrustLawyerLib.getMonthQualityScore().compareTo(new BigDecimal(85)) >= 0) {
                totalScore += 10;
                log.info("{} 质检评分：{}", entrustLawyerLib.getNickName(), 10);
            } else {
                totalScore += 10;
                BigDecimal rateStan = new BigDecimal(85);
                BigDecimal bigDecimal = rateStan.subtract(entrustLawyerLib.getMonthQualityScore()).setScale(0, RoundingMode.CEILING);
                totalScore -= Math.min(bigDecimal.intValue(), 10);
                log.info("{} 质检评分：{}", entrustLawyerLib.getNickName(), 10 - Math.min(bigDecimal.intValue(), 10));
            }
        } else {
            totalScore += 5;
            log.info("{} 质检评分：{}", entrustLawyerLib.getNickName(), 5);
        }
        // 加分项
        totalScore += Math.min(entrustLawyerLib.getVolunteerScore() == null ? 0 : entrustLawyerLib.getVolunteerScore().intValue(), 10);
        log.info("{} 加分项：{}", entrustLawyerLib.getNickName(), Math.min(entrustLawyerLib.getVolunteerScore() == null ? 0 : entrustLawyerLib.getVolunteerScore().intValue(), 10));
        // 减分项
        totalScore -= entrustLawyerLib.getDeductPoints() == null ? 0 : entrustLawyerLib.getDeductPoints().intValue();
        log.info("{} 减分项：{}", entrustLawyerLib.getNickName(), (entrustLawyerLib.getDeductPoints() == null ? 0 : entrustLawyerLib.getDeductPoints().intValue()));
        log.info("{} 总分：{}", entrustLawyerLib.getNickName(), totalScore);
        entrustLawyerLib.setTotalScore(Math.max(totalScore, 0));
    }
}
