package com.gobon.project.intelligencequality.controller;

import com.gobon.common.exception.CustomException;
import com.gobon.common.utils.DateUtils;
import com.gobon.common.utils.SecurityUtils;
import com.gobon.framework.aspectj.lang.annotation.Log;
import com.gobon.framework.aspectj.lang.enums.BusinessType;
import com.gobon.framework.security.LoginUser;
import com.gobon.framework.task.IntelligenceQualityTask;
import com.gobon.framework.web.controller.BaseController;
import com.gobon.framework.web.domain.AjaxResult;
import com.gobon.framework.web.page.TableDataInfo;
import com.gobon.project.intelligencequality.constant.QualityRedisConstant;
import com.gobon.project.intelligencequality.domain.CallcenterQualitySamplingRule;
import com.gobon.project.intelligencequality.domain.param.SamplingRuleParam;
import com.gobon.project.intelligencequality.domain.vo.CallcenterQualitySamplingRuleVO;
import com.gobon.project.intelligencequality.service.ICallcenterQualitySamplingRuleService;
import com.gobon.project.system.domain.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 质检抽样规则Controller
 * 
 * <AUTHOR>
 * @date 2020-09-04
 */
@RestController
@RequestMapping("/system/samplingRule")
public class CallcenterQualitySamplingRuleController extends BaseController
{
    /**
     * 智能质检redis key 前缀
     */
    private static final String REDIS_QUALITY_PREFIX = "quality:";

    @Autowired
    private ICallcenterQualitySamplingRuleService callcenterQualitySamplingRuleService;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    IntelligenceQualityTask intelligenceQualityTask;
    /**
     * 查询质检抽样规则列表
     */
    @PreAuthorize("@ss.hasPermi('system:samplingRule:query')")
    @GetMapping("/getSamplingRuleList")
    public TableDataInfo getSamplingRuleList(CallcenterQualitySamplingRule callcenterQualitySamplingRule) {
        startPage();
        List<CallcenterQualitySamplingRuleVO> list = callcenterQualitySamplingRuleService.selectCallcenterQualitySamplingRuleList(callcenterQualitySamplingRule);
        return getDataTable(list);
    }

    /**
     * 获取质检抽样规则详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:samplingRule:query')")
    @GetMapping(value = "/getSamplingRule/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(callcenterQualitySamplingRuleService.selectCallcenterQualitySamplingRuleById(id));
    }

    /**
     * 新增/修改质检抽样规则
     */
    @PreAuthorize("@ss.hasPermi('system:samplingRule:addOrEdit')")
    @Log(title = "新增/修改质检抽样规则", businessType = BusinessType.INSERT)
    @PostMapping("/addOrEditSamplingRule")
    public AjaxResult addOrEditSamplingRule(@RequestBody CallcenterQualitySamplingRule callcenterQualitySamplingRule) {
        if(callcenterQualitySamplingRule.getRecordEndTime() != null && callcenterQualitySamplingRule.getRecordStartTime() != null){
            long days = DateUtils.diffLocalDate(callcenterQualitySamplingRule.getRecordStartTime(),callcenterQualitySamplingRule.getRecordEndTime());
            if(days > 30){
                throw new CustomException("抽样时间区间不能大于30天！");
            }
        }
        if(callcenterQualitySamplingRule.getSamplingNumber() != null && Integer.valueOf(callcenterQualitySamplingRule.getSamplingNumber()) > 200){
            throw new CustomException("抽样个数不能大于200个！");
        }

        int rows = callcenterQualitySamplingRuleService.insertOrUpdateCallcenterQualitySamplingRule(callcenterQualitySamplingRule);
        if (rows == 1) {
            return AjaxResult.success();
        } else if (rows == 2){
            return AjaxResult.error("所选择的评分规则已停用，请重新选择");
        } else {
            return AjaxResult.error();
        }
    }

    /**
     * 执行抽样规则
     */
    @PreAuthorize("@ss.hasPermi('system:samplingRule:addOrEdit')")
    @Log(title = "执行抽样规则", businessType = BusinessType.INSERT)
    @PostMapping("/executionSamplingRule")
    public AjaxResult executionSamplingRule(@RequestBody SamplingRuleParam param) {

        //防止gpu 服务器 会话数太多 导致带宽不足  限制 一次只能执行一个质检任务 一次任务执行完成需要等2分钟后才能继续执行下个

        if(redisTemplate.opsForValue().get(QualityRedisConstant.QUALITY_PROCESS_LOCK_KEY) != null){
            return AjaxResult.error("质检任务正在执行中，请稍后再试！");
        }

        Long time = redisTemplate.getExpire(QualityRedisConstant.QUALITY_PROCESS_LOCK_TIME, TimeUnit.SECONDS);
        if(time != null && time > 0){
            return AjaxResult.error("请等待"+time+"秒后再试！");
        }

        redisTemplate.opsForValue().set(QualityRedisConstant.QUALITY_PROCESS_LOCK_KEY,"LOCK",30, TimeUnit.MINUTES);


        LoginUser loginUser  = SecurityUtils.getLoginUser();
        if("1".equals(param.getExecutionState())) {
            CompletableFuture.runAsync(() -> {
                intelligenceQualityTask.scanCallRecordParam(param.getSamplingRuleId().toString(), "gd12348.com",loginUser,param.getPlatForm(),false); // todo
            });
        }
        return callcenterQualitySamplingRuleService.executionSamplingRule(param);
    }

    /**
     * 删除质检抽样规则
     */
    @PreAuthorize("@ss.hasPermi('system:samplingRule:remove')")
    @Log(title = "删除质检抽样规则", businessType = BusinessType.DELETE)
	@PostMapping("/removeSamplingRule/{id}")
    public AjaxResult removeSamplingRule(@PathVariable Long id) {
        //删除发布状态限制
        /*int row = */callcenterQualitySamplingRuleService.deleteCallcenterQualitySamplingRuleById(id);
        /*if (row == 1) {*/
            return AjaxResult.success();
        /*}*/ /*else if (row == 2){
            return AjaxResult.error("抽样规则已发布，删除失败！");
        } else {
            return AjaxResult.error();
        }*/
    }

    /**
     * 简单抽样规则列表
     * @return {@link AjaxResult}
     */
    @RequestMapping("/simpleList")
    public AjaxResult simpleList() {
        final SysUser user = SecurityUtils.getLoginUser().getUser();
        return AjaxResult.success(callcenterQualitySamplingRuleService.selectAllList(user.getAddrArea()));
    }

    /**
     * 抽样质检进度
     */
    @GetMapping("/progress/{id}")
    public AjaxResult samplingProgress(@PathVariable("id") Long id) {
        Object val = redisTemplate.opsForValue().get(REDIS_QUALITY_PREFIX + "progress:"+id);
        Integer progress = val == null ? 100 : Integer.parseInt(val.toString());
        return AjaxResult.success(progress);
    }

    /**
     * 取消/发布抽样规则
     * @param param
     * @return
     */
    @PostMapping(value = "/cancelOrPublish")
    @PreAuthorize("@ss.hasPermi('system:samplingRule:cancelOrPublish')")
    @Log(title = "取消/发布抽样规则", businessType = BusinessType.UPDATE)
    public AjaxResult cancelPublish(@RequestBody SamplingRuleParam param){
        callcenterQualitySamplingRuleService.cancelPublish(param);
        return AjaxResult.success();
    }


}
