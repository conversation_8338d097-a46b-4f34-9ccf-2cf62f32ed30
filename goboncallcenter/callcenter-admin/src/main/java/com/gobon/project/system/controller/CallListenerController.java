package com.gobon.project.system.controller;

import com.gobon.common.utils.poi.ExcelUtil;
import com.gobon.framework.aspectj.lang.annotation.Log;
import com.gobon.framework.aspectj.lang.enums.BusinessType;
import com.gobon.framework.web.controller.BaseController;
import com.gobon.framework.web.domain.AjaxResult;
import com.gobon.framework.web.page.TableDataInfo;
import com.gobon.project.system.domain.param.CallListenerParam;
import com.gobon.project.system.domain.SysDictData;
import com.gobon.project.system.domain.param.UserAnalysisParam;
import com.gobon.project.system.domain.vo.*;
import com.gobon.project.system.service.CallListenerService;
import com.gobon.project.system.service.ISysDictDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api(value = "坐席监控相关操作接口",tags = "坐席监控相关操作接口")
public class CallListenerController extends BaseController {
    @Autowired
    CallListenerService callListenerService;

    @Autowired
    ISysDictDataService  sysDictDataService;

    @GetMapping("/system/listener/getCallListenerInfo")
    @ApiOperation("坐席监控获取当前坐席状态")
    public TableDataInfo getCallListenerInfo(CallListenerParam callListener){
        startPage();
        List<CallListenerVo> callListenerInfo = callListenerService.getCallListenerInfo(callListener);
        return getDataTable(callListenerInfo);
    }



    /**
     * 导出坐席监控获取当前坐席状态
     */
    @ApiOperation("导出坐席监控获取当前坐席状态")
    @Log(title = "坐席监控获取当前坐席状态", businessType = BusinessType.EXPORT)
    @GetMapping("/system/listener/getBusinessDataExport")
    public AjaxResult getBusinessDataExport( CallListenerParam callListener ) {
        AjaxResult ajaxResult = AjaxResult.success();
        List<CallListenerVo> callListenerInfo = callListenerService.getCallListenerInfo(callListener);
        ExcelUtil<CallListenerVo> util = new ExcelUtil<>( CallListenerVo.class );
        return util.exportExcel( callListenerInfo, "坐席监控获取当前坐席状态" );
    }


    @GetMapping("/system/listener/getPersonCount")
    @ApiOperation("坐席监控获取呼叫服务数据趋势")
    public  AjaxResult getPersonCount(CallListenerParam param){
        PersonSiderVo personSiderVo = callListenerService.getPersonCount(param);
        List<Map> perlist = new ArrayList<>();
        Map onlineMap = new HashMap();
        onlineMap.put("name","在线");
        onlineMap.put("value",personSiderVo.getOnlineCount());

        Map notOnlineMap = new HashMap();
        notOnlineMap.put("name","离线");
        notOnlineMap.put("value",personSiderVo.getNotOnlineCount());

        Map notLoginMap = new HashMap();
        notLoginMap.put("name","未登录");
        notLoginMap.put("value",personSiderVo.getNotLoginCount());

        Map callingCountMap = new HashMap();
        callingCountMap.put("name","通话中");
        callingCountMap.put("value",personSiderVo.getCallingCount());

        perlist.add(notLoginMap);
        perlist.add(notOnlineMap);
        perlist.add(onlineMap);
        perlist.add(callingCountMap);

        AjaxResult ajaxResult = AjaxResult.success();
        ajaxResult.put("data",perlist);
        return ajaxResult;
    }


    /**
     * 导出获取呼叫服务数据趋势
     */
    @ApiOperation("导出获取呼叫服务数据趋势")
    @Log(title = "获取呼叫服务数据趋势", businessType = BusinessType.EXPORT)
    @GetMapping("/system/listener/getPersonRateExport")
    public AjaxResult getPersonRateExport( CallListenerParam param ) {
        AjaxResult ajaxResult = AjaxResult.success();
        PersonSiderVo personSiderVo = callListenerService.getPersonCount(param);
        List<SeatMonitoringRateExportVo> exportVos = new ArrayList<>();
        if ( !ObjectUtils.isEmpty( personSiderVo ) ) {
            Integer total = personSiderVo.getCallingCount() + personSiderVo.getNotLoginCount() + personSiderVo.getNotOnlineCount()
                    + personSiderVo.getOnlineCount();
            SeatMonitoringRateExportVo countVo = new SeatMonitoringRateExportVo();
            countVo.setDateType( "数量" );
            countVo.setOnline( personSiderVo.getOnlineCount().toString() );
            countVo.setNotLogin( personSiderVo.getNotLoginCount().toString() );
            countVo.setNotOnline( personSiderVo.getNotOnlineCount().toString() );
            countVo.setCalling( personSiderVo.getCallingCount().toString() );
            exportVos.add( countVo );

            SeatMonitoringRateExportVo rateVo = new SeatMonitoringRateExportVo();
            rateVo.setDateType( "占比" );
            rateVo.setOnline( getRate( personSiderVo.getOnlineCount(), total ) );
            rateVo.setNotOnline( getRate( personSiderVo.getNotOnlineCount(), total ) );
            rateVo.setCalling( getRate( personSiderVo.getCallingCount(), total ) );
            rateVo.setNotLogin( getRate( personSiderVo.getNotLoginCount(), total ) );
            exportVos.add( rateVo );
        }
        ExcelUtil<SeatMonitoringRateExportVo> util = new ExcelUtil<>( SeatMonitoringRateExportVo.class );
        return util.exportExcel( exportVos, "获取呼叫服务数据趋势" );
    }


    /**
     * 导出坐席监控获取当前状态人数
     */
    @ApiOperation("导出坐席监控获取当前状态人数")
    @Log(title = "坐席监控获取当前状态人数", businessType = BusinessType.EXPORT)
    @GetMapping("/system/listener/getPersonCountExport")
    public AjaxResult getPersonCountExport( CallListenerParam param ) {
        AjaxResult ajaxResult = AjaxResult.success();
        PersonSiderVo personSiderVo = callListenerService.getPersonCount(param);
        List<SeatMonitoringCountExportVo> exportVos = new ArrayList<>();
        SeatMonitoringCountExportVo countExportVo = new SeatMonitoringCountExportVo();
        if ( !ObjectUtils.isEmpty( personSiderVo ) ){
            countExportVo.setOnline( personSiderVo.getOnlineCount().toString() );
            countExportVo.setCalling( personSiderVo.getCallingCount().toString() );
            countExportVo.setDoingCount( personSiderVo.getDoingCount().toString() );
            countExportVo.setGqCount( personSiderVo.getGqCount().toString() );
            countExportVo.setNotLogin( personSiderVo.getNotLoginCount().toString() );
            countExportVo.setNotOnline( personSiderVo.getNotOnlineCount().toString() );
            int i = Integer.valueOf( personSiderVo.getRestEatSc() ) + Integer.valueOf( personSiderVo.getRestMeetSc() )
                    + Integer.valueOf( personSiderVo.getRestPxSc() ) + Integer.valueOf( personSiderVo.getRestSsjSc() )
                    + Integer.valueOf( personSiderVo.getRestXxSc() );
            countExportVo.setRestCount( i + "" );
            exportVos.add(countExportVo);
        }
        ExcelUtil<SeatMonitoringCountExportVo> util = new ExcelUtil<>( SeatMonitoringCountExportVo.class );
        return util.exportExcel( exportVos, "坐席监控获取当前状态人数" );
    }

    /**
     * 得到格式化百分比
     * @param num1
     * @param num2
     */
    public String getRate( Integer num1, Integer num2 ){
        if ( num2 == 0 ){
            return "0%";
        }
        DecimalFormat df=new DecimalFormat("0.00");
        double l = ( num1 * 100 ) / (double) num2;
        double result = Double.valueOf( df.format( l ) );
        return result + "%";
    }


    @GetMapping("/system/listener/kfService")
    @PreAuthorize("@ss.hasPermi('system:sitListener:list')")
    @ApiOperation("获取当前坐席状态的筛选条件")
    public  AjaxResult kfService(){
        AjaxResult success = AjaxResult.success();

        List<Map> callList = new ArrayList<>();

        List<SysDictData> serviceCallStates = sysDictDataService.selectDictDataByType("service_call_states");
        serviceCallStates.removeIf(t -> t.getDictValue().equals("55"));
        for (SysDictData serviceCallState : serviceCallStates) {
            Map map = new HashMap();
            String  callvalue = serviceCallState.getDictValue();
            String  callLable = serviceCallState.getDictLabel();
            map.put("value",callvalue);
            map.put("label",callLable);
            callList.add(map);
        }
        Map lastMap = new HashMap();
        lastMap.put("value","000");
        lastMap.put("label","未通话");
        callList.add(lastMap);
        success.put("callStatus",callList);

        List<Map> resultList = new ArrayList<>();
        List<SysDictData> serviceStates = sysDictDataService.selectDictDataByType("service_states");
        for (SysDictData serviceState : serviceStates) {
            Map map = new HashMap();
            String  value = serviceState.getDictValue();
            String  label = serviceState.getDictLabel();

            if(value.equals("2")){
                List<SysDictData> serviceRestStates = sysDictDataService.selectDictDataByType("service_rest_states");

                List<Map> childList = new ArrayList<>();
                for (SysDictData serviceRestState : serviceRestStates) {
                    Map map1 = new HashMap();
                    String  restvalue = serviceRestState.getDictValue();
                    String  restLable = serviceRestState.getDictLabel();
                    map1.put("value",restvalue);
                    map1.put("label",restLable);
                    childList.add(map1);
                }
                map.put("children",childList);
                map.put("value",value);
                map.put("label",label);
                resultList.add(map);
                continue;
            }

            if(value.equals("7")){
                continue;
            }
            map.put("value",value);
            map.put("label",label);
            resultList.add(map);
        }
        Map notLoginMap = new HashMap();
        notLoginMap.put("value","未登录");
        notLoginMap.put("label","未登录");
        resultList.add(notLoginMap);
        success.put("data",resultList);

        return  success;

    }


    @ApiOperation("坐席监控  侧边栏的数据")
    @GetMapping("/system/listener/getPersonDetail")
    @PreAuthorize("@ss.hasPermi('system:sitListener:list')")
    public AjaxResult getPersonDetail(CallListenerParam param){
        AjaxResult ajaxResult = AjaxResult.success();
        PersonSiderVo personSiderVo = callListenerService.sideBar(param);
        ajaxResult.put("data",personSiderVo);
        return ajaxResult;
    }
}
