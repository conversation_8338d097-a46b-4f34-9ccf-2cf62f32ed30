package com.gobon.project.phonedata.pojo.vo;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.gobon.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Min;
import java.io.Serializable;

@Data
@ToString
@ApiModel("办事量统计")
public class PhoneBslMonthCountVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    @Excel(name = "日期")
    private String dateStr;
    /**
     * 数量
     */
    @Min(message = "数量不能为负数!", value = 0)
    @ApiModelProperty(value = "数量")
    @Excel(name = "数量")
    private Integer countNum;

    @ApiModelProperty(value = "是否允许修改")
    private Boolean edit = false;

}
