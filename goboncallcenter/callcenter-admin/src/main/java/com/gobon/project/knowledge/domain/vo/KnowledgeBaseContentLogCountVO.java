package com.gobon.project.knowledge.domain.vo;

import com.gobon.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 知识库内容-日志
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
@ApiModel(value="KnowledgeBaseContentLogCountVO", description="知识库内容-日志")
public class KnowledgeBaseContentLogCountVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "日期")
    @Excel(name = "日期")
    private String date;

    @ApiModelProperty(value = "新增数量")
    @Excel(name = "新增数量")
    private int addCount;

    @ApiModelProperty(value = "修改数量")
    @Excel(name = "修改数量")
    private int updateCount;

    @ApiModelProperty(value = "删除数量")
    @Excel(name = "删除数量")
    private int delCount;


}
