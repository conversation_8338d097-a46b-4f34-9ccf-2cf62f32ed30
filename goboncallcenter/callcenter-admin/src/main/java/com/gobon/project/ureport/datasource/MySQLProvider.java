//package com.gobon.project.ureport.datasource;
//
//import java.io.ByteArrayInputStream;
//import java.io.File;
//import java.io.FileInputStream;
//import java.io.FileNotFoundException;
//import java.io.FileOutputStream;
//import java.io.IOException;
//import java.io.InputStream;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//
//import javax.servlet.ServletContext;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpSession;
//
//import org.apache.commons.io.IOUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.BeansException;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.ApplicationContext;
//import org.springframework.context.ApplicationContextAware;
//import org.springframework.stereotype.Component;
//
//import com.bstek.ureport.exception.ReportException;
//import com.bstek.ureport.provider.report.ReportFile;
//import com.bstek.ureport.provider.report.ReportProvider;
//import com.gobon.project.ureport.domain.UreportFile;
//import com.gobon.project.ureport.service.UreportFileService;
//
//import lombok.Setter;
//
///**
// * @description 自定义报表存储器
// * <AUTHOR>
// * @since 20200401
// *
// */
//
//@Setter
//@Component
//@ConfigurationProperties(prefix = "ureport.mysql.provider")
//public class MySQLProvider implements ReportProvider {
//	private final Logger log = LoggerFactory.getLogger(getClass());
//	private static final String NAME = "文件系统";
//	private String prefix = "file:";
//
//	@Value("${ureport.myfileStoreDir}")
//	private String fileStoreDir;
//	@Value("${ureport.mydisableFileProvider}")
//	private boolean disabled;
//
//	@Autowired
//	private UreportFileService ureportFileService;
//
//	/**
//	 * 根据报表名加载报表文件
//	 *
//	 * @param file 报表名称
//	 * @return 返回的InputStream
//	 */
//	@Override
//	public InputStream loadReport(String file) {
//		// UreportFile UreportFile =
//		// ureportFileService.queryUreportFileByName(getCorrectName(file));
//		// byte[] content = UreportFile.getContent();
//		// ByteArrayInputStream inputStream = new ByteArrayInputStream(content);
//		// return inputStream;
//		if (file.startsWith(prefix)) {
//			file = file.substring(prefix.length(), file.length());
//		}
//		String fullPath = fileStoreDir + "/" + file;
//		try {
//			return new FileInputStream(fullPath);
//		} catch (FileNotFoundException e) {
//			throw new ReportException(e);
//		}
//	}
//
//	/**
//	 * 根据报表名，删除指定的报表文件
//	 *
//	 * @param file 报表名称
//	 */
//	@Override
//	public void deleteReport(String file) {
//		ureportFileService.deleteReportFileByName(getCorrectName(file));
//		if (file.startsWith(prefix)) {
//			file = file.substring(prefix.length(), file.length());
//		}
//		String fullPath = fileStoreDir + "/" + file;
//		File f = new File(fullPath);
//		if (f.exists()) {
//			f.delete();
//		}
//	}
//
//	/**
//	 * 获取所有的报表文件
//	 *
//	 * @return 返回报表文件列表
//	 */
//	@Override
//	public List<ReportFile> getReportFiles() {
//		List<UreportFile> list = ureportFileService.queryReportFileList();
//		List<ReportFile> reportList = new ArrayList<>();
//		for (UreportFile UreportFile : list) {
//			reportList.add(new ReportFile(UreportFile.getName(), UreportFile.getUpdateTime()));
//		}
//		return reportList;
//	}
//
//	/**
//	 * 保存报表文件
//	 *
//	 * @param file    报表名称
//	 * @param content 报表的XML内容
//	 */
//	@Override
//	public void saveReport(String file, String content) {
//		file = getCorrectName(file);
//		UreportFile UreportFile = ureportFileService.queryUreportFileByName(file);
//		Date currentDate = new Date();
//		if (UreportFile == null) {
//			UreportFile = new UreportFile();
//			UreportFile.setName(file);
//			// UreportFile.setContent(content.getBytes()); 文件存储实体文件 先注释掉存储数据库
//			UreportFile.setCreateTime(currentDate);
//			UreportFile.setUpdateTime(currentDate);
//			ureportFileService.insertReportFile(UreportFile);
//
//		} else {
//			//UreportFile.setContent(content.getBytes());
//			UreportFile.setUpdateTime(currentDate);
//			ureportFileService.updateReportFile(UreportFile);
//		}
//		if (file.startsWith(prefix)) {
//			file = file.substring(prefix.length(), file.length());
//		}
//		File file2 = new File(fileStoreDir);
//		if (!file2.exists()) {
//			file2.mkdirs();
//		}
//		String fullPath = fileStoreDir + "/" + file;
//		FileOutputStream outStream = null;
//		try {
//			outStream = new FileOutputStream(new File(fullPath));
//			IOUtils.write(content, outStream, "utf-8");
//		} catch (Exception ex) {
//			throw new ReportException(ex);
//		} finally {
//			if (outStream != null) {
//				try {
//					outStream.close();
//				} catch (IOException e) {
//					e.printStackTrace();
//				}
//			}
//		}
//
//	}
//
//	/**
//	 * @return 返回存储器名称
//	 */
//	@Override
//	public String getName() {
//		return NAME;
//	}
//
//	/**
//	 * @return 返回是否禁用
//	 */
//	@Override
//	public boolean disabled() {
//		return this.disabled;
//	}
//
//	/**
//	 * @return 返回报表文件名前缀
//	 */
//	@Override
//	public String getPrefix() {
//		return prefix;
//	}
//
//	/**
//	 * @description 获取没有前缀的文件名
//	 * @param name
//	 * @return
//	 */
//
//	private String getCorrectName(String name) {
//
//		log.info("前缀:" + prefix);
//		if (name.startsWith(prefix)) {
//			name = name.substring(prefix.length(), name.length());
//		}
//		return name;
//	}
//
//
//}
