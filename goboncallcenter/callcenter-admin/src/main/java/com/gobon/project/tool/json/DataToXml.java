package com.gobon.project.tool.json;

import com.gobon.common.constant.ConfigCommonConstants;
import com.gobon.common.constant.ConfigConstants;
import com.gobon.common.constant.Constants;
import com.gobon.common.spring.SpringUtils;
import com.gobon.common.utils.StringUtils;
import com.gobon.framework.minio.MinioProp;
import com.gobon.project.ivrnavigation.domain.vo.IVRSettingVo;
import com.gobon.project.ivrnavigation.domain.vo.SysNavigationTempVo;
import com.gobon.project.setting.domain.CallcenterSatisfiedInfo;
import com.gobon.project.system.service.ISysConfigService;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.io.DocumentSource;

import javax.annotation.Resource;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamResult;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import static org.bouncycastle.asn1.x500.style.RFC4519Style.c;


/**
 * 转换xml工具类
 */
public class DataToXml {



    /*@Resource
    private ISysConfigService sysConfigService;*/

    public static void main(String[] args) throws Exception {
        long a = System.currentTimeMillis();
        DataToXml cd = new DataToXml();
        String xmlString = "";
        cd.stringToXml("D:/", xmlString, "ivrxml");
        System.out.println(System.currentTimeMillis() - a);
    }

    /**
     * 描述：生成txt文件
     *
     * @param path
     * @param xmlString
     * @param fileName
     * @throws Exception
     */
    public static String stringToTxt(String path, String xmlString, String fileName) throws Exception {
        String resFile = path + fileName + ".txt";
        File pathFolder = new File(path);
        if (!pathFolder.exists()) {
            pathFolder.mkdirs();
        }
        FileWriter fwriter = null;
        try {
            // 覆盖原来的内容
            File file = new File(resFile);
            if (file.exists()) {
                file.delete();
            }
            fwriter = new FileWriter(resFile);
            fwriter.write(xmlString);
        } catch (IOException ex) {
            ex.printStackTrace();
        } finally {
            try {
                assert fwriter != null;
                fwriter.flush();
                fwriter.close();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        return resFile;
    }

    /**
     * 描述：生成xml文件
     *
     * @param xmlString
     * @throws Exception
     */
    public static String stringToXml(String path, String xmlString, String fileName) throws Exception {
        Document resDoc = DocumentHelper.parseText(xmlString);
        TransformerFactory tFactory = TransformerFactory.newInstance();
        Transformer transformer = tFactory.newTransformer();
        resDoc.getRootElement().asXML();//去除头部
        DocumentSource source = new DocumentSource(resDoc);
        transformer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
        //设置文档的换行与缩进
        transformer.setOutputProperty(OutputKeys.INDENT, "YES");
        String resFile = path + fileName + ".xml";
        File file = new File(resFile);
        if (file.exists()) {
            file.delete();
        }
        StreamResult result = new StreamResult(file);
        transformer.transform(source, result);
        return resFile;
    }

    /**
     * 生成评价配置
     *
     * @param satisfiedInfos 开启的语音评价
     * @param audioUrl       录音地址
     * @param settingVo      ivr基础配置
     * @param filePath       文件服务器基础地址
     * @return
     */
    public static String getSatisfiedText(List<CallcenterSatisfiedInfo> satisfiedInfos, String audioUrl,
                                          IVRSettingVo settingVo, String filePath) {
        StringBuffer sb = new StringBuffer();
        sb.append("<menu name=\"satisfaction_ivr\"\n" +
                " greet-long=\"" + (StringUtils.isNotBlank(audioUrl) ? filePath + audioUrl : "") + "\"\n" +
                " greet-short=\"" + (StringUtils.isNotBlank(audioUrl) ? filePath + audioUrl : "") + "\"\n" +
                " invalid-sound=\"" + (StringUtils.isNotBlank(settingVo.getInvalidSound()) ?
                settingVo.getInvalidSound() : "") + "\"\n" +
                " exit-sound=\"" + (StringUtils.isNotBlank(settingVo.getExitSound()) ? settingVo.getExitSound() : "") + "\"\n" +
                " confirm-macro=\"\"\n" +
                " confirm-key=\"\"\n" +
                " tts-engine=\"\"\n" +
                " tts-voice=\"\"\n" +
                " confirm-attempts=\"3\"\n" +
                " timeout=\"" + settingVo.getTimeout() + "\"\n" +
                " inter-digit-timeout=\"" + settingVo.getInterDigitTimeout() + "\"\n" +
                " max-failures=\"" + settingVo.getMaxFailures() + "\"\n" +
                " max-timeouts=\"" + settingVo.getMaxTimeouts() + "\"\n" +
                " digit-len=\"1\">\n");
        for (CallcenterSatisfiedInfo satisfiedInfo : satisfiedInfos) {
            sb.append("<entry action=\"menu-exec-app\" " +
                    "digits=\"" + satisfiedInfo.getNumberKey() + "\" " +
                    "param=\"execute_extension set:satisfaction_class=" + satisfiedInfo.getId() + "," +
                    "set:satisfaction_goodby=" + settingVo.getSatisfiedEndAudio() + ",park: inline\"/>\n");
        }
        sb.append("</menu>\n");
        return sb.toString();
    }


    /**
     * 生成ivr导航的xml文件
     *
     * @param vo
     * @return
     */
    public static String getIVRXmlString(SysNavigationTempVo vo, IVRSettingVo settingVo, StringBuffer sb,
                                         String filePath, Long topId) {
        sb = (sb != null && sb.length() > 0) ? sb : new StringBuffer();
        String domainText = Constants.DOMAIN_12348.equals(ConfigConstants.domain)
                ? Constants.DOMAIN_12348 : Constants.DOMAIN_DEFAULT;
        //返回上级和返回首层、转人工客服才有menu菜单不需要
        if ("6".equals(vo.getNavigationAction()) || "7".equals(vo.getNavigationAction())
                || "2".equals(vo.getNavigationAction())) {
            return sb.toString();
        }
        if (StringUtils.isBlank(vo.getAudioUrl())) {
            return sb.toString();
        }
        sb.append("<menu name=\"" + (StringUtils.isBlank(vo.getParentId()) ? Constants.IVR_INTO_DEFAULT : vo.getId()) + "\"\n" +
                " greet-long=\"" + (StringUtils.isNotBlank(vo.getAudioUrl()) ? filePath + vo.getAudioUrl() : "") + "\"\n" +
                " greet-short=\"" + (StringUtils.isNotBlank(vo.getAudioUrl()) ? filePath + vo.getAudioUrl() : "") + "\"\n" +
                " invalid-sound=\"" + (StringUtils.isNotBlank(settingVo.getInvalidSound()) ?
                settingVo.getInvalidSound() : "") + "\"\n" +
                " exit-sound=\"" + (StringUtils.isNotBlank(settingVo.getExitSound()) ? settingVo.getExitSound() : "") + "\"\n" +
                " confirm-macro=\"\"\n" +
                " confirm-key=\"\"\n" +
                " tts-engine=\"\"\n" +
                " tts-voice=\"\"\n" +
                " confirm-attempts=\"3\"\n" +
                " timeout=\"" + settingVo.getTimeout() + "\"\n" +
                " inter-digit-timeout=\"" + settingVo.getInterDigitTimeout() + "\"\n" +
                " max-failures=\"" + settingVo.getMaxFailures() + "\"\n" +
                " max-timeouts=\"" + settingVo.getMaxTimeouts() + "\"\n" +
                " digit-len=\"4\">\n");
        List<SysNavigationTempVo> children = vo.getChildren();
        if (children != null && children.size() > 0) {
            for (SysNavigationTempVo child : children) {
                //下一级导航
                if ("1".equals(child.getNavigationAction())) {
                    sb.append("<entry action=\"menu-sub\" digits=\"" + child.getNavigationKey() + "\" " +
                            "param=\"" + (topId.equals(child.getId()) ? Constants.IVR_INTO_DEFAULT : child.getId()) + "\"/>\n");
                } else if ("2".equals(child.getNavigationAction())) {
                    //分配给客服组
                    sb.append("<entry action=\"menu-exec-app\" digits=\"" + child.getNavigationKey() + "\" " +
                            "param=\"execute_extension set:Outbound_Socket=true,set:Bridge_Profile=SpecifyGroup,set:Bridge_Target=" +
                            "" + (child.getCustomerGroupId() == null ? "" : child.getCustomerGroupId()) + "," +
                            "set:Bridege_Target_Name=" + child.getCustomerGroupName() + "," +
                            "transfer:'100086 XML " + domainText + "' inline\"/>\n");
                    //此时不需要子级
                    child.setChildren(null);
                } else if ("3".equals(child.getNavigationAction())) {
                    //播放自定义语音
                    sb.append("<entry action=\"menu-play-sound\" digits=\"" + child.getNavigationKey() + "\" param=\"" + filePath + child.getNavigationIntroductionAudioUrl() + "\"/>\n");
                    //此时不需要子级
                    child.setChildren(null);
                } else if ("4".equals(child.getNavigationAction())) {
                    //重听一遍
                    sb.append("<entry action=\"menu-sub\" digits=\"" + child.getNavigationKey() + "\" param=\"" + vo.getId() + "\"/>\n");
                    //此时不需要子级
                    child.setChildren(null);
                } else if ("5".equals(child.getNavigationAction())) {
                    ISysConfigService sysConfigService = SpringUtils.getBean(ISysConfigService.class);
                    String ivrGateWay = sysConfigService.selectConfigByKey(ConfigCommonConstants.IVR_GATE_WAY_KEY);
                    // 第三方电话
                    sb.append(
                            "<entry action=\"menu-exec-app\" digits=\""
                                    + child.getNavigationKey()
                                    + "\" param=\"execute_extension bridge:'sofia/gateway/" + ivrGateWay + "/"
                                    + child.getThirdParty()
                                    + "' inline\"/>\n"
                    );
                    child.setChildren(null);
                } else if ("6".equals(child.getNavigationAction())) {
                    String ancestors = child.getAncestors();
                    if (StringUtils.isNotBlank(ancestors) && StringUtils.isNotBlank(child.getParentId())) {
                        String[] split = child.getAncestors().split(",");
                        int index = 0;
                        //获取上级
                        for (int i = 0; i < split.length; i++) {
                            if (child.getParentId().equals(split[i])) {
                                index = i;
                                break;
                            }
                        }
                        index = index > 0 ? (index - 1) : index;
                        String lastUrl = split[index];
                        // 返回上一级
                        sb.append("<entry action=\"menu-sub\" digits=\"" + child.getNavigationKey() + "\" " +
                                "param=\"" + (topId.toString().equals(lastUrl) ? Constants.IVR_INTO_DEFAULT : lastUrl) + "\"/>\n");
                        //此时不需要子级
                        child.setChildren(null);
                    }
                } else if ("7".equals(child.getNavigationAction())) {
                    // 返回首层
                    sb.append("<entry action=\"menu-sub\" digits=\"" + child.getNavigationKey() + "\" param=\"" + Constants.IVR_INTO_DEFAULT + "\"/>\n");
                    //此时不需要子级
                    child.setChildren(null);
                }
            }

            sb.append("</menu>\n");
            for (SysNavigationTempVo child : children) {
                getIVRXmlString(child, settingVo, sb, filePath, topId);
            }
        } else {
            sb.append("</menu>\n");
        }
        return sb.toString();
    }

}

