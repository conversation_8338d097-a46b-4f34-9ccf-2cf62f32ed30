package com.gobon.project.navigation.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program goboncallcenter
 * @description
 * @create 2021/3/10 20:46
 **/
@Data
public class NavigationInitVo implements Serializable {
    private static final long serialVersionUID = -1492794056832875123L;

    /**
     * 号码
     */
    private String phoneNumber;

    /**
     * id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 导航名称
     */
    private String navigationName;


    /**
     * ivr 名称
     */
    private String ivrRootName;

    /**
     * 导航标识(人工自定义可选用枚举配置)
     */
    private String navigationFlag;

    /**
     * ivr进入下一节点方式
     */
    private Integer intoNextFlag;


    /**
     * 是否开启排队提示
     */
    private Integer lineAudioFlag;

    /**
     * 服务时间分类(按周、按节假日)
     */
    private Integer serviceTimeType;

    /**
     * 是否VIP优先
     */
    private Integer vipFlag;

    /**
     * 是否历史客服优先
     */
    private Integer historyFlag;

    /**
     * 是否地域优先
     */
    private Integer areaFlag;

    /**
     * 是否自动接起(这个暂时放在这里，后续改到坐席配置里去)
     */
    private Integer autoPickFlag;

    /**
     * 分配类型(随机分配、最大空闲分配)
     */
    private Integer distributionType;

    /**
     * 服务开始时间
     */
    private String startTime;

    /**
     * 服务结束时间
     */
    private String endTime;

    /**
     * 非服务时间动作(播放语音、展示介绍文字)
     */
    private String noserviceRespond;

    /**
     * 更新标识
     */
    private Integer updateFlag;

    /**
     * 更新内容
     */
    private String updateAudio;

    /**
     * 域信息
     */
    private String domain;

    /**
     * 导航内容
     */
    private String audioFile;

    /**
     * 导航技能组
     */
    private String skillId;

    /**
     * 服务时间
     */
    private String serviceTimes;

    /**
     * 服务时间的星期
     * 说明：指定星期范围,以','分组,以'-'分割,有效数字为(1-7),其中1为星期1，7为星期天，其他数值对应星期几
     * add by zhang 20210709
     */
    private String serviceWeekday;

    /**
     * 服务时间的服务日期：
     * 说明：指定日期或者日期范围以','分组,以'-'为分割;例如:(2021/05/23-2021/05/28,2021/05/28)
     * add by zhang 20210709
     */
    private String serviceDays;
}
