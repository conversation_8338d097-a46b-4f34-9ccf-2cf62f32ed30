package com.gobon.project.quality.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.gobon.framework.aspectj.lang.annotation.Excel;
import com.gobon.framework.web.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 质检项对象 quality_option
 *
 * <AUTHOR>
 * @date 2020-03-17
 */
public class QualityOption extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long id;

    /** 指标项名称/禁忌项名称 */
    @Excel(name = "指标项名称/禁忌项名称")
    private String optionName;

    /** 是否有描述（0否，1是） */
    @Excel(name = "是否有描述", readConverterExp = "0=否，1是")
    private String optionDescState;

    /** 描述 */
    @Excel(name = "描述")
    private String optionDesc;

    /** 分值 （评分制 设置分； 扣分制 设置分； 禁忌项 默认 0） */
    @Excel(name = "分值 ", readConverterExp = "评=分制,设=置分；,扣=分制,设=置分；,禁=忌项,默=认,0=")
    private BigDecimal score;

    /** 排序 */
    @Excel(name = "排序")
    private Integer orderNum;

    /** 质检规则id */
    @Excel(name = "质检规则id")
    @JsonSerialize(using=ToStringSerializer.class)
    private Long ruleId;

    /** 类型（0指标项，1禁忌项） */
    @Excel(name = "类型", readConverterExp = "0=指标项，1禁忌项")
    private String type;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setOptionName(String optionName)
    {
        this.optionName = optionName;
    }

    public String getOptionName()
    {
        return optionName;
    }
    public void setOptionDescState(String optionDescState)
    {
        this.optionDescState = optionDescState;
    }

    public String getOptionDescState()
    {
        return optionDescState;
    }
    public void setOptionDesc(String optionDesc)
    {
        this.optionDesc = optionDesc;
    }

    public String getOptionDesc()
    {
        return optionDesc;
    }
    public void setScore(BigDecimal score)
    {
        this.score = score;
    }

    public BigDecimal getScore()
    {
        return score;
    }
    public void setOrderNum(Integer orderNum)
    {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum()
    {
        return orderNum;
    }
    public void setRuleId(Long ruleId)
    {
        this.ruleId = ruleId;
    }

    public Long getRuleId()
    {
        return ruleId;
    }
    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("optionName", getOptionName())
            .append("optionDescState", getOptionDescState())
            .append("optionDesc", getOptionDesc())
            .append("score", getScore())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("orderNum", getOrderNum())
            .append("ruleId", getRuleId())
            .append("type", getType())
            .toString();
    }
}
