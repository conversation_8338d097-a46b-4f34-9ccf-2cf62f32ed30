package com.gobon.project.quality.service;



import com.baomidou.mybatisplus.extension.service.IService;
import com.gobon.project.quality.domain.entity.CallcenterQualityTaskEntity;
import com.gobon.project.quality.domain.param.QualityTaskDetailParam;
import com.gobon.project.quality.domain.param.QualityTaskQueryParam;
import com.gobon.project.quality.domain.vo.CallcenterQualityTaskDetailVO;
import com.gobon.project.quality.domain.vo.CallcenterQualityTaskVO;

import java.util.List;

/**
 * 质量监测任务(CallcenterQualityTask)服务接口
 *
 * <AUTHOR>
 * @since 2025-01-16 15:45:35
 */
public interface CallcenterQualityTaskService extends IService<CallcenterQualityTaskEntity> {

    List<CallcenterQualityTaskVO> findList(QualityTaskQueryParam param);

    List<CallcenterQualityTaskDetailVO> findTaskDetailData(QualityTaskDetailParam param);
}
