package com.gobon.project.conversation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gobon.api.service.LocalAsrVoiceService;
import com.gobon.common.constant.ConfigCommonConstants;
import com.gobon.common.constant.Constants;
import com.gobon.common.constant.SysConfigEnum;
import com.gobon.common.constant.freeswitch.CallCenterRecordEnum;
import com.gobon.common.exception.CustomException;
import com.gobon.common.utils.*;
import com.gobon.framework.handler.submeter.YearMonthHandler;
import com.gobon.framework.manager.AsyncManager;
import com.gobon.framework.manager.TransactionAfterCommitManager;
import com.gobon.framework.manager.factory.AsyncFactory;
import com.gobon.framework.rabbitmq.CdrVariableConst;
import com.gobon.framework.rabbitmq.MessageProducer;
import com.gobon.framework.redis.RedisCache;
import com.gobon.framework.redis.RedisSequenceFactory;
import com.gobon.framework.redis.RedissonLocker;
import com.gobon.framework.security.LoginUser;
import com.gobon.framework.web.domain.R;
import com.gobon.project.bst.domain.BSTCallRecordMsgObj;
import com.gobon.project.bst.mq.CallSender;
import com.gobon.project.callcenter.constant.CallcenterConstant;
import com.gobon.project.common.constants.CallSumupConstant;
import com.gobon.project.conversation.domain.*;
import com.gobon.project.conversation.domain.param.TransferParam;
import com.gobon.project.conversation.dto.SumUpJsonDto;
import com.gobon.project.conversation.mapper.CallcenterRecordDetailExtMapper;
import com.gobon.project.conversation.mapper.CallcenterRecordDetailMapper;
import com.gobon.project.conversation.mapper.CallcenterRecordMapper;
import com.gobon.project.conversation.service.ICallcenterRecordDetailService;
import com.gobon.project.conversation.service.ICallcenterRecordMonitorService;
import com.gobon.project.conversation.service.ICallcenterRecordService;
import com.gobon.project.conversation.utils.CallSoundUtil;
import com.gobon.project.conversation.vo.*;
import com.gobon.project.customer.domain.CallcenterVisitRecord;
import com.gobon.project.customer.domain.vo.CallcenterCalloutTargetsVo;
import com.gobon.project.customer.mapper.CallcenterCalloutTargetsMapper;
import com.gobon.project.customer.service.ICallcenterCalloutTargetsService;
import com.gobon.project.customer.service.ICallcenterCustomerService;
import com.gobon.project.customer.service.ICallcenterVisitRecordService;
import com.gobon.project.leave.domain.CallcenterLeaveVisitRecord;
import com.gobon.project.leave.service.ICallcenterLeaveMessageService;
import com.gobon.project.leave.service.ICallcenterLeaveVisitRecordService;
import com.gobon.project.noanswer.domain.CallcenterNoanswerVisitRecord;
import com.gobon.project.noanswer.service.ICallcenterNoanswerService;
import com.gobon.project.noanswer.service.ICallcenterNoanswerVisitRecordService;
import com.gobon.project.quality.service.IQualityService;
import com.gobon.project.queue.AudioHandle;
import com.gobon.project.queue.AudioHandleQueue;
import com.gobon.project.queue.domain.AudioHandleParam;
import com.gobon.project.system.domain.SysSkillGroup;
import com.gobon.project.system.domain.SysUser;
import com.gobon.project.system.domain.entity.SysSkillGroupEntity;
import com.gobon.project.system.mapper.SysSkillGroupMapper;
import com.gobon.project.system.service.ICallcenterFreeswitchOptionService;
import com.gobon.project.system.service.ISysConfigService;
import com.gobon.project.system.service.ISysUserService;
import com.gobontech.cc.callcenter.domain.mq.CallTransferMsg;
import com.gobontech.cc.callcenter.domain.mq.CallTransferResult;
import com.gobontech.cc.callcenter.domain.mq.MsgObj;
import com.gobontech.cc.callcenter.enums.CallTransferResultEnum;
import com.gobontech.cc.callcenter.enums.CallTypeEnum;
import com.gobontech.cc.callcenter.enums.QueueEnum;
import com.gobontech.cc.callcenter.enums.TransferTypeEnum;
import com.gobontech.cc.fs.cdr.domain.Callflow;
import com.gobontech.cc.fs.cdr.domain.Cdr;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 呼入呼出通话记录主Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-03-24
 */
@Service
@Slf4j
public class CallcenterRecordServiceImpl extends ServiceImpl<CallcenterRecordMapper, CallcenterRecordEntity> implements ICallcenterRecordService {
    @Resource
    private CallcenterRecordMapper callcenterRecordMapper;
    @Resource
    private CallcenterRecordDetailMapper callcenterRecordDetailMapper;
    @Resource
    private ICallcenterRecordDetailService callcenterRecordDetailService;
    @Resource
    private ISysUserService sysUserService;
    @Resource
    private MessageProducer messageProducer;
    @Resource
    private ICallcenterFreeswitchOptionService callcenterFreeswitchOptionService;
    @Resource
    private ICallcenterCustomerService callcenterCustomerService;
    @Resource
    private ICallcenterVisitRecordService visitRecordService;
    @Resource
    private ICallcenterNoanswerVisitRecordService noanswerVisitRecordService;
    @Resource
    private ICallcenterLeaveVisitRecordService leaveVisitRecordService;
    @Resource
    private ICallcenterLeaveMessageService leaveMessageService;
    @Resource
    private ICallcenterCalloutTargetsService calloutTargetsService;
    @Resource
    private ICallcenterNoanswerService noanswerService;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private ICallcenterRecordMonitorService callcenterRecordMonitorService;

    @Autowired
    private CallSender callSender;

    @Autowired
    private AudioHandleQueue executor;

    @Autowired
    private SysSkillGroupMapper sysSkillGroupMapper;

    @Autowired
    private CallcenterCalloutTargetsMapper callcenterCalloutTargetsMapper;

    @Autowired
    private RedisCache redisCache;
    @Autowired
    private RedissonLocker redissonLocker;

    @Autowired
    private RedisSequenceFactory redisSequenceFactory;

    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private AmqpTemplate amqpTemplate;

    @Resource
    @Lazy
    private LocalAsrVoiceService localAsrVoiceService;

    @Resource
    private LLMApiUtil llmApiUtil;

    @Resource
    private CallcenterRecordDetailExtMapper callcenterRecordDetailExtMapper;

    @Resource
    private IQualityService qualityService;


    /**
     * 查询呼入呼出通话记录主
     *
     * @param id 呼入呼出通话记录主ID
     * @return 呼入呼出通话记录主
     */
    @Override
    public CallcenterRecord selectCallcenterRecordById(Long id) {
        return callcenterRecordMapper.selectCallcenterRecordById(id);
    }

    /**
     * 查询呼入呼出通话记录主列表
     *
     * @param callcenterRecord 呼入呼出通话记录主
     * @return 呼入呼出通话记录主
     */
    @Override
    public List<CallcenterRecord> selectCallcenterRecordList(CallcenterRecord callcenterRecord) {
        return callcenterRecordMapper.selectCallcenterRecordList(callcenterRecord);
    }

    /**
     * 新增呼入/呼出通话记录主
     *
     * @param callcenterRecordParam 参数
     * @param recordType            参数
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertCallcenterRecord(CallcenterRecordParam callcenterRecordParam, CallCenterRecordEnum.RecordType recordType) {
        CallcenterRecordDetail callcenterRecordDetail = null;
        if (StringUtils.isNotBlank(callcenterRecordParam.getTraceId())) {
            callcenterRecordDetail = callcenterRecordDetailMapper.selectCallcenterRecordByTraceId(callcenterRecordParam.getTraceId());
        }
        SysUser sysUser = null;
        try {
            sysUser = CommonUtils.getInstance().getSysUser();
        } catch (Exception e) {
            log.error("获取用户信息为空", e);
        }

        if (callcenterRecordParam.getProvince() != null && "直辖市".equals(callcenterRecordParam.getProvince())) {
            callcenterRecordParam.setProvince(callcenterRecordParam.getCity());
        }
        // 被叫
        String userPhone = callcenterRecordParam.getCalledPhone().trim();
        if (callcenterRecordDetail != null) {
            CallcenterRecordDetailEntity detailEntity = new CallcenterRecordDetailEntity();
            detailEntity.setId(callcenterRecordDetail.getId());
            if (callcenterRecordParam.getProvince() != null && !"".equals(callcenterRecordParam.getProvince())) {
                detailEntity.setProvince(callcenterRecordParam.getProvince());
                if ("直辖市".equals(detailEntity.getProvince())) {
                    detailEntity.setProvince(callcenterRecordParam.getCity());
                }
                detailEntity.setCity(callcenterRecordParam.getCity());
                detailEntity.setIsp(callcenterRecordParam.getIsp());
                detailEntity.setCityCode(callcenterRecordParam.getCityCode());
                detailEntity.setUserPhone(userPhone);
                //设置默认可查询出的国家为中国
                detailEntity.setCountry("中国");
                log.info("更新数据：{}", detailEntity);
                callcenterRecordDetailService.updateById(detailEntity);
            }
//            else if (StringUtils.isBlank(callcenterRecordDetail.getSound()) && StringUtils.isBlank(callcenterRecordDetail.getUserName())) {
//                CallcenterRecordDetailEntity detailEntity = new CallcenterRecordDetailEntity();
//                detailEntity.setId(callcenterRecordDetail.getId());
//                // 录音文件
//                if (StringUtils.isNotEmpty(callcenterRecordParam.getRecordPath()) && StringUtils.isNotEmpty(callcenterRecordParam.getRecordFile())) {
//                    detailEntity.setSound(callcenterRecordParam.getRecordPath() + callcenterRecordParam.getRecordFile() + CdrVariableConst.VAR_SOUND_SURFFIX);
//                }
//                detailEntity.setUserId(sysUser.getUserId());
//                detailEntity.setUserName(sysUser.getNickName());
//                detailEntity.setUserNumber(sysUser.getUserNumber());
//                detailEntity.setUserNumberphone(sysUser.getFreeswitchPhone());
//                detailEntity.setUserPhone(userPhone);
//                detailEntity.setDeptId(String.valueOf(sysUser.getDeptId()));
//                // 是固定号码
//                if (callcenterRecordParam.getProvince() != null && !"".equals(callcenterRecordParam.getProvince())) {
//                    detailEntity.setProvince(callcenterRecordParam.getProvince());
//                    detailEntity.setCity(callcenterRecordParam.getCity());
//                    detailEntity.setIsp(callcenterRecordParam.getIsp());
//                    detailEntity.setCityCode(callcenterRecordParam.getCityCode());
//                    //设置默认可查询出的国家为中国
//                    detailEntity.setCountry("中国");
//                }
//
//                detailEntity.setTargetId(callcenterRecordParam.getTargetsId());
//                detailEntity.setNoanswerId(callcenterRecordParam.getNoanswerId());
//                detailEntity.setLeaveId(callcenterRecordParam.getLeaveId());
//                log.info("更新数据：{}", detailEntity);
//                callcenterRecordDetailService.updateById(detailEntity);
//            }
//            return callcenterRecordDetail.getId();
            return callcenterRecordDetail.getId();
        }
        if (StringUtils.isNotBlank(callcenterRecordParam.getTraceId())) {
            redisCache.setCacheObject(Constants.INSERT_LOCK + callcenterRecordParam.getTraceId(), 1, 1, TimeUnit.MINUTES);
        }
        // 联系人号码
        String phone;
        if (CallCenterRecordEnum.RecordType.IN.equals(recordType)) {
            phone = callcenterRecordParam.getDialingPhone();
        } else {
            phone = callcenterRecordParam.getCalledPhone();
        }

        // 主叫
        String callPhone = callcenterRecordParam.getDialingPhone();

        // 不是固定号码
        if (callcenterRecordParam.getProvince() == null || "".equals(callcenterRecordParam.getProvince())) {
            if (phone.indexOf("00") == 0) {
                phone = phone.substring(phone.indexOf("00") + 2);
            } else if (phone.indexOf("0") == 0) {
                phone = phone.substring(phone.indexOf("0") + 1);
            }
        }

        CallcenterRecord callcenterRecord = new CallcenterRecord();
        Date callTime = null;
        if (StringUtils.isNotEmpty(callcenterRecordParam.getCallTime())) {
            callTime = DateUtils.parseDate(callcenterRecordParam.getCallTime());
        } else {
            callTime = new Date();
        }
        callcenterRecord.setId(callcenterRecordParam.getId());
        callcenterRecord.setCallTime(callTime);
        callcenterRecord.setCallPhone(callPhone);
        callcenterRecord.setUserPhone(userPhone);
        callcenterRecord.setIvrSkillGroupId(callcenterRecordParam.getSkillGroupId());
        callcenterRecord.setTargetsId(callcenterRecordParam.getTargetsId());
        if (recordType.equals(CallCenterRecordEnum.RecordType.OUT)) {
            callcenterRecord.setBillNumber(BillNumber.createOrderNumber(BillNumber.Type.OUT));
            callcenterRecord.setRecordType(String.valueOf(CallCenterRecordEnum.RecordType.OUT.ordinal()));
        } else {
            callcenterRecord.setBillNumber(BillNumber.createOrderNumber(BillNumber.Type.IN));
            callcenterRecord.setRecordType(String.valueOf(CallCenterRecordEnum.RecordType.IN.ordinal()));
//            CallcenterRecord prevCallcenterRecord = callcenterRecordMapper.selectCallcenterRecordByCallPhone(callPhone, CallCenterRecordEnum.RecordType.IN.ordinal());
//            if (prevCallcenterRecord != null) {
//                callcenterRecord.setBeforeTime(prevCallcenterRecord.getCallTime());
//                callcenterRecord.setBeforeSecond((callcenterRecord.getCallTime().getTime() - callcenterRecord.getBeforeTime().getTime()) / 1000);
//            }
        }
        this.insertCallcenterRecordIn(callcenterRecord);

        callcenterRecordDetail = new CallcenterRecordDetail();
        callcenterRecordDetail.setId(callcenterRecordParam.getId());
        Long incr = redisSequenceFactory.incr(String.format(CallcenterConstant.BILL_NUMBER_INR_KEY, callcenterRecord.getBillNumber()), 5, TimeUnit.HOURS);
        callcenterRecordDetail.setBillNumber(
                callcenterRecord.getBillNumber()
                        + Constants.SPLIT_WHIPPTREE
                        + incr);
        callcenterRecordDetail.setRecordId(callcenterRecord.getId());
        callcenterRecordDetail.setType(String.valueOf(CallCenterRecordEnum.RecordDetail.NORMAL.value()));
        callcenterRecordDetail.setCallPhone(callcenterRecord.getCallPhone());
        callcenterRecordDetail.setCallTime(callcenterRecord.getCallTime());
        callcenterRecordDetail.setRecordType(Integer.parseInt(callcenterRecord.getRecordType()));

        if (sysUser != null) {
            callcenterRecordDetail.setUserId(sysUser.getUserId());
            callcenterRecordDetail.setUserName(sysUser.getNickName());
            callcenterRecordDetail.setUserNumber(sysUser.getUserNumber());
            callcenterRecordDetail.setUserNumberphone(sysUser.getFreeswitchPhone());
            callcenterRecordDetail.setDeptId(String.valueOf(sysUser.getDeptId()));
            callcenterRecordDetail.setDomain(sysUser.getDomain());
        } else {
            callcenterRecordDetail.setUserId(1000000L);
            callcenterRecordDetail.setUserName("智能外呼机器人");
            callcenterRecordDetail.setUserNumber("999999");
            callcenterRecordDetail.setUserNumberphone("9999");
            callcenterRecordDetail.setDeptId("100");
            callcenterRecordDetail.setDomain(ConfigCommonConstants.domain);
        }
        callcenterRecordDetail.setUserPhone(callcenterRecord.getUserPhone());
        callcenterRecordDetail.setTargetId(callcenterRecordParam.getTargetsId());
        callcenterRecordDetail.setNoanswerId(callcenterRecordParam.getNoanswerId());
        callcenterRecordDetail.setLeaveId(callcenterRecordParam.getLeaveId());

        // 录音文件
        if (StringUtils.isNotEmpty(callcenterRecordParam.getRecordPath()) && StringUtils.isNotEmpty(callcenterRecordParam.getRecordFile())) {
            callcenterRecordDetail.setSound(callcenterRecordParam.getRecordPath() + callcenterRecordParam.getRecordFile() + CdrVariableConst.VAR_SOUND_SURFFIX);
        }

        // 默认状态改成客服未接听
        callcenterRecordDetail.setState(String.valueOf(CallCenterRecordEnum.CallStatus.NO_CONNECT.value()));
        callcenterRecordDetail.setEndPerson("2");
        if (CallCenterRecordEnum.RecordType.IN.equals(recordType)) {
            callcenterRecordDetail.setAnswerState(String.valueOf(CallCenterRecordEnum.AnswerStatus.CUSTOMER_SERVICE_NO_CONNECT.value()));
        } else {
            callcenterRecordDetail.setAnswerState(String.valueOf(CallCenterRecordEnum.AnswerStatus.NO_CONNECT.value()));
        }

        // 是固定号码
        if (callcenterRecordParam.getProvince() != null && !"".equals(callcenterRecordParam.getProvince())) {
            callcenterRecordDetail.setProvince(callcenterRecordParam.getProvince());
            callcenterRecordDetail.setCity(callcenterRecordParam.getCity());
            callcenterRecordDetail.setIsp(callcenterRecordParam.getIsp());
            callcenterRecordDetail.setCityCode(callcenterRecordParam.getCityCode());
            //设置默认可查询出的国家为中国
            callcenterRecordDetail.setCountry("中国");
        }

        callcenterRecordDetail.setTraceId(callcenterRecordParam.getTraceId());
        callcenterRecordDetail.setFreeswitchDetailId(callcenterRecordParam.getDetailId());
        callcenterRecordDetail.setIvrSkillGroupId(callcenterRecord.getIvrSkillGroupId());
        if (callcenterRecordDetail.getIvrSkillGroupId() != null) {
            SysSkillGroup sysSkillGroup = sysSkillGroupMapper.selectSysSkillGroupById(callcenterRecordDetail.getIvrSkillGroupId());
            callcenterRecordDetail.setIvrSkillGroupName(sysSkillGroup.getGroupName());
        }
        // 真实号码
        callcenterRecordDetail.setOriginalCallTelephone(callcenterRecordParam.getOriginalCallTelephone());
        //警情id
        callcenterRecordDetail.setPoliceId(callcenterRecordParam.getPoliceId());
        log.info("更新数据-插入数据：{}", callcenterRecordDetail);
        callcenterRecordDetailService.insertCallcenterRecordDetail(callcenterRecordDetail);
        callcenterRecordDetailService.setTraceTime(callcenterRecordDetail.getTraceId());
        //插入外呼呼叫记录表
        if (callcenterRecordParam.getTargetsId() != null) {
            CallcenterVisitRecord visitRecord = new CallcenterVisitRecord();
            visitRecord.setBusinessId(callcenterRecordDetail.getId());
            visitRecord.setBusinessType(1);
            visitRecord.setVisitTime(callcenterRecord.getCallTime());
            if (sysUser != null) {
                visitRecord.setVisitUserId(sysUser.getUserId());
                visitRecord.setVisitUserName(sysUser.getNickName());
            }
            visitRecord.setTargetId(callcenterRecordParam.getTargetsId());
            visitRecord.setCreateTime(new Date());
            CallcenterCalloutTargetsVo targets = calloutTargetsService.selectCallcenterCalloutTargetsById(callcenterRecordParam.getTargetsId());
            if (targets != null) {
                visitRecord.setDialplanId(targets.getDialplan());
            }
            visitRecordService.save(visitRecord);
        }

        //插入未接来电呼叫记录表
        if (callcenterRecordDetail.getNoanswerId() != null) {
            CallcenterNoanswerVisitRecord visitRecord = new CallcenterNoanswerVisitRecord();
            visitRecord.setBusinessId(callcenterRecordDetail.getId());
            visitRecord.setBusinessType(1);
            visitRecord.setVisitTime(callcenterRecord.getCallTime());
            if (sysUser != null) {
                visitRecord.setVisitUserId(sysUser.getUserId());
                visitRecord.setVisitUserName(sysUser.getNickName());
            }
            visitRecord.setNoanswerId(callcenterRecordDetail.getNoanswerId());
            visitRecord.setCreateTime(new Date());
            noanswerVisitRecordService.save(visitRecord);
        }

        //插入留言记录呼叫记录表
        if (callcenterRecordDetail.getLeaveId() != null) {
            CallcenterLeaveVisitRecord leaveVisitRecord = new CallcenterLeaveVisitRecord();
            leaveVisitRecord.setBusinessId(callcenterRecordDetail.getId());
            leaveVisitRecord.setBusinessType(1);
            leaveVisitRecord.setVisitTime(callcenterRecord.getCallTime());
            if (sysUser != null) {
                leaveVisitRecord.setVisitUserId(sysUser.getUserId());
                leaveVisitRecord.setVisitUserName(sysUser.getNickName());
            }
            leaveVisitRecord.setLeaveId(callcenterRecordDetail.getLeaveId());
            leaveVisitRecord.setCreateTime(new Date());
            leaveVisitRecordService.save(leaveVisitRecord);
        }
        return callcenterRecordDetail.getId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertCallcenterRecordDetail(CallcenterRecordOtherParam callcenterRecordParam, Boolean external, String mobilePhone) {
        // 主叫
        String callPhone;
        // 被叫
        String userPhone;
        // 被呼叫人号码（接起人）
        String calledPhone;
        if (CallCenterRecordEnum.RecordType.IN.name().equalsIgnoreCase(callcenterRecordParam.getCallType())) {
            callPhone = callcenterRecordParam.getDialingPhone();
            userPhone = callcenterRecordParam.getCalledPhone();
            calledPhone = userPhone;
        } else {
            callPhone = callcenterRecordParam.getCalledPhone();
            userPhone = callcenterRecordParam.getDialingPhone();
            calledPhone = callPhone;
        }

        SysUser sysUser = CommonUtils.getInstance().getSysUser();
        String traceId = callcenterRecordParam.getTraceId();
        CallcenterRecordDetail lastCallcenterRecordDetail = callcenterRecordDetailMapper.selectCallcenterRecordLastByTraceId(traceId);
        CallcenterRecordDetail insertCallcenterRecordDetail = new CallcenterRecordDetail();
        SysUser calledUser;
        if (external) {
            calledUser = sysUserService.selectUserByPhonenumber(mobilePhone);
        } else {
            calledUser = sysUserService.selectUserByFreeSwitchPhone(calledPhone);
        }
        if (lastCallcenterRecordDetail != null) {
            Date now = new Date();
            //CallcenterRecordDetail newCallcenterRecordDetail = callcenterRecordDetailMapper.selectCallcenterRecordDetailByRecordId(lastCallcenterRecordDetail.getRecordId());
            String[] billNumbers = StringUtils.split(lastCallcenterRecordDetail.getBillNumber(), Constants.SPLIT_WHIPPTREE);
            Long incr = redisSequenceFactory.incr(String.format(CallcenterConstant.BILL_NUMBER_INR_KEY, billNumbers[0]), 5, TimeUnit.HOURS);
            insertCallcenterRecordDetail.setBillNumber(billNumbers[0] + Constants.SPLIT_WHIPPTREE + incr);
            /*if (billNumbers != null && billNumbers.length == 2) {
                insertCallcenterRecordDetail.setBillNumber(billNumbers[0] + Constants.SPLIT_WHIPPTREE + (Integer.parseInt(billNumbers[1]) + 1));
            }*/
            insertCallcenterRecordDetail.setRecordId(lastCallcenterRecordDetail.getRecordId());
            if (StringUtils.isNotEmpty(callcenterRecordParam.getOperationType())) {
                // 转接/拦截/强拆
                insertCallcenterRecordDetail.setType(String.valueOf(CallCenterRecordEnum.RecordDetail.valueOf(callcenterRecordParam.getOperationType()).value()));
            }
            insertCallcenterRecordDetail.setTransferId(lastCallcenterRecordDetail.getTransferId());
            if (StringUtils.isNotEmpty(lastCallcenterRecordDetail.getState()) && String.valueOf(CallCenterRecordEnum.CallStatus.CALLING.value()).equals(lastCallcenterRecordDetail.getState())) {
                lastCallcenterRecordDetail.setState(String.valueOf(CallCenterRecordEnum.CallStatus.HANG_UP.value()));
                lastCallcenterRecordDetail.setOutTime(now);
                // 通话时长（挂断时间-接通时间）
                //double conversationTime = DateUtils.diff(lastCallcenterRecordDetail.getStartTime(),lastCallcenterRecordDetail.getOutTime());
                lastCallcenterRecordDetail.setConversationTime(String.valueOf(DateUtils.roundAndDiff(lastCallcenterRecordDetail.getStartTime(), lastCallcenterRecordDetail.getOutTime())));
            }
            insertCallcenterRecordDetail.setCallTime(lastCallcenterRecordDetail.getOutTime());
            if (lastCallcenterRecordDetail.getOutTime() == null) {
                insertCallcenterRecordDetail.setCallTime(lastCallcenterRecordDetail.getCallTime());
            }
            // 转接
            if (CallCenterRecordEnum.RecordDetail.TRANSFER.name().equalsIgnoreCase(callcenterRecordParam.getOperationType())) {
                // TODO 转接到客服组，分配时间和进入队列时间 ，实际需要重新对接，重新回到ivr队列
                Long skillGroupId = callcenterRecordParam.getSkillGroupId();
                if (skillGroupId != null && CallCenterRecordEnum.RecordType.IN.name().equalsIgnoreCase(callcenterRecordParam.getCallType())) {
                    insertCallcenterRecordDetail.setIvrSkillGroupId(skillGroupId);
                    SysSkillGroup sysSkillGroup = sysSkillGroupMapper.selectSysSkillGroupById(skillGroupId);
                    insertCallcenterRecordDetail.setIvrSkillGroupName(sysSkillGroup.getGroupName());
                    insertCallcenterRecordDetail.setDistributionTime(DateUtils.getNowDate());
//                    insertCallcenterRecordDetail.setOutIvrTime(insertCallcenterRecordDetail.getDistributionTime());
                }
                insertCallcenterRecordDetail.setTransferId(lastCallcenterRecordDetail.getId());
                lastCallcenterRecordDetail.setSwitchFlag(Constants.YES);
                lastCallcenterRecordDetail.setSwitchTime(now);
                if (lastCallcenterRecordDetail.getOutTime() != null) {
                    lastCallcenterRecordDetail.setSwitchTime(lastCallcenterRecordDetail.getOutTime());
                }
            }
            // 拦截
            if (CallCenterRecordEnum.RecordDetail.INTERCEPT.name().equalsIgnoreCase(callcenterRecordParam.getOperationType())) {
                lastCallcenterRecordDetail.setInterceptFlag(Constants.YES);
                lastCallcenterRecordDetail.setInterceptTime(now);
                if (lastCallcenterRecordDetail.getOutTime() != null) {
                    lastCallcenterRecordDetail.setInterceptTime(lastCallcenterRecordDetail.getOutTime());
                }
                lastCallcenterRecordDetail.setInterceptUserId(sysUser.getUserId());
                lastCallcenterRecordDetail.setInterceptUserName(sysUser.getNickName());
                if (StringUtils.isNotEmpty(lastCallcenterRecordDetail.getAnswerState()) && String.valueOf(CallCenterRecordEnum.AnswerStatus.NO_CONNECT.value()).equals(lastCallcenterRecordDetail.getAnswerState())) {
                    if (CallCenterRecordEnum.RecordType.IN.name().equalsIgnoreCase(callcenterRecordParam.getCallType())) {
                        lastCallcenterRecordDetail.setAnswerState(String.valueOf(CallCenterRecordEnum.AnswerStatus.CUSTOMER_SERVICE_NO_CONNECT.value()));
                    } else {
                        lastCallcenterRecordDetail.setAnswerState(String.valueOf(CallCenterRecordEnum.AnswerStatus.NO_ANSWER.value()));
                    }
                }
            }
            // 强拆
            if (CallCenterRecordEnum.RecordDetail.FORCED_DEMOLITION.name().equalsIgnoreCase(callcenterRecordParam.getOperationType())) {
                lastCallcenterRecordDetail.setForcedFlag(Constants.YES);
                lastCallcenterRecordDetail.setForcedTime(now);
                if (lastCallcenterRecordDetail.getOutTime() != null) {
                    lastCallcenterRecordDetail.setForcedTime(lastCallcenterRecordDetail.getOutTime());
                }
                lastCallcenterRecordDetail.setForcedUserId(sysUser.getUserId());
                lastCallcenterRecordDetail.setForcedUserName(sysUser.getNickName());
            }
            // 拦截/强拆
            if (String.valueOf(CallCenterRecordEnum.RecordDetail.TRANSFER.value()).equals(lastCallcenterRecordDetail.getType()) &&
                    (CallCenterRecordEnum.RecordDetail.INTERCEPT.name().equalsIgnoreCase(callcenterRecordParam.getOperationType())
                            || CallCenterRecordEnum.RecordDetail.FORCED_DEMOLITION.name().equalsIgnoreCase(callcenterRecordParam.getOperationType()))) {
                // 如果上一通为转接
                insertCallcenterRecordDetail.setTransferId(lastCallcenterRecordDetail.getId());
            }
            /*Long recordDetailId;
            if(String.valueOf(CallCenterRecordEnum.RecordDetail.NORMAL.value()).equals(lastCallcenterRecordDetail.getType())){
                recordDetailId = lastCallcenterRecordDetail.getId();
            } else {
                recordDetailId = callcenterRecordDetailMapper.selectCallcenterRecordByTraceId(traceId).getId();
            }*/
            insertCallcenterRecordDetail.setRecordDetailId(lastCallcenterRecordDetail.getId());
//            insertCallcenterRecordDetail.setDomain(lastCallcenterRecordDetail.getDomain());
            //修改为选择人的域，不拿上一通通话的域
            if (calledUser != null) {
                insertCallcenterRecordDetail.setDomain(calledUser.getDomain());
            } else {
                insertCallcenterRecordDetail.setDomain(lastCallcenterRecordDetail.getDomain());
            }
            lastCallcenterRecordDetail.setFreeswitchDetailId(null);
            // 更新上一通记录
            log.info("更新数据：{}", lastCallcenterRecordDetail);
            callcenterRecordDetailMapper.updateCallcenterRecordDetail(lastCallcenterRecordDetail);
        }
        insertCallcenterRecordDetail.setTraceId(callcenterRecordParam.getTraceId());
        insertCallcenterRecordDetail.setFreeswitchDetailId(callcenterRecordParam.getDetailId());
        insertCallcenterRecordDetail.setCallPhone(callPhone);
        insertCallcenterRecordDetail.setUserPhone(userPhone);
        if (calledUser != null) {
            insertCallcenterRecordDetail.setUserId(calledUser.getUserId());
            insertCallcenterRecordDetail.setUserName(calledUser.getNickName());
            insertCallcenterRecordDetail.setUserNumber(calledUser.getUserNumber());
            insertCallcenterRecordDetail.setUserNumberphone(calledUser.getFreeswitchPhone());
            insertCallcenterRecordDetail.setDeptId(String.valueOf(calledUser.getDeptId()));
        }

        // 录音文件
        if (StringUtils.isNotEmpty(callcenterRecordParam.getRecordPath()) && StringUtils.isNotEmpty(callcenterRecordParam.getRecordFile())) {
            insertCallcenterRecordDetail.setSound(callcenterRecordParam.getRecordPath() + callcenterRecordParam.getRecordFile() + CdrVariableConst.VAR_SOUND_SURFFIX);
        }

        // 联系人
        String customerPhone = "";
//        CallcenterRecord callcenterRecord = callcenterRecordDetailMapper.getByDetailId(lastCallcenterRecordDetail.getId());
//        if ("0".equals(callcenterRecord.getRecordType())) {
//            customerPhone = callcenterRecord.getCallPhone();
//        } else {
//            customerPhone = callcenterRecord.getUserPhone();
//        }

        // 根据手机号 查询 客户信息
//        CallcenterCustomerParam customerParam = new CallcenterCustomerParam();
//        customerParam.setPhone(customerPhone);
//        customerParam.setPersonId(sysUser.getUserId());
//        CallcenterCustomerVO callcenterCustomerVO = callcenterCustomerService.selectCallcenterCustomerByPhone(customerParam);
//
//        if (callcenterCustomerVO != null) {
//            insertCallcenterRecordDetail.setCustomerId(callcenterCustomerVO.getId());
//            insertCallcenterRecordDetail.setCustomerName(callcenterCustomerVO.getCustomerName());
//        }
//        log.info("************转接日志{},{}, {}", lastCallcenterRecordDetail.getRecordType(), lastCallcenterRecordDetail.getCallPhone(), insertCallcenterRecordDetail.getCallPhone());
        if (lastCallcenterRecordDetail.getRecordType() == 0) {
            if (!lastCallcenterRecordDetail.getCallPhone().equals(insertCallcenterRecordDetail.getCallPhone())) {
                insertCallcenterRecordDetail.setCallPhone(lastCallcenterRecordDetail.getCallPhone());
            }
        }

        insertCallcenterRecordDetail.setRecordType(lastCallcenterRecordDetail.getRecordType());
        // 呼入原始号码
        insertCallcenterRecordDetail.setOriginalCallTelephone(callcenterRecordParam.getOriginalCallTelephone());
        insertCallcenterRecordDetail.setAnswerState(String.valueOf(CallCenterRecordEnum.AnswerStatus.CUSTOMER_SERVICE_NO_CONNECT.value()));
        log.info("转接插入记录：{},{}", lastCallcenterRecordDetail, insertCallcenterRecordDetail);
        callcenterRecordDetailService.insertCallcenterRecordDetail(insertCallcenterRecordDetail);
        final Long id = insertCallcenterRecordDetail.getId();
//        sendCallRecord(callcenterRecord, lastCallcenterRecordDetail, BSTCallRecordMsgObj.HANG_UP);
        /*TransactionAfterCommitManager.getInstance().afterCommit(() -> {
            // 得到ID后   根据ID 异步插入当前记录的电话号码归属地
            AsyncManager.me().execute(AsyncFactory.updateNumberRegion(id));
            if(!external){
//                sendCallRecord(callcenterRecord,insertCallcenterRecordDetail,BSTCallRecordMsgObj.RING);
            }
        });*/
        return insertCallcenterRecordDetail.getId();
    }

    /**
     * 新增呼入/呼出通话记录主
     *
     * @param callcenterRecord 呼入呼出通话记录主
     * @return 结果
     */
    @Override
    public int insertCallcenterRecordIn(CallcenterRecord callcenterRecord) {
        return callcenterRecordMapper.insertCallcenterRecord(callcenterRecord);
    }

    /**
     * 修改呼入呼出通话记录主
     *
     * @param callcenterRecord 呼入呼出通话记录主
     * @return 结果
     */
    @Override
    public int updateCallcenterRecord(CallcenterRecord callcenterRecord) {
        return callcenterRecordMapper.updateCallcenterRecord(callcenterRecord);
    }

    /**
     * 更新呼出电话-接通
     *
     * @param callcenterRecordParam 参数
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateCallcenterRecordCallId(CallcenterRecordDetailParam callcenterRecordParam) {
//        CallcenterRecordDetail dbCallcenterRecordDetail = callcenterRecordDetailMapper.selectCallcenterRecordById(callcenterRecordParam.getRecordDetailId());
//        // 接通后记住号码接通用户信息
//        if ("1".equals(sysConfigService.selectConfigByKey("oldUserInfoSwitch"))) {
//            String oldUserInfoTime = sysConfigService.selectConfigByKey("oldUserInfoTime");
//            redisCache.setCacheObject(Constants.OLD_USER_INFO + dbCallcenterRecordDetail.getUserPhone(), dbCallcenterRecordDetail.getCallPhone(),
//                    Integer.parseInt(StringUtils.isNotBlank(oldUserInfoTime) ? oldUserInfoTime : "2"), TimeUnit.HOURS);
//        }
//        // 若外呼任务拨打则更新外呼任务客户表信息
//        if (dbCallcenterRecordDetail != null && dbCallcenterRecordDetail.getTargetId() != null) {
//            CallcenterCalloutTargetsVo targets = callcenterCalloutTargetsMapper.selectCallcenterCalloutTargetsById(dbCallcenterRecordDetail.getTargetId());
//            targets.setSuccess(targets.getSuccess() == null ? 1 : (targets.getSuccess() + 1));
//            targets.setSuccessLastTime(new Date());
//            callcenterCalloutTargetsMapper.updateCallcenterCalloutTargets(targets);
//        }
        return 1;
    }

    @Override
    public int updateCallcenterRecordMissed(Long recordDetailId) {
//        CallcenterRecordDetailVO callcenterRecordDetailVO = callcenterRecordDetailMapper.selectCallcenterRecordDetailById(recordDetailId);
//        if (callcenterRecordDetailVO != null) {
//            CallcenterRecordDetail updateCallcenterRecordDetail = new CallcenterRecordDetail();
//            updateCallcenterRecordDetail.setId(recordDetailId);
//            // 无人应答
//            updateCallcenterRecordDetail.setAnswerState(String.valueOf(CallCenterRecordEnum.AnswerStatus.NO_ANSWER.value()));
//            callcenterRecordDetailMapper.updateCallcenterRecordDetail(updateCallcenterRecordDetail);
//        }
        return 1;
    }

    @Override
    public int updateCallcenterRecordCancel(Long recordDetailId) {
//        CallcenterRecordDetailVO callcenterRecordDetailVO = callcenterRecordDetailMapper.selectCallcenterRecordDetailById(recordDetailId);
//        if (callcenterRecordDetailVO != null) {
//            CallcenterRecordDetail updateCallcenterRecordDetail = new CallcenterRecordDetail();
//            updateCallcenterRecordDetail.setId(recordDetailId);
//            // 无人应答
//            updateCallcenterRecordDetail.setAnswerState(String.valueOf(CallCenterRecordEnum.AnswerStatus.NO_ANSWER.value()));
//            callcenterRecordDetailMapper.updateCallcenterRecordDetail(updateCallcenterRecordDetail);
//        }
        return 1;
    }

    @Override
    public int updateHandleSecond(CallcenterHandleSecondParam callcenterHandleSecondParam) {
        CallcenterRecordDetail updateCallcenterRecordDetail = new CallcenterRecordDetail();
        updateCallcenterRecordDetail.setId(callcenterHandleSecondParam.getRecordDetailId());
        updateCallcenterRecordDetail.setHandleTime(new Date());
        updateCallcenterRecordDetail.setHandleSecond(callcenterHandleSecondParam.getHandleSecond());
        callcenterRecordDetailMapper.updateCallcenterRecordDetail(updateCallcenterRecordDetail);
        return 1;
    }

    /**
     * 电话-转接
     *
     * @param callcenterRecordParam 参数
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updateCallcenterRecordTransfer(CallcenterRecordParam callcenterRecordParam, TransferParam transferParam) {
        Long result = 1L;
        // MQ发消息给freeswitch服务器
        messageProducer.transferPhone(callcenterRecordParam, transferParam);
        if (callcenterRecordParam.getExternal()) {
            CallcenterRecordOtherParam callcenterRecordOtherParam = new CallcenterRecordOtherParam();
            CallcenterRecordDetail lastCallcenterRecordDetail = callcenterRecordDetailMapper.selectCallcenterRecordLastByTraceId(callcenterRecordParam.getTraceId());
            if (lastCallcenterRecordDetail != null) {
                if (CallCenterRecordEnum.RecordType.IN.name().equalsIgnoreCase(callcenterRecordParam.getCallType())) {
                    callcenterRecordOtherParam.setDialingPhone(lastCallcenterRecordDetail.getCallPhone());
                    callcenterRecordOtherParam.setCalledPhone(callcenterRecordParam.getOriginCalledPhone());
                } else {
                    callcenterRecordOtherParam.setCalledPhone(callcenterRecordParam.getOriginCalledPhone());
                    callcenterRecordOtherParam.setDialingPhone(lastCallcenterRecordDetail.getUserPhone());
                }
            }

            callcenterRecordOtherParam.setCallId(callcenterRecordParam.getCallId());
            callcenterRecordOtherParam.setTraceId(callcenterRecordParam.getTraceId());
            callcenterRecordOtherParam.setCallType(callcenterRecordParam.getCallType());
            callcenterRecordOtherParam.setOperationType(CallCenterRecordEnum.RecordDetail.TRANSFER.name());
            callcenterRecordOtherParam.setSkillGroupId(callcenterRecordParam.getCallSkillGroupId());
            result = this.insertCallcenterRecordDetail(callcenterRecordOtherParam, true, callcenterRecordParam.getOriginCalledPhone());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateCallcenterRecordTransferPhone(CallTransferResult callTransferResult) {
        CallTransferResultEnum callTransferResultEnum = callTransferResult.getResult();
        CallTransferMsg callTransferMsg = callTransferResult.getCallTransfer();
        CallcenterRecordDetail lastCallcenterRecordDetail = callcenterRecordDetailMapper.selectCallcenterRecordLastByTraceId(callTransferMsg.getTraceId());
        if (lastCallcenterRecordDetail != null && lastCallcenterRecordDetail.getFreeswitchDetailId() == null) {
            // 录音文件
            if (StringUtils.isNotEmpty(callTransferMsg.getRecordPath()) && StringUtils.isNotEmpty(callTransferMsg.getRecordFile())) {
                lastCallcenterRecordDetail.setSound(callTransferMsg.getRecordPath() + callTransferMsg.getRecordFile() + CdrVariableConst.VAR_SOUND_SURFFIX);
            }
            lastCallcenterRecordDetail.setFreeswitchDetailId(callTransferMsg.getDetailId());
            lastCallcenterRecordDetail.setState(String.valueOf(CallCenterRecordEnum.CallStatus.HANG_UP.value()));
            CallcenterRecord updateCallcenterRecord = callcenterRecordMapper.selectCallcenterRecordById(lastCallcenterRecordDetail.getRecordId());
            if (CallTransferResultEnum.SUCCESS.equals(callTransferResultEnum)) {
                lastCallcenterRecordDetail.setAnswerState(String.valueOf(CallCenterRecordEnum.AnswerStatus.CONNECT.value()));
                lastCallcenterRecordDetail.setStartTime(new Date(Long.valueOf(String.valueOf(callTransferMsg.getAnswerUepoch())) / 1000));
                lastCallcenterRecordDetail.setStartTime(DateUtils.dateWithNoNano(lastCallcenterRecordDetail.getStartTime()));
                lastCallcenterRecordDetail.setOutTime(new Date(Long.valueOf(String.valueOf(callTransferMsg.getEndUepoch())) / 1000));
                lastCallcenterRecordDetail.setOutTime(DateUtils.dateWithNoNano(lastCallcenterRecordDetail.getOutTime()));
                // 通话时长（挂断时间-接通时间）
                //double conversationTime = DateUtils.diff(lastCallcenterRecordDetail.getStartTime(),lastCallcenterRecordDetail.getOutTime());
                lastCallcenterRecordDetail.setConversationTime(String.valueOf(DateUtils.roundAndDiff(lastCallcenterRecordDetail.getStartTime(), lastCallcenterRecordDetail.getOutTime())));

                if (updateCallcenterRecord.getOutTime() == null) {
                    updateCallcenterRecord.setOutTime(lastCallcenterRecordDetail.getOutTime());
                }
                callcenterRecordMapper.updateCallcenterRecord(updateCallcenterRecord);
            } else {
                if ("1".equals(lastCallcenterRecordDetail.getRecordType())) {
                    lastCallcenterRecordDetail.setAnswerState(String.valueOf(CallCenterRecordEnum.AnswerStatus.NO_CONNECT.value()));
                } else {
                    lastCallcenterRecordDetail.setAnswerState(String.valueOf(CallCenterRecordEnum.AnswerStatus.CUSTOMER_SERVICE_NO_CONNECT.value()));
                }
            }
            if (lastCallcenterRecordDetail.getStartTime() != null && lastCallcenterRecordDetail.getOutTime() != null
                    && lastCallcenterRecordDetail.getStartTime().after(lastCallcenterRecordDetail.getOutTime())) {
                lastCallcenterRecordDetail.setOutTime(DateUtils.dateMoveTimeSec(lastCallcenterRecordDetail.getStartTime(), 1000L));
            }
            log.info("更新数据：{}", lastCallcenterRecordDetail);
            callcenterRecordDetailMapper.updateCallcenterRecordDetail(lastCallcenterRecordDetail);
            // 推送数据（百事通）
            TransactionAfterCommitManager.getInstance().afterCommit(() -> {
                sendCallRecord(updateCallcenterRecord, lastCallcenterRecordDetail, BSTCallRecordMsgObj.HANG_UP);
            });
        }
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateStartEavesdrop(String traceId) {
        CallcenterRecordDetail updateCallcenterRecordDetail = callcenterRecordDetailMapper.selectCallcenterRecordLastByTraceId(traceId);
        if (updateCallcenterRecordDetail != null) {
            SysUser sysUser = CommonUtils.getInstance().getSysUser();
            updateCallcenterRecordDetail.setMonitorFlag(Constants.YES);
            updateCallcenterRecordDetail.setMonitorStartTime(new Date());
            updateCallcenterRecordDetail.setMonitorEndTime(null);
            updateCallcenterRecordDetail.setMonitorUserId(sysUser.getUserId());
            updateCallcenterRecordDetail.setMonitorUserName(sysUser.getNickName());
            callcenterRecordDetailMapper.updateCallcenterRecordDetailMonitor(updateCallcenterRecordDetail);

            // 监听记录
            CallcenterRecordMonitor callcenterRecordMonitor = new CallcenterRecordMonitor();
            callcenterRecordMonitor.setRecordDetailId(updateCallcenterRecordDetail.getId());
            callcenterRecordMonitor.setMonitorUserId(sysUser.getUserId());
            callcenterRecordMonitor.setMonitorUserName(sysUser.getNickName());
            threadPoolTaskExecutor.execute(() -> callcenterRecordMonitorService.addCallcenterRecordMonitor(callcenterRecordMonitor));
        }
        return 1;
    }

    @Override
    public int updateEndEavesdrop(String traceId) {
        CallcenterRecordDetail updateCallcenterRecordDetail = callcenterRecordDetailMapper.selectCallcenterRecordLastMonitorByTraceId(traceId);
        if (updateCallcenterRecordDetail != null) {
            updateCallcenterRecordDetail.setMonitorEndTime(new Date());
            callcenterRecordDetailMapper.updateCallcenterRecordDetailMonitor(updateCallcenterRecordDetail);

            // 监听记录
            CallcenterRecordMonitor callcenterRecordMonitor = new CallcenterRecordMonitor();
            callcenterRecordMonitor.setRecordDetailId(updateCallcenterRecordDetail.getId());
            callcenterRecordMonitorService.updateCallcenterRecordMonitor(callcenterRecordMonitor);
        }
        return 1;
    }

    /**
     * 更新客服接通电话信息
     *
     * @param callcenterRecordParam 参数
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateCallcenterRecord(CallcenterRecordParam callcenterRecordParam) {
        log.info("接听电话：{}", callcenterRecordParam);
        CallcenterRecordDetail callcenterRecordDetail = callcenterRecordDetailMapper.selectCallcenterRecordById(callcenterRecordParam.getRecordDetailId());
        if (callcenterRecordDetail != null) {
            // 接通后记住号码接通用户信息
            if ("1".equals(sysConfigService.selectConfigByKey("oldUserInfoSwitch"))) {
                String oldUserInfoTime = sysConfigService.selectConfigByKey("oldUserInfoTime");
                if (String.valueOf(CallCenterRecordEnum.RecordType.IN.ordinal()).equals(callcenterRecordDetail.getRecordType().toString())) {
                    redisCache.setCacheObject(Constants.OLD_USER_INFO + callcenterRecordDetail.getCallPhone(), callcenterRecordDetail.getUserPhone(),
                            Integer.parseInt(StringUtils.isNotBlank(oldUserInfoTime) ? oldUserInfoTime : "2"), TimeUnit.HOURS);
                } else {
                    redisCache.setCacheObject(Constants.OLD_USER_INFO + callcenterRecordDetail.getUserPhone(), callcenterRecordDetail.getCallPhone(),
                            Integer.parseInt(StringUtils.isNotBlank(oldUserInfoTime) ? oldUserInfoTime : "2"), TimeUnit.HOURS);
                }

            }
//            Date now = new Date();
//            // 主叫
//            String callPhone;
//            // 被叫
//            String userPhone;
//            if (CallCenterRecordEnum.RecordType.IN.name().equalsIgnoreCase(callcenterRecordParam.getCallType())) {
//                callPhone = AesUtil.desEncrypt(callcenterRecordParam.getDialingPhone());
//                userPhone = callcenterRecordParam.getCalledPhone();
//            } else {
//                callPhone = callcenterRecordParam.getCalledPhone();
//                userPhone = callcenterRecordParam.getDialingPhone();
//            }
//            CallcenterRecord updateCallcenterRecord = callcenterRecordMapper.selectCallcenterRecordById(callcenterRecordDetail.getRecordId());
//            if (updateCallcenterRecord != null) {
//                if (updateCallcenterRecord.getStartTime() == null) {
//                    updateCallcenterRecord.setStartTime(now);
//                }
//                this.updateCallcenterRecord(updateCallcenterRecord);
//            }
//
//            CallcenterRecordDetail updateCallcenterRecordDetail = new CallcenterRecordDetail();
//            updateCallcenterRecordDetail.setId(callcenterRecordDetail.getId());
//            SysUser sysUser = CommonUtils.getInstance().getSysUser();
//            updateCallcenterRecordDetail.setUserId(sysUser.getUserId());
//            updateCallcenterRecordDetail.setUserName(sysUser.getNickName());
//            updateCallcenterRecordDetail.setUserNumber(sysUser.getUserNumber());
//            updateCallcenterRecordDetail.setUserNumberphone(sysUser.getFreeswitchPhone());
//            updateCallcenterRecordDetail.setDeptId(String.valueOf(sysUser.getDeptId()));
//            updateCallcenterRecordDetail.setCallPhone(callPhone);
//            updateCallcenterRecordDetail.setUserPhone(userPhone);
//            log.info("更新数据：{}", updateCallcenterRecordDetail);
//            callcenterRecordDetailService.updateCallcenterRecordDetail(updateCallcenterRecordDetail);
        }
        return 1;
    }


    /**
     * IVR语音导航中挂断
     */
    private CallcenterRecordDetail insertIvrHangUp(Cdr cdr) {
        Map<String, Object> variables = cdr.getVariables().getVariableTable();
        String callUuid = String.valueOf(variables.get(CdrVariableConst.VAR_CALL_UUID));
        String traceId = String.valueOf(variables.get(CdrVariableConst.VAR_TRACE_ID));
        // 唯一detailid
        String sipCallId = String.valueOf(variables.get(CdrVariableConst.SIP_CALLID));
        // 唯一detailid
        //String sipDetailId = String.valueOf(variables.get(CdrVariableConst.SIP_DETAIL_ID));
        // 呼入原始号码
        String sipToUser = String.valueOf(variables.get(CdrVariableConst.SIP_TO_USER));
        String freeswitchDetailId = "";
        Date outTime = new Date(Long.parseLong(String.valueOf(variables.get(CdrVariableConst.VAR_END_TIME))) / 1000);
        // 主叫/被叫号码
        String phone = cdr.getCallflows().get(0).getCallerProfile().getCallerIdNumber();
        // 不是固定号码
        if (phone.indexOf("00") == 0) {
            phone = phone.substring(phone.indexOf("00") + 2);
        } else if (phone.indexOf("0") == 0) {
            phone = phone.substring(phone.indexOf("0") + 1);
        }

        CallcenterRecord callcenterRecord = new CallcenterRecord();
        callcenterRecord.setCallPhone(phone);
        // 拨打时间
        callcenterRecord.setCallTime(new Date(Long.valueOf(String.valueOf(variables.get(CdrVariableConst.VAR_START_TIME))) / 1000));
        callcenterRecord.setBillNumber(BillNumber.createOrderNumber(BillNumber.Type.IN));
        callcenterRecord.setRecordType(String.valueOf(CallCenterRecordEnum.RecordType.IN.ordinal()));
        CallcenterRecord prevCallcenterRecord = callcenterRecordMapper.selectCallcenterRecordByCallPhone(phone, CallCenterRecordEnum.RecordType.IN.ordinal());
        if (prevCallcenterRecord != null) {
            callcenterRecord.setBeforeTime(prevCallcenterRecord.getCallTime());
            callcenterRecord.setBeforeSecond((callcenterRecord.getCallTime().getTime() - callcenterRecord.getBeforeTime().getTime()) / 1000);
        }
        callcenterRecord.setIvrState(Boolean.valueOf(String.valueOf(variables.get(CdrVariableConst.ENTER_IVR))));
        this.insertCallcenterRecordIn(callcenterRecord);

        CallcenterRecordDetail callcenterRecordDetail = new CallcenterRecordDetail();
        callcenterRecordDetail.setBillNumber(callcenterRecord.getBillNumber() + Constants.SPLIT_WHIPPTREE + "1");
        callcenterRecordDetail.setRecordId(callcenterRecord.getId());
        callcenterRecordDetail.setType(String.valueOf(CallCenterRecordEnum.RecordDetail.NORMAL.value()));
        callcenterRecordDetail.setCallPhone(callcenterRecord.getCallPhone());
        // 拨打时间
        callcenterRecordDetail.setCallTime(callcenterRecord.getCallTime());
        callcenterRecordDetail.setState(String.valueOf(CallCenterRecordEnum.CallStatus.IVR.value()));
        callcenterRecordDetail.setAnswerState(String.valueOf(CallCenterRecordEnum.AnswerStatus.NO_CONNECT.value()));

        callcenterRecordDetail.setFreeswitchCallId(callUuid);
        callcenterRecordDetail.setTraceId(traceId);
        callcenterRecordDetail.setRecordType(CallCenterRecordEnum.RecordType.IN.ordinal());
        callcenterRecordDetail.setOutTime(outTime);


        if (StringUtils.isNotEmptyOrNullStr(sipCallId)) {
            try {
                freeswitchDetailId = URLDecoder.decode(sipCallId, "utf-8");
            } catch (UnsupportedEncodingException e) {
                log.error("获取freeswitchDetailId异常", e);
            }
        }
        callcenterRecordDetail.setFreeswitchDetailId(freeswitchDetailId);
        callcenterRecordDetail.setOriginalCallTelephone(StringUtils.isEmptyOrNullStr(sipToUser) ? "" : sipToUser);
        if (StringUtils.isNotBlank(callcenterRecordDetail.getOriginalCallTelephone())) {
            if (redisCache.getCacheMapValue(Constants.INIT_DIRECT_INTO_SKILL, callcenterRecordDetail.getOriginalCallTelephone()) != null) {
                callcenterRecordDetail.setIvrSkillGroupId(redisCache.getCacheMapValue(Constants.INIT_DIRECT_INTO_SKILL, callcenterRecordDetail.getOriginalCallTelephone()));
            }
        }
        log.info("更新数据-插入数据-cdr：{}", callcenterRecordDetail);
        callcenterRecordDetailService.insertCallcenterRecordDetail(callcenterRecordDetail);
        return callcenterRecordDetail;
    }

    private void updateCallingOut(CallcenterRecordDetail callcenterRecordDetail, Cdr cdr) {
        Map<String, Object> variables = cdr.getVariables().getVariableTable();
        CallcenterRecord callcenterRecord = callcenterRecordMapper.selectCallcenterRecordById(callcenterRecordDetail.getRecordId());
        CallcenterRecord updateCallcenterRecord = new CallcenterRecord();
        updateCallcenterRecord.setId(0L);
        if (callcenterRecordDetail != null) {
            updateCallcenterRecord.setId(callcenterRecordDetail.getRecordId());
        }

        CallcenterRecordDetail updaCallcenterRecordDetail = new CallcenterRecordDetail();
        updaCallcenterRecordDetail.setId(callcenterRecordDetail.getId());
        updaCallcenterRecordDetail.setCallTime(callcenterRecordDetail.getCallTime());
        if (callcenterRecordDetail.getCallTime() == null) {
            // 拨打时间
            updaCallcenterRecordDetail.setCallTime(new Date(Long.parseLong(String.valueOf(variables.get(CdrVariableConst.VAR_START_TIME))) / 1000));
        }
        updaCallcenterRecordDetail.setStartTime(callcenterRecordDetail.getStartTime());
        if (callcenterRecordDetail.getStartTime() == null) {
            // 接通时间
            Long startTimeMilliseconds = Long.parseLong(String.valueOf(variables.get(CdrVariableConst.VAR_CONNECT_TIME))) / 1000;
            if (!startTimeMilliseconds.equals(0L)) {
                updaCallcenterRecordDetail.setStartTime(new Date(startTimeMilliseconds));
            }

        }
        updaCallcenterRecordDetail.setOutTime(callcenterRecordDetail.getOutTime());
        if (callcenterRecordDetail.getOutTime() == null) {
            // 挂断时间
            updaCallcenterRecordDetail.setOutTime(new Date(Long.parseLong(String.valueOf(variables.get(CdrVariableConst.VAR_END_TIME))) / 1000));
            updaCallcenterRecordDetail.setOutTime(DateUtils.dateWithNoNano(updaCallcenterRecordDetail.getOutTime()));
            if (updaCallcenterRecordDetail.getStartTime() != null && updaCallcenterRecordDetail.getOutTime() != null
                    && updaCallcenterRecordDetail.getStartTime().after(updaCallcenterRecordDetail.getOutTime())) {
                updaCallcenterRecordDetail.setOutTime(DateUtils.dateMoveTimeSec(updaCallcenterRecordDetail.getStartTime(), 1000L));
            }
        }
        // 通话时长（挂断时间-接通时间）
        //double conversationTime = DateUtils.diff(updaCallcenterRecordDetail.getStartTime(),updaCallcenterRecordDetail.getOutTime());
        updaCallcenterRecordDetail.setConversationTime(String.valueOf(DateUtils.roundAndDiff(updaCallcenterRecordDetail.getStartTime(), updaCallcenterRecordDetail.getOutTime())));
        updaCallcenterRecordDetail.setState(String.valueOf(CallCenterRecordEnum.CallStatus.HANG_UP.value()));
        if (updaCallcenterRecordDetail.getStartTime() != null) {
            // 接通
            updaCallcenterRecordDetail.setAnswerState(String.valueOf(CallCenterRecordEnum.AnswerStatus.CONNECT.value()));
        }
        if (updaCallcenterRecordDetail.getStartTime() != null && updaCallcenterRecordDetail.getOutTime() != null
                && updaCallcenterRecordDetail.getStartTime().after(updaCallcenterRecordDetail.getOutTime())) {
            updaCallcenterRecordDetail.setOutTime(DateUtils.dateMoveTimeSec(updaCallcenterRecordDetail.getStartTime(), 1000L));
        }
        callcenterRecordDetailMapper.updateCallcenterRecordDetail(updaCallcenterRecordDetail);

        updateCallcenterRecord.setCallTime(updaCallcenterRecordDetail.getCallTime());
        updateCallcenterRecord.setStartTime(updaCallcenterRecordDetail.getStartTime());
        updateCallcenterRecord.setOutTime(updaCallcenterRecordDetail.getOutTime());
        if (variables.get(CdrVariableConst.VAR_END_PERSON) != null) {
            updateCallcenterRecord.setEndPerson(String.valueOf(CallCenterRecordEnum.WhoHangUp.valueOf(String.valueOf(variables.get(CdrVariableConst.VAR_END_PERSON)).toUpperCase()).ordinal()));
        }
        callcenterRecordMapper.updateCallcenterRecord(updateCallcenterRecord);
        // 推送数据（百事通）
        //CallcenterRecordDetail dbCallcenterRecordDetail = callcenterRecordDetailMapper.selectCallcenterRecordById(updaCallcenterRecordDetail.getId());
        //BeanUtils.copyPropertiesIgnoreNull(updaCallcenterRecordDetail,dbCallcenterRecordDetail);
        //BeanUtils.copyPropertiesIgnoreNull(updateCallcenterRecord,callcenterRecord);
        TransactionAfterCommitManager.getInstance().afterCommit(() -> {
            sendCallRecord(callcenterRecord, updaCallcenterRecordDetail, BSTCallRecordMsgObj.HANG_UP);
        });
    }

    private void updateCallingIn(CallcenterRecordDetail callcenterRecordDetail, Cdr cdr, boolean firstPhone) {
        Map<String, Object> variables = cdr.getVariables().getVariableTable();
        // 取第二通
        CallcenterRecord updateCallcenterRecord = callcenterRecordMapper.selectCallcenterRecordById(callcenterRecordDetail.getRecordId());

        String tmpAnswerStatus = (String) variables.get(CdrVariableConst.VAR_ANSWER_STATUS);
        String sipToUser = String.valueOf(variables.get(CdrVariableConst.SIP_TO_USER));
        // 未接通
        CallCenterRecordEnum.AnswerStatus CONNECT = CallCenterRecordEnum.AnswerStatus.CONNECT;
        CallCenterRecordEnum.AnswerStatus UN_CONNECT = CallCenterRecordEnum.AnswerStatus.CUSTOMER_SERVICE_NO_CONNECT;
        CallCenterRecordEnum.AnswerStatus answerStatus = CdrVariableConst.SUCCESS.equals(tmpAnswerStatus) ? CallCenterRecordEnum.AnswerStatus.CONNECT : UN_CONNECT;
        CallcenterRecordDetail updaCallcenterRecordDetail = null;
        if (firstPhone) {
            updaCallcenterRecordDetail = new CallcenterRecordDetail();
            // 明细记录
            updaCallcenterRecordDetail.setId(callcenterRecordDetail.getId());
            CallcenterRecordDetailEntity byId = callcenterRecordDetailService.getById(callcenterRecordDetail.getId());
            updaCallcenterRecordDetail.setRecordId(callcenterRecordDetail.getRecordId());
            // 接通
            if (CONNECT.equals(answerStatus) && String.valueOf(CallCenterRecordEnum.AnswerStatus.CONNECT.value()).equals(callcenterRecordDetail.getAnswerState())) {
                // 接通时间（用客服接起时间）
                updaCallcenterRecordDetail.setStartTime(callcenterRecordDetail.getStartTime());
                if (callcenterRecordDetail.getStartTime() == null) {
                    updaCallcenterRecordDetail.setStartTime(new Date(Long.valueOf(String.valueOf(variables.get(CdrVariableConst.VAR_CONNECT_TIME))) / 1000));
                }

                // 挂断时间
                updaCallcenterRecordDetail.setOutTime(callcenterRecordDetail.getOutTime());
                if (callcenterRecordDetail.getOutTime() == null) {
                    updaCallcenterRecordDetail.setOutTime(new Date(Long.valueOf(String.valueOf(variables.get(CdrVariableConst.VAR_END_TIME))) / 1000));
                }
                updaCallcenterRecordDetail.setOutTime(DateUtils.dateWithNoNano(updaCallcenterRecordDetail.getOutTime()));

                // 通话时长（挂断时间-接通时间）
                //double conversationTime = DateUtils.diff(updaCallcenterRecordDetail.getStartTime(),updaCallcenterRecordDetail.getOutTime());
                updaCallcenterRecordDetail.setConversationTime(String.valueOf(DateUtils.roundAndDiff(updaCallcenterRecordDetail.getStartTime(), updaCallcenterRecordDetail.getOutTime())));
                updaCallcenterRecordDetail.setState(String.valueOf(CallCenterRecordEnum.CallStatus.HANG_UP.value()));
            }

            // 无人应答
            if (UN_CONNECT.equals(answerStatus) && byId.getAnswerState() == null && byId.getUserId() != null) {
                updaCallcenterRecordDetail.setState(String.valueOf(CallCenterRecordEnum.CallStatus.NO_CONNECT.value()));
                updaCallcenterRecordDetail.setAnswerState(String.valueOf(CallCenterRecordEnum.AnswerStatus.CUSTOMER_SERVICE_NO_CONNECT.value()));
            }
            if (byId.getUserId() == null && 0 == byId.getRecordType() && 0 == byId.getType()) {
                updaCallcenterRecordDetail.setAnswerState(String.valueOf(CallCenterRecordEnum.AnswerStatus.NO_CONNECT.value()));
                updaCallcenterRecordDetail.setEndPerson("0");
            }
            if (variables.get(CdrVariableConst.VAR_ASSIGN_TIME) != null && StringUtils.isNotBlank(variables.get(CdrVariableConst.VAR_ASSIGN_TIME).toString())
                    && byId.getDistributionTime() == null) {
                updaCallcenterRecordDetail.setDistributionTime(DateUtils.ignoreMs(new Date(Long.parseLong(String.valueOf(variables.get(CdrVariableConst.VAR_ASSIGN_TIME))) / 1000)));
            }
            if (variables.get(CdrVariableConst.VAR_ENQUEUE_TIMESTMAP) != null && StringUtils.isNotBlank(variables.get(CdrVariableConst.VAR_ENQUEUE_TIMESTMAP).toString())
                    && byId.getOutIvrTime() == null) {
                updaCallcenterRecordDetail.setOutIvrTime(DateUtils.ignoreMs(new Date(Long.parseLong(String.valueOf(variables.get(CdrVariableConst.VAR_ENQUEUE_TIMESTMAP))) / 1000)));
            }
            // 呼入原始号码
            if (StringUtils.isNotEmpty(callcenterRecordDetail.getOriginalCallTelephone()) && StringUtils.isBlank(byId.getOriginalCallTelephone())) {
                updaCallcenterRecordDetail.setOriginalCallTelephone(sipToUser);
            }
            // 判断数据正确性
            if (updaCallcenterRecordDetail.getStartTime() != null && updaCallcenterRecordDetail.getOutTime() != null
                    && updaCallcenterRecordDetail.getStartTime().after(updaCallcenterRecordDetail.getOutTime())) {
                updaCallcenterRecordDetail.setOutTime(DateUtils.dateMoveTimeSec(updaCallcenterRecordDetail.getStartTime(), 1000L));
            }

            if (updaCallcenterRecordDetail.getStartTime() != null && updaCallcenterRecordDetail.getDistributionTime() != null
                    && updaCallcenterRecordDetail.getDistributionTime().after(updaCallcenterRecordDetail.getStartTime())) {
                updaCallcenterRecordDetail.setDistributionTime(updaCallcenterRecordDetail.getStartTime());
            }

            if (byId.getDistributionTime() != null && updaCallcenterRecordDetail.getOutIvrTime() != null
                    && updaCallcenterRecordDetail.getOutIvrTime().after(byId.getDistributionTime())) {
                updaCallcenterRecordDetail.setOutIvrTime(byId.getDistributionTime());
            }

            if (byId.getOutIvrTime() != null && updaCallcenterRecordDetail.getDistributionTime() != null
                    && byId.getOutIvrTime().after(updaCallcenterRecordDetail.getDistributionTime())) {
                updaCallcenterRecordDetail.setDistributionTime(byId.getOutIvrTime());
            }

            if (updaCallcenterRecordDetail.getStartTime() != null) {
                updaCallcenterRecordDetail.setAnswerState(String.valueOf(CallCenterRecordEnum.AnswerStatus.CONNECT.value()));
            }
            if (updaCallcenterRecordDetail.getStartTime() == null && updaCallcenterRecordDetail.getOutTime() == null &&
                    byId.getStartTime() != null && byId.getOutTime() != null && !"3".equals(byId.getAnswerState() + "")) {
                if (byId.getStartTime().after(byId.getOutTime())) {
                    updaCallcenterRecordDetail.setStartTime(byId.getOutTime());
                }
                if (DateUtils.diffSecond(byId.getStartTime(), byId.getOutTime()) <= CdrVariableConst.FAST_HANGUP) {
                    updaCallcenterRecordDetail.setAnswerState(String.valueOf(CallCenterRecordEnum.AnswerStatus.FAST_HANGUP.value()));
                    updaCallcenterRecordDetail.setEndPerson("0");
                } else {
                    updaCallcenterRecordDetail.setAnswerState(String.valueOf(CallCenterRecordEnum.AnswerStatus.CONNECT.value()));
                }
            }


            if (updaCallcenterRecordDetail.getOutIvrTime() != null && updaCallcenterRecordDetail.getDistributionTime() != null
                    && updaCallcenterRecordDetail.getOutIvrTime().after(updaCallcenterRecordDetail.getDistributionTime())) {
                updaCallcenterRecordDetail.setDistributionTime(byId.getOutIvrTime());
            }
            log.info("更新数据：{}", updaCallcenterRecordDetail);
            callcenterRecordDetailMapper.updateCallcenterRecordDetail(updaCallcenterRecordDetail);
        }

        if (variables.get(CdrVariableConst.VAR_ASSIGN_TIME) != null) {
            updateCallcenterRecord.setDistributionTime(DateUtils.ignoreMs(new Date(Long.parseLong(String.valueOf(variables.get(CdrVariableConst.VAR_ASSIGN_TIME))) / 1000)));
        }
        if (variables.get(CdrVariableConst.VAR_ENQUEUE_TIMESTMAP) != null) {
            updateCallcenterRecord.setOutIvrTime(DateUtils.ignoreMs(new Date(Long.parseLong(String.valueOf(variables.get(CdrVariableConst.VAR_ENQUEUE_TIMESTMAP))) / 1000)));
        }
        // 接通
        if (CONNECT.equals(answerStatus) && String.valueOf(CallCenterRecordEnum.AnswerStatus.CONNECT.value()).equals(callcenterRecordDetail.getAnswerState())) {
            // 接通时间（用客服接起时间）
            if (updateCallcenterRecord.getStartTime() == null) {
                updateCallcenterRecord.setStartTime(new Date(Long.valueOf(String.valueOf(variables.get(CdrVariableConst.VAR_CONNECT_TIME))) / 1000));
                updateCallcenterRecord.setStartTime(DateUtils.dateWithNoNano(updateCallcenterRecord.getStartTime()));
            }
            // 挂断时间
            if (updateCallcenterRecord.getOutTime() == null) {
                updateCallcenterRecord.setOutTime(new Date(Long.valueOf(String.valueOf(variables.get(CdrVariableConst.VAR_END_TIME))) / 1000));
            }
            updateCallcenterRecord.setOutTime(DateUtils.dateWithNoNano(updateCallcenterRecord.getOutTime()));
        }
        updateCallcenterRecord.setEndPerson(String.valueOf(CallCenterRecordEnum.WhoHangUp.valueOf(String.valueOf(variables.get(CdrVariableConst.VAR_END_PERSON)).toUpperCase()).ordinal()));
        updateCallcenterRecord.setIvrState(Boolean.valueOf(String.valueOf(variables.get(CdrVariableConst.ENTER_IVR))));
        this.updateCallcenterRecord(updateCallcenterRecord);
        // 推送数据（百事通）
//        if (firstPhone) {
//            //CallcenterRecordDetail dbCallcenterRecordDetail = callcenterRecordDetailMapper.selectCallcenterRecordById(updaCallcenterRecordDetail.getId());
//            //BeanUtils.copyPropertiesIgnoreNull(updaCallcenterRecordDetail,dbCallcenterRecordDetail);
//            CallcenterRecordDetail finalUpdaCallcenterRecordDetail = updaCallcenterRecordDetail;
//            TransactionAfterCommitManager.getInstance().afterCommit(() -> {
//                sendCallRecord(updateCallcenterRecord, finalUpdaCallcenterRecordDetail, BSTCallRecordMsgObj.HANG_UP);
//            });
//        }
    }

    /**
     * 更新呼入通话记录-挂断
     *
     * @param cdr 参数
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateCallcenterRecordIn(Cdr cdr) {
        Map<String, Object> variables = cdr.getVariables().getVariableTable();
        String callUuid = String.valueOf(variables.get(CdrVariableConst.VAR_CALL_UUID));
        String traceId = String.valueOf(variables.get(CdrVariableConst.VAR_TRACE_ID));
        // 呼入原始号码
        String sipToUser = String.valueOf(variables.get(CdrVariableConst.SIP_TO_USER));
        CallcenterRecordDetail callcenterRecordDetail = callcenterRecordDetailMapper.selectCallcenterRecordByTraceId(traceId);
        List<String> historys = Optional.ofNullable((List<String>) variables.get(CdrVariableConst.VAR_TRANSFER_HISTORY)).orElse(Collections.emptyList()).stream().filter(item -> item.contains(CdrVariableConst.TRANSFER_X_OPERATION_TYPE)).collect(Collectors.toList());
        // 挂断方
        // 第一通电话
        if (callUuid.equals(traceId)) {
            // IVR转人工前挂断
            if (callcenterRecordDetail == null && historys.size() == 0) {
                CallcenterRecordDetail dbCallcenterRecordDetail = this.insertIvrHangUp(cdr);
                // 得到ID后   根据ID 异步插入当前记录的电话号码归属地
                TransactionAfterCommitManager.getInstance().afterCommit(() -> AsyncManager.me().execute(AsyncFactory.updateNumberRegion(dbCallcenterRecordDetail.getId())));
            }
            if (callcenterRecordDetail == null) {
                return 0;
            }
            if (variables.get(CdrVariableConst.ACD_MODEL) != null) {
                // ACD只有一通电话
                this.updateCallingIn(callcenterRecordDetail, cdr, historys.size() == 0);
                if (historys.size() > 0) {
                    int lastIndex = historys.size() - 1;
                    IntStream.range(0, historys.size()).forEach(index -> {
                        String tmpItem = historys.get(index);
                        String item = URLDecoder.decode(tmpItem, Charset.forName("UTF-8"));
                        PatternHistoryVo patternHistoryVo = new PatternHistoryVo(item);
                        Callflow callflow = cdr.getCallflows().stream().filter(tmpCallFlow -> tmpCallFlow.getUniqueId().equals(patternHistoryVo.getCallUuid())).findFirst().orElse(null);
                        if (callflow == null || StringUtils.isEmpty(patternHistoryVo.getOperationType())) {
                            return;
                        }

                        if (patternHistoryVo.getExternal()) {
                            return;
                        }

                        CallcenterRecordDetail updateCallcenterRecordDetail = callcenterRecordDetailMapper.selectCallcenterRecordByFreeswitchDetailId(patternHistoryVo.getDetailId());
                        if (updateCallcenterRecordDetail == null) {
                            return;
                        }

                        if (index == lastIndex) {
                            if (updateCallcenterRecordDetail.getOutTime() == null) {
                                updateCallcenterRecordDetail.setOutTime(new Date(Long.parseLong(String.valueOf(variables.get(CdrVariableConst.VAR_END_TIME))) / 1000));
                                updateCallcenterRecordDetail.setOutTime(DateUtils.dateWithNoNano(updateCallcenterRecordDetail.getOutTime()));
                            }
                            updateCallcenterRecordDetail.setState(String.valueOf(CallCenterRecordEnum.CallStatus.HANG_UP.value()));
                            if (StringUtils.isNotEmptyOrNullStr(sipToUser)) {
                                updateCallcenterRecordDetail.setOriginalCallTelephone(sipToUser);
                            }
                            if (updateCallcenterRecordDetail.getStartTime() != null && updateCallcenterRecordDetail.getOutTime() != null
                                    && updateCallcenterRecordDetail.getStartTime().after(updateCallcenterRecordDetail.getOutTime())) {
                                updateCallcenterRecordDetail.setOutTime(DateUtils.dateMoveTimeSec(updateCallcenterRecordDetail.getStartTime(), 1000L));
                            }
                            if (StringUtils.isBlank(updateCallcenterRecordDetail.getStatisfName()) && updateCallcenterRecordDetail.getStartTime() != null && updateCallcenterRecordDetail.getOutTime() != null &&
                                    DateUtils.diffSecond(updateCallcenterRecordDetail.getStartTime(), updateCallcenterRecordDetail.getOutTime()) <= CdrVariableConst.FAST_HANGUP) {
                                updateCallcenterRecordDetail.setAnswerState(String.valueOf(CallCenterRecordEnum.AnswerStatus.FAST_HANGUP.value()));
                                updateCallcenterRecordDetail.setEndPerson("0");
                            }
                            log.info("更新数据：{}", updateCallcenterRecordDetail);
                            callcenterRecordDetailMapper.updateCallcenterRecordDetail(updateCallcenterRecordDetail);

                            // 推送数据（百事通）
                            CallcenterRecord callcenterRecord = callcenterRecordMapper.selectCallcenterRecordById(updateCallcenterRecordDetail.getRecordId());
                            TransactionAfterCommitManager.getInstance().afterCommit(() -> {
                                sendCallRecord(callcenterRecord, updateCallcenterRecordDetail, BSTCallRecordMsgObj.HANG_UP);
                            });
                        }
                    });
                }
            } else {
                this.updateCallingIn(callcenterRecordDetail, cdr, true);
            }
        } else {
            if (callcenterRecordDetail == null) {
                return 0;
            }
            // 第二通电话
            this.updateCallingIn(callcenterRecordDetail, cdr, false);
            if (historys.size() > 0) {
                historys.forEach(tmpItem -> {
                    String item = URLDecoder.decode(tmpItem, Charset.forName("UTF-8"));
                    PatternHistoryVo patternHistoryVo = new PatternHistoryVo(item);
                    Callflow callflow = cdr.getCallflows().stream().filter(tmpCallFlow -> tmpCallFlow.getUniqueId().equals(patternHistoryVo.getCallUuid())).findFirst().orElse(null);
                    log.info("更新呼入通话记录-挂断 patternHistoryVo:{},callflow:{}", patternHistoryVo, callflow);
                    if (callflow == null || StringUtils.isEmpty(patternHistoryVo.getOperationType())) {
                        return;
                    }

                    CallcenterRecordDetail updateCallcenterRecordDetail = callcenterRecordDetailMapper.selectCallcenterRecordByFreeswitchDetailId(patternHistoryVo.getDetailId());
                    if (updateCallcenterRecordDetail == null) {
                        return;
                    }
                    if (!callflow.getTimes().getTransferTime().equals(0L)) {
                        return;
                    }

                    // 如果转接时间为0，则为最后一通
                    if (callflow.getTimes().getTransferTime().equals(0L)) {
                        updateCallcenterRecordDetail.setOutTime(new Date(callflow.getTimes().getHangupTime() / 1000));
                        updateCallcenterRecordDetail.setOutTime(DateUtils.dateWithNoNano(updateCallcenterRecordDetail.getOutTime()));
                        // 通话时长（挂断时间-接通时间）
                        if (updateCallcenterRecordDetail.getStartTime() != null) {
                            updateCallcenterRecordDetail.setConversationTime(String.valueOf((updateCallcenterRecordDetail.getOutTime().getTime() - updateCallcenterRecordDetail.getStartTime().getTime()) / 1000));
                        }
                    }
                    updateCallcenterRecordDetail.setState(String.valueOf(CallCenterRecordEnum.CallStatus.HANG_UP.value()));
                    // 呼入原始号码
                    if (StringUtils.isNotEmptyOrNullStr(sipToUser)) {
                        updateCallcenterRecordDetail.setOriginalCallTelephone(sipToUser);
                    }
                    callcenterRecordDetailMapper.updateCallcenterRecordDetail(updateCallcenterRecordDetail);
                    // 推送数据（百事通）
                    CallcenterRecord callcenterRecord = callcenterRecordMapper.selectCallcenterRecordById(updateCallcenterRecordDetail.getRecordId());
                    TransactionAfterCommitManager.getInstance().afterCommit(() -> {
                        sendCallRecord(callcenterRecord, updateCallcenterRecordDetail, BSTCallRecordMsgObj.HANG_UP);
                    });
                });
            }
        }


        // 添加到 录音处理 延迟队列
        // qualityDesignationUserId 智能质检指定客服检测(userId)
        /*String qualityDesignationUserId = redisCache.getCacheObject( Constants.SYSTEM_CONFIG + "qualityDesignationUserId" );
        if (StringUtils.isNotNull(qualityDesignationUserId)) {
            try {
                if ("null".equals(qualityDesignationUserId)) {
                    this.getIntoAudioHandleQueue(callcenterRecordDetail);
                }
            } catch (Exception e){
                log.error("------添加到 录音处理 延迟队列失败-----" + e);
            }
        }*/

        return 1;
    }

    /**
     * 更新呼出通话记录-挂断
     *
     * @param cdr 参数
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateCallcenterRecord(Cdr cdr) {
        Map<String, Object> variables = cdr.getVariables().getVariableTable();
        String callUuid = String.valueOf(variables.get(CdrVariableConst.VAR_CALL_UUID));
        String traceId = String.valueOf(variables.get(CdrVariableConst.VAR_TRACE_ID));
        CallcenterRecordDetail callcenterRecordDetail = callcenterRecordDetailMapper.selectCallcenterRecordByTraceId(traceId);
        // 未接通的电话无traceId
        if (callcenterRecordDetail == null) {
            return 0;
        }
        List<String> historys = Optional.ofNullable((List<String>) variables.get(CdrVariableConst.VAR_TRANSFER_HISTORY)).orElse(Collections.emptyList()).stream().filter(item -> item.contains(CdrVariableConst.TRANSFER_X_OPERATION_TYPE)).collect(Collectors.toList());
        // 第一通电话
        if (callUuid.equals(traceId)) {
            this.updateCallingOut(callcenterRecordDetail, cdr);
        } else {
            // 第二通电话
            if (historys.size() > 0) {
                historys.forEach(tmpItem -> {
                    String item = URLDecoder.decode(tmpItem, Charset.forName("UTF-8"));
                    PatternHistoryVo patternHistoryVo = new PatternHistoryVo(item);
                    Callflow callflow = cdr.getCallflows().stream().filter(tmpCallFlow -> tmpCallFlow.getUniqueId().equals(patternHistoryVo.getCallUuid())).findFirst().orElse(null);
                    if (callflow == null) {
                        return;
                    }

                    if (patternHistoryVo.getExternal()) {
                        return;
                    }

                    CallcenterRecordDetail updateCallcenterRecordDetail = callcenterRecordDetailMapper.selectCallcenterRecordByFreeswitchDetailId(patternHistoryVo.getDetailId());
                    if (updateCallcenterRecordDetail == null) {
                        return;
                    }

                    CallcenterRecord updateCallcenterRecord = callcenterRecordMapper.selectCallcenterRecordById(updateCallcenterRecordDetail.getRecordId());
                    if (String.valueOf(CallCenterRecordEnum.CallStatus.CALLING.value()).equals(updateCallcenterRecordDetail.getState())) {
                        // 如果转接时间为0，则为最后一通
                        if (callflow.getTimes().getTransferTime().equals(0L)) {
                            updateCallcenterRecordDetail.setOutTime(new Date(callflow.getTimes().getHangupTime() / 1000));
                            updateCallcenterRecordDetail.setOutTime(DateUtils.dateWithNoNano(updateCallcenterRecordDetail.getOutTime()));
                        } else if (!callflow.getTimes().getTransferTime().equals(0L)) {
                            updateCallcenterRecordDetail.setOutTime(new Date(callflow.getTimes().getTransferTime() / 1000));
                            updateCallcenterRecordDetail.setOutTime(DateUtils.dateWithNoNano(updateCallcenterRecordDetail.getOutTime()));
                        }
                        // 通话时长（挂断时间-接通时间）
                        updateCallcenterRecordDetail.setConversationTime(String.valueOf((updateCallcenterRecordDetail.getOutTime().getTime() - updateCallcenterRecordDetail.getStartTime().getTime()) / 1000));
                        updateCallcenterRecordDetail.setState(String.valueOf(CallCenterRecordEnum.CallStatus.HANG_UP.value()));

                        if (updateCallcenterRecord.getOutTime() == null) {
                            updateCallcenterRecord.setOutTime(updateCallcenterRecordDetail.getOutTime());
                        }
                        callcenterRecordMapper.updateCallcenterRecord(updateCallcenterRecord);
                    }

                    if (updateCallcenterRecordDetail.getStartTime() != null && updateCallcenterRecordDetail.getOutTime() != null
                            && updateCallcenterRecordDetail.getStartTime().after(updateCallcenterRecordDetail.getOutTime())) {
                        updateCallcenterRecordDetail.setOutTime(DateUtils.dateMoveTimeSec(updateCallcenterRecordDetail.getStartTime(), 1000L));
                    }

                    log.info("更新数据：{}", updateCallcenterRecordDetail);
                    callcenterRecordDetailMapper.updateCallcenterRecordDetail(updateCallcenterRecordDetail);
                    // 推送数据（百事通）
                    /*TransactionAfterCommitManager.getInstance().afterCommit(() ->{
                        sendCallRecord(updateCallcenterRecord,updateCallcenterRecordDetail,BSTCallRecordMsgObj.HANG_UP);
                    });*/
                });
            }
        }

        // 添加到 录音处理 延迟队列
        // qualityDesignationUserId 智能质检指定客服检测(userId)
        /*String qualityDesignationUserId = redisCache.getCacheObject( Constants.SYSTEM_CONFIG + "qualityDesignationUserId" );
        if (StringUtils.isNotNull(qualityDesignationUserId)) {
            try {
                if ("null".equals(qualityDesignationUserId)) {
                    this.getIntoAudioHandleQueue(callcenterRecordDetail);
                }
            } catch (Exception e){
                log.error("------添加到 录音处理 延迟队列失败-----" + e);
            }
        }*/

        return 1;
    }


    /**
     * 添加到 录音处理 延迟队列
     *
     * @param vo
     */
    private void getIntoAudioHandleQueue(CallcenterRecordDetail vo) {
        AudioHandleParam param = new AudioHandleParam();
        param.setCallRecordDetailId(vo.getId());
        param.setType(vo.getType());
        param.setMultipartyId(vo.getMultipartyId());
        param.setSound(vo.getSound());
        param.setSoundTextState(vo.getSoundTextState());
        log.error("--------------录音分割进延迟队列----------------------------" + param.toString());
        this.executor.add(new AudioHandle(param, 45, TimeUnit.SECONDS, this.callcenterRecordDetailService));
    }

    /**
     * 批量删除呼入呼出通话记录主
     *
     * @param ids 需要删除的呼入呼出通话记录主ID
     * @return 结果
     */
    @Override
    public int deleteCallcenterRecordByIds(Long[] ids) {
        return callcenterRecordMapper.deleteCallcenterRecordByIds(ids);
    }

    /**
     * 删除呼入呼出通话记录主信息
     *
     * @param id 呼入呼出通话记录主ID
     * @return 结果
     */
    @Override
    public int deleteCallcenterRecordById(Long id) {
        return callcenterRecordMapper.deleteCallcenterRecordById(id);
    }

    @Override
    public int updateAudioText(Long serviceId, String text) {
        if (StringUtils.isNotEmpty(text)) {
            callcenterRecordDetailMapper.updateSoundText(serviceId, text);
        }
        return 1;
    }

    @Override
    public void reject(Long detailId) {
        CallcenterRecordDetail callcenterRecordDetail = callcenterRecordDetailMapper.selectCallcenterRecordById(detailId);
        // 删除redis旧客服信息
        redisCache.deleteObject(Constants.OLD_USER_INFO + callcenterRecordDetail.getCallPhone());
    }

    @Override
    public void transferSkill(TransferSkillGroupParam param) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        CallTransferMsg callTransferMsg = new CallTransferMsg();
        String ivrGateWay = sysConfigService.selectConfigByKey(ConfigCommonConstants.IVR_GATE_WAY_KEY);
        CallcenterRecordDetailEntity byId = callcenterRecordDetailService.getById(param.getRecordDetailId());
        SysSkillGroupEntity sysSkillGroupEntity = sysSkillGroupMapper.selectById(param.getSkillId());

        callTransferMsg.setTransferType(TransferTypeEnum.AGENT)
                .setDomain(user.getAddrArea())
                .setCallType("0".equals(byId.getRecordType()) ? CallTypeEnum.IN : CallTypeEnum.OUT)
                .setCallId(param.getCallId())
                .setUserId(String.valueOf(user.getUserId()))
                .setTraceId(param.getTraceId())
                .setTransferType(TransferTypeEnum.GROUP)
                .setTo(String.valueOf(param.getSkillId()))
                .setFromEmpNo(user.getFreeswitchPhone())
                .setToGroup(sysSkillGroupEntity.getGroupName())
                .setLanguage(sysSkillGroupEntity.getLanguage())
                .setOpenSipHost(sysConfigService.selectConfigByKey(SysConfigEnum.OPENSIPS_IP))
                .setOpenSipPort(sysConfigService.selectConfigByKey(SysConfigEnum.OPENSIPS_PORT))
                .setGateway(ivrGateWay);
        MsgObj<CallTransferMsg> msgObj = new MsgObj<>(UUID.randomUUID().toString(), user.getAddrArea(), LocalDateTime.now(), callTransferMsg, null);
        log.info("转接技能组：" + JSONObject.toJSONString(msgObj));
        amqpTemplate.convertAndSend(QueueEnum.QUEUE_CALL_TRANSFER.getExchange(), QueueEnum.QUEUE_CALL_TRANSFER.getRouteKey(), msgObj);
        // 插入转接轨迹
        AsyncManager.me().execute(AsyncFactory.insertTransferCallTrace(param, System.currentTimeMillis(), SecurityUtils.getUserId()));
    }

    /**
     * 执行语音转义 转义完成 完成质检  并生成通话内容
     */
    @Override
    public void transformCall(Long detailId) {
        //查询是否已经生成通话语音转义
        CallcenterRecordDetailEntity callcenterRecordDetailEntity = callcenterRecordDetailMapper.selectById(detailId);
        if (callcenterRecordDetailEntity != null) {
            //没有经过语音分割
            if (null == callcenterRecordDetailEntity.getDivisionMark() || !callcenterRecordDetailEntity.getDivisionMark().equals("1")) {
                R result = localAsrVoiceService.transferTextByDetailId(detailId.toString());
                log.info("通话" + callcenterRecordDetailEntity.getBillNumber() + "语音转义结果为" + result.getMsg());
                if (result != null && result.getCode() != 200) {
                    log.error("通话" + callcenterRecordDetailEntity.getBillNumber() + "语音转义失败:" + result.getMsg());
                    throw new CustomException("通话" + callcenterRecordDetailEntity.getBillNumber() + "语音转义失败:" + result.getMsg());
                }
            }
            // 获取需要日志的变量
            String billNumber = callcenterRecordDetailEntity.getBillNumber();

            // 异步执行质检评分
            CompletableFuture.runAsync(() -> {
                try {
                    qualityService.doQualityScoring(detailId);
                } catch (Exception e) {
                    log.error("通话" + billNumber + "执行质检评分失败:", e);
                }
            }).exceptionally(e -> {
                log.error("通话" + billNumber + "质检评分异步任务异常:", e);
                return null;
            });
            // 异步执行通话总结
            CompletableFuture.runAsync(() -> {
                try {
                    generateSumUp(detailId, callcenterRecordDetailEntity.getCallTime());
                } catch (Exception e) {
                    log.error("通话" + billNumber + "执行通话总结失败:", e);
                }
            }).exceptionally(e -> {
                log.error("通话" + billNumber + "通话总结异步任务异常:", e);
                return null;
            });

        } else {
            throw new CustomException("无该通话记录id");
        }
    }


    /**
     * 执行语音转义 转义完成 完成质检  并生成通话内容
     */
    @Override
    public void transformCallTB(Long detailId) {
        //查询是否已经生成通话语音转义
        CallcenterRecordDetailEntity callcenterRecordDetailEntity = callcenterRecordDetailMapper.selectById(detailId);
        if (callcenterRecordDetailEntity != null) {
            //没有经过语音分割
            if (null == callcenterRecordDetailEntity.getDivisionMark() || !callcenterRecordDetailEntity.getDivisionMark().equals("1")) {
                R result = localAsrVoiceService.transferTextByDetailId(detailId.toString());
                if (result.getCode() != 200) {
                    log.error("通话" + callcenterRecordDetailEntity.getBillNumber() + "语音转义失败:" + result.getMsg());
                    throw new CustomException("语音转义失败");
                }
            }
            generateSumUp(detailId, callcenterRecordDetailEntity.getCallTime());
        }
    }


    /**
     * 生成通话内容总结
     *
     * @param detailId
     */
    public void generateSumUp(Long detailId, Date callTime) {
        Date startDate = new Date();
        YearMonthHandler.setData(DateUtil.format(callTime, "yyyyMM"));
        List<CallcenterRecordDetailSound> contentData = callcenterRecordDetailMapper.selectCallcenterRecordDetailSoundByCallRecordDetailId(detailId);
        YearMonthHandler.removeData();
        if (CollUtil.isNotEmpty(contentData)) {
            contentData.sort(Comparator.comparingDouble(c -> Double.valueOf(c.getStartSecond())));
            String sumupText = getAiSumUpText(contentData);
            CallcenterRecordDetailExtEntity callcenterRecordDetailExtEntity = new CallcenterRecordDetailExtEntity();
            callcenterRecordDetailExtEntity.setRecordDetailId(detailId);
            callcenterRecordDetailExtEntity.setSummary(sumupText);
            callcenterRecordDetailExtEntity.setCreateTime(new Date());
            callcenterRecordDetailExtEntity.setStartTime(startDate);
            callcenterRecordDetailExtMapper.insert(callcenterRecordDetailExtEntity);
            /*String content = contentData.stream().map(CallcenterRecordDetailSound::getSoundText).collect(Collectors.joining());
            if (isContentTooLarge(content)) {
                List<String> segments = splitContent(content, 102400); // 128KB = 131072字节
                StringBuilder finalSummary = new StringBuilder();
                for (String segment : segments) {
                    String summary = generateSegmentSummary(segment);
                    Integer index = segments.indexOf(segment);
                    finalSummary.append("通话总结"+(index+1)+"：" + summary).append("\n");
                    CallcenterRecordDetailExtEntity callcenterRecordDetailExtEntity = new CallcenterRecordDetailExtEntity();
                    callcenterRecordDetailExtEntity.setRecordDetailId(detailId);
                    callcenterRecordDetailExtEntity.setSummary(finalSummary.toString());
                    callcenterRecordDetailExtEntity.setCreateTime(new Date());
                    callcenterRecordDetailExtMapper.insert(callcenterRecordDetailExtEntity);
                }
            } else {
                // 内容未超过128KB，直接生成总结
                String summary = generateSegmentSummary(content);
                CallcenterRecordDetailExtEntity callcenterRecordDetailExtEntity = new CallcenterRecordDetailExtEntity();
                callcenterRecordDetailExtEntity.setRecordDetailId(detailId);
                callcenterRecordDetailExtEntity.setSummary(summary.toString());
                callcenterRecordDetailExtEntity.setCreateTime(new Date());
                callcenterRecordDetailExtMapper.insert(callcenterRecordDetailExtEntity);
            }*/
        }
    }


    /**
     * 生成通话内容总结
     *
     * @param detailId
     */
    @Override
    public void generateCallSumUp(Long detailId, Boolean judgmentCall) {
        //查询是否已经生成通话语音转义
        CallcenterRecordDetailEntity callcenterRecordDetailEntity = callcenterRecordDetailMapper.selectById(detailId);
        if (null == callcenterRecordDetailEntity.getDivisionMark() || !callcenterRecordDetailEntity.getDivisionMark().equals("1")) {
            throw new CustomException("语音转义未完成,请稍后再试");
        }
        generateSumUp(detailId, callcenterRecordDetailEntity.getCallTime());
    }

    @Override
    public String getSumUpByDetailId(Long detailId) {
        CallcenterRecordDetailExtEntity callcenterRecordDetailExtEntity = callcenterRecordDetailExtMapper
                .selectOne(new LambdaQueryWrapper<CallcenterRecordDetailExtEntity>().eq(CallcenterRecordDetailExtEntity::getRecordDetailId, detailId).orderByDesc(CallcenterRecordDetailExtEntity::getCreateTime).last(" limit 1"));
        return callcenterRecordDetailExtEntity == null ? "" : callcenterRecordDetailExtEntity.getSummary();
    }

    /**
     * 语音转译
     *
     * @param detailId
     * @param isTest
     * @return
     */
    @Override
    public List<CallcenterRecordDetailSound> translateSound(Long detailId, Boolean isTest) {
        CallcenterRecordDetailEntity detail = callcenterRecordDetailMapper.selectById(detailId);
        if(isTest){
            YearMonthHandler.setData(DateUtil.format(detail.getCallTime(), "yyyyMM"));
            List<CallcenterRecordDetailSound> sounds = callcenterRecordDetailMapper.selectCallcenterRecordDetailSoundByCallRecordDetailId(detailId);
            YearMonthHandler.removeData();
            return sounds;
        }else{
        /*YearMonthHandler.setData(DateUtil.format(detail.getCallTime(), "yyyyMM"));
        List<CallcenterRecordDetailSound> sounds = callcenterRecordDetailMapper.selectCallcenterRecordDetailSoundByCallRecordDetailId(detailId);
        YearMonthHandler.removeData();
        if (CollUtil.isNotEmpty(sounds)) {
            return sounds;
        } else {*/
            //删除之前的录音内容
            R result = localAsrVoiceService.transferTextByDetailId(detailId.toString());
            if (result != null && result.getCode() == 200) {
                YearMonthHandler.setData(DateUtil.format(detail.getCallTime(), "yyyyMM"));
                List<CallcenterRecordDetailSound> data = callcenterRecordDetailMapper.selectCallcenterRecordDetailSoundByCallRecordDetailId(detailId);
                YearMonthHandler.removeData();
                CallSoundUtil.mergeConversations(data);
                return data;
            } else {
                throw new CustomException(result.getMsg());
            }
        }
    }

    @Override
    public String generateCallSumUpTest(Long detailId) {
        List<CallcenterRecordDetailSound> contentData = callcenterRecordDetailMapper.selectCallcenterRecordDetailSoundByCallRecordDetailId(detailId);
        contentData.sort(Comparator.comparingDouble(c -> Double.valueOf(c.getStartSecond())));
        return getAiSumUpText(contentData);
    }

    @Override
    public String getAiGenerateSumup(List<CallcenterRecordDetailSoundEntity> soundList) {
        List<CallcenterRecordDetailSound> callcenterRecordDetailSounds = new ArrayList<>();
        soundList.forEach(v -> {
            CallcenterRecordDetailSound callcenterRecordDetailSound = new CallcenterRecordDetailSound();
            BeanUtil.copyProperties(v, callcenterRecordDetailSound);
            callcenterRecordDetailSounds.add(callcenterRecordDetailSound);
        });
        return getAiSumUpText(callcenterRecordDetailSounds);
    }


    /**
     * 获取通话总结
     *
     * @param contentData
     * @return
     */
    private String getAiSumUpText(List<CallcenterRecordDetailSound> contentData) {
        List<List<String>> contentDatas = new ArrayList<>();
        Integer maxSeconds = 200;
        Integer minStrLength = 20;
        List<String> computeDatas = new ArrayList<>();

        for (int i = 0; i < contentData.size(); i++) {
            CallcenterRecordDetailSound v = contentData.get(i);
            BigDecimal value = new BigDecimal(maxSeconds + maxSeconds * contentDatas.size()).subtract(new BigDecimal(Double.valueOf(v.getEndSecond())));
            if (value.compareTo(BigDecimal.ZERO) > 0) {
                computeDatas.add(v.getSoundText());
                if (i == contentData.size() - 1 && CollUtil.isNotEmpty(computeDatas)) {
                    contentDatas.add(computeDatas);
                }
            } else {
                List<String> changeDatas = new ArrayList<>(computeDatas);
                contentDatas.add(changeDatas);
                computeDatas.clear();
                computeDatas.add(v.getSoundText());
            }

        }

        for (CallcenterRecordDetailSound v : contentData) {
            BigDecimal value = new BigDecimal(maxSeconds + maxSeconds * contentDatas.size()).subtract(new BigDecimal(Double.valueOf(v.getEndSecond())));
            if (value.compareTo(BigDecimal.ZERO) > 0) {
                computeDatas.add(v.getSoundText());
            } else {
                List<String> changeDatas = new ArrayList<>(computeDatas);
                contentDatas.add(changeDatas);
                computeDatas.clear();
                computeDatas.add(v.getSoundText());
            }

        }

        if (contentDatas.size() == 1) {
            String result = contentDatas.get(0).stream().collect(Collectors.joining());
            if (result.length() <= minStrLength) {
                return "当前通话内容过短，无法生成通话总结！";
            }
        }
        List<SumUpJsonDto> sumUpJsonDtos = new ArrayList<>();
        for (List<String> data : contentDatas) {
            String str = data.stream().collect(Collectors.joining());
            if (str.length() <= minStrLength) {
                break;
            }
            log.info("通话总结调用参数为" + String.format(CallSumupConstant.SUM_UP_KEYWORD_JSON, str));
            String resultJson = llmApiUtil.chat(String.format(CallSumupConstant.SUM_UP_KEYWORD_JSON, str));
            if (StrUtil.isNotBlank(resultJson)) {
                try {
                    SumUpJsonDto sumUpJsonDto = JSONUtil.toBean(resultJson, SumUpJsonDto.class);
                    if (StrUtil.isNotBlank(sumUpJsonDto.getSummary()) && CollUtil.isNotEmpty(sumUpJsonDto.getMainContent())) {
                        sumUpJsonDtos.add(sumUpJsonDto);
                    }
                } catch (Exception e) {
                    log.error("通话总结json格式化失败" + resultJson);
                    break;
                }
            }
        }
        if (CollUtil.isNotEmpty(sumUpJsonDtos)) {
            if (sumUpJsonDtos.size() == 1) {
                String sumUpContent = "通话概括：" + sumUpJsonDtos.get(0).getSummary() + "\n主要内容：\n";
                StringBuilder mainContent = new StringBuilder("");
                List<String> maincontentList = sumUpJsonDtos.get(0).getMainContent();
                for (int i = 0; i < maincontentList.size(); i++) {
                    mainContent.append((i + 1) + ":" + maincontentList.get(i) + "\n");
                }
                sumUpContent += mainContent.toString();
                return sumUpContent;
            } else {
                StringBuilder summaryStr = new StringBuilder("");
                StringBuilder mainContentStr = new StringBuilder("");
                for (int i = 0; i < sumUpJsonDtos.size(); i++) {
                    summaryStr.append((i + 1) + ":" + sumUpJsonDtos.get(i).getSummary());
                }
                AtomicReference<Integer> contentIndex = new AtomicReference<>(1);
                sumUpJsonDtos.forEach(v -> {
                    v.getMainContent().forEach(vv -> {
                        mainContentStr.append(contentIndex + ":" + vv);
                        contentIndex.getAndSet(contentIndex.get() + 1);
                    });
                });
                String summaryStrs = llmApiUtil.chat(String.format(CallSumupConstant.SUM_UP_SUMMARY_KEYWORD, summaryStr), null, false);
                System.out.println(String.format(CallSumupConstant.SUM_UP_MAIN_CONTENT_KEYWORD, mainContentStr));
                String mainContentStrs = llmApiUtil.chat(String.format(CallSumupConstant.SUM_UP_MAIN_CONTENT_KEYWORD, mainContentStr), null, false);
                if (StrUtil.isBlank(mainContentStrs)) {
                    System.out.println(mainContentStrs);
                }
                String sumUpContent = StrUtil.startWith(summaryStrs, "通话概括") ? summaryStrs + "\n主要内容：\n" + mainContentStrs : "通话概括：\n" + summaryStrs + "\n主要内容：\n" + mainContentStrs;
                return sumUpContent;
            }
        }

        return "";
    }

    /**
     * 判断内容是否超过128KB
     *
     * @param content
     * @return
     */
    public boolean isContentTooLarge(String content) {
        return content.getBytes(StandardCharsets.UTF_8).length > 102400; // 128KB
    }

    /**
     * 将内容分段
     *
     * @param content
     * @param maxBytes
     * @return
     */
    public static List<String> splitContent(String content, int maxBytes) {
        List<String> segments = new ArrayList<>();
        byte[] contentBytes = content.getBytes(StandardCharsets.UTF_8);
        int length = contentBytes.length;

        int start = 0;
        while (start < length) {
            int end = Math.min(start + maxBytes, length);
            // 处理可能在字符中间的情况
            while (end > start && (contentBytes[end - 1] & 0xC0) == 0x80) {
                end--; // 确保不在UTF-8字符中间截断
            }
            segments.add(new String(contentBytes, start, end - start, StandardCharsets.UTF_8));
            start = end;
        }
        return segments;
    }

    /**
     * 调用生成段落总结
     *
     * @param segment
     * @return
     */
    public String generateSegmentSummary(String segment) {
        // 这里可以调用AI模型生成实际的总结
        return llmApiUtil.chat(String.format(CallSumupConstant.SUM_UP_KEYWORD, segment), null, false);
    }


    /**
     * 推送通话记录（统一处理发送百事通）
     *
     * @param callcenterRecord
     * @param callcenterRecordDetail
     * @param state
     */
    private void sendCallRecord(CallcenterRecord callcenterRecord, CallcenterRecordDetail callcenterRecordDetail,
                                int state) {
        /*if(callcenterRecord == null || callcenterRecordDetail == null){
            log.info("-------------------推送通话记录为空-------------------，主记录：{}，详情：{}",callcenterRecord,callcenterRecordDetail);
            return;
        }
        List<CallcenterRecordDetail> callcenterRecordDetails = Lists.newArrayList();
        callcenterRecordDetails.add(callcenterRecordDetail);
        threadPoolTaskExecutor.execute(() -> {
            callcenterRecord.setCallcenterRecordDetailList(callcenterRecordDetails);
            BSTCallRecordMsgObj<CallcenterRecord> callRecordMsgObj = new BSTCallRecordMsgObj<CallcenterRecord>()
                    .setData(callcenterRecord).setState(state);
            callSender.sendCallRecord(callRecordMsgObj);
            //log.info("**********************统一推送通话记录**********************:{}",callRecordMsgObj);
        });*/
    }

}
