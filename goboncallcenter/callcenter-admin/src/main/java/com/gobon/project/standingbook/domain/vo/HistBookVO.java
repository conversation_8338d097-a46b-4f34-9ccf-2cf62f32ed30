package com.gobon.project.standingbook.domain.vo;

import com.gobon.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program goboncallcenter
 * @description
 * @create 2023/8/24 18:03
 **/
@Data
public class HistBookVO implements Serializable {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("流水号")
    @Excel(name = "流水号")
    private String vacLedgerSerial;

    @ApiModelProperty("来电时间")
    @Excel(name = "来电时间")
    private String createDate;

    @ApiModelProperty("客户姓名")
    @Excel(name = "客户姓名")
    private String vacClientName;

    @ApiModelProperty("性别")
    @Excel(name = "性别")
    private String customerSex;

    @ApiModelProperty("呼叫地市")
    @Excel(name = "呼叫地市")
    private String calledCity;

    @ApiModelProperty("来电号码")
    @Excel(name = "来电号码")
    private String vacLedgerNumber;

    @ApiModelProperty("证件号码")
    @Excel(name = "证件号码")
    private String credentialsNum;

    @ApiModelProperty("咨询人类别")
    @Excel(name = "咨询人类别")
    private String customerType;

    @ApiModelProperty("业务类型一级")
    @Excel(name = "业务类型一级")
    private String firstLevel;

    @ApiModelProperty("业务类型二级")
    @Excel(name = "业务类型二级")
    private String secondLevel;

    @ApiModelProperty("业务类型三级")
    @Excel(name = "业务类型三级")
    private String thirdLevel;

    @ApiModelProperty("业务类型四级")
    @Excel(name = "业务类型四级")
    private String fourLevel;

    @ApiModelProperty("业务类型")
    @Excel(name = "业务类型")
    private String vacLedgerBusinessType;

    @ApiModelProperty("通话时长")
    @Excel(name = "通话时长", cellType = Excel.ColumnType.NUMERIC)
    private Integer vacLedgerTalkDur;

    @ApiModelProperty("呼叫工号")
    @Excel(name = "呼叫工号")
    private String vacLedgerAgentCode;

    @ApiModelProperty("呼叫坐席名称")
    @Excel(name = "呼叫坐席名称")
    private String vacLedgerAgentName;

    @ApiModelProperty("呼叫队列")
    @Excel(name = "呼叫队列")
    private String numLedgerCallQueue;

    @ApiModelProperty("客户自诉")
    @Excel(name = "客户自诉")
    private String vacLedgerClientAccount;

    @ApiModelProperty("回复内容")
    @Excel(name = "回复内容")
    private String vacLedgerLawyerSuggestion;

    @ApiModelProperty("服务地市")
    @Excel(name = "服务地市")
    private String cityAlone;

    @ApiModelProperty("服务号码")
    @Excel(name = "服务号码")
    private String serviceNumber;

    @ApiModelProperty("改后队列")
    @Excel(name = "改后队列")
    private String numLedgerCallQueueUpdate;

    @ApiModelProperty("咨询人年龄")
    @Excel(name = "咨询人年龄")
    private String customerAge;

    @ApiModelProperty("咨询人行业")
    @Excel(name = "咨询人行业")
    private String customerIndustry;

    @ApiModelProperty("咨询人职业")
    @Excel(name = "咨询人职业")
    private String customerCareer;

    @ApiModelProperty("咨询人来源")
    @Excel(name = "咨询人来源")
    private String customerSource;

    @ApiModelProperty("满意度")
    @Excel(name = "满意度")
    private String serviceReport;

    @ApiModelProperty("事件标签")
    @Excel(name = "事件标签")
    private String eventLabel;

    @ApiModelProperty("呼叫类型")
    @Excel(name = "呼叫类型")
    private String callType;

    @ApiModelProperty("咨询人学历")
    @Excel(name = "咨询人学历")
    private String customerEducation;

    @ApiModelProperty("指引渠道")
    @Excel(name = "指引渠道")
    private String guideChannel;
}
