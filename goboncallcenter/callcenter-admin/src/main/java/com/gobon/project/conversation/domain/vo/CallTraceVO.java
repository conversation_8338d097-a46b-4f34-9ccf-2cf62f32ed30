package com.gobon.project.conversation.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @program goboncallcenter
 * @description
 * @create 2023/10/17 16:31
 **/
@Data
public class CallTraceVO implements Serializable {

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("用户昵称")
    private String nickName;

    @ApiModelProperty("用户工号")
    private String userNumber;

    @ApiModelProperty("通话id")
    private Long recordDetailId;

    @ApiModelProperty("通话detailId")
    private String detailId;

    @ApiModelProperty("通话traceId")
    private String traceId;

    @ApiModelProperty("状态")
    private String state;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("mq推送")
    private String mqContent;

    @ApiModelProperty("状态时间")
    private String stateTime;

}
