package com.gobon.project.report.vo;

import com.gobon.framework.aspectj.lang.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: goboncallcenter
 * @description:
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2020/11/24 14:25
 **/
@Data
public class LocalCitiesVo implements Serializable {

    private static final long serialVersionUID = 1708050647948637753L;
    /**
     * 城市
     */
    @Excel(name = "地市")
    private String cityName;

    /**
     * IVR拨打总量
     */
    @Excel(name = "IVR拨打总量")
    private String ivrTotal;

    /**
     * 接通量
     */
    @Excel(name = "通话总量")
    private String answerTotal;

    /**
     * 进入队列量
     */
    @Excel(name = "进入队列量")
    private String queuTotal;

    /**
     * 满意度
     */
    @Excel(name = "满意度")
    private String satisfaction;

    /**
     * 快速挂机
     */
    @Excel(name = "群众快速挂机量")
    private String fastHang;

    /**
     * 队列放弃量
     */
    @Excel(name = "队列放弃量")
    private String queueGiveUp;

    /**
     * 未进队列数量
     */
    @Excel(name = "未进队列数量")
    private String noIntoTotal;

    /**
     * IVR放弃量
     */
    @Excel(name = "IVR放弃量")
    private String ivrGiveUp;


    /**
     * 呼入量
     */
    @Excel(name = "呼入量")
    private String inTotal;

    /**
     * 呼入接通量
     */
    @Excel(name = "呼入接通量")
    private String inAnswerTotal;

    /**
     * 呼入接通率
     */
    @Excel(name = "呼入接通率")
    private String inAnswerRate;

    /**
     * 呼入总时长
     */
    @Excel(name = "呼入通话总时长")
    private String inAnswerTime;

    /**
     * 呼出量
     */
    @Excel(name = "呼出量")
    private String outTotal;

    /**
     * 呼出接通量
     */
    @Excel(name = "呼出接通量")
    private String outAnswerTotal;

    /**
     * 呼出接通率
     */
    @Excel(name = "呼出接通率")
    private String outAnswerRate;

    /**
     * 呼出总时长
     */
    @Excel(name = "呼出通话总时长")
    private String outAnswerTime;
    /**
     * 参评量
     */
    @Excel(name = "参评总量")
    private String evaluateTotal;
    /**
     * 非常满意量
     */
    @Excel(name = "非常满意量")
    private String veryGoodTotal;
    /**
     * 满意量
     */
    @Excel(name = "满意量")
    private String goodTotal;
    /**
     * 一般量
     */
    @Excel(name = "基本满意量")
    private String commonTotal;
    /**
     * 不满意量
     */
    @Excel(name = "不满意量")
    private String badTotal;
    /**
     * 参评率
     */
    @Excel(name = "参评率")
    private String evaluateRate;

}
