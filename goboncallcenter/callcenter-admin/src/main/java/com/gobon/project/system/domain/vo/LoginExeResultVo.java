package com.gobon.project.system.domain.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program goboncallcenter
 * @description
 * @create 2021/5/17 10:30
 **/
@Data
@ApiModel("exe登录返回结果")
public class LoginExeResultVo implements Serializable {
    private static final long serialVersionUID = -7042666438994540350L;

    /**
     * websocketurl
     */
    @ApiModelProperty("websocketurl")
    private String websocketUrl;

    /**
     * 网站url
     */
    @ApiModelProperty("网站url")
    private String webUrl;

    /**
     * 管理后台url
     */
    @ApiModelProperty("管理后台url")
    private String manageUrl;
}
