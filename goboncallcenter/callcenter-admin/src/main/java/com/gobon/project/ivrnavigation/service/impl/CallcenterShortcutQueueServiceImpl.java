package com.gobon.project.ivrnavigation.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.gobon.common.utils.DateUtils;
import com.gobon.common.utils.SecurityUtils;
import com.gobon.framework.aspectj.lang.annotation.DataScopeDeptSkillGroup;
import com.gobon.project.ivrnavigation.domain.CallcenterShortcutQueue;
import com.gobon.project.ivrnavigation.domain.param.CallcenterShortcutQueueParam;
import com.gobon.project.ivrnavigation.domain.vo.ExternalNumberTypeVO;
import com.gobon.project.ivrnavigation.mapper.CallcenterShortcutQueueMapper;
import com.gobon.project.ivrnavigation.service.ICallcenterShortcutQueueService;
import com.gobon.project.system.domain.SysUser;
import com.gobon.project.ivrnavigation.domain.param.ExternalNumberParam;
import com.gobon.project.ivrnavigation.domain.vo.ExternalNumberTreeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * 快捷热线队列设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-06-15
 */
@Service
public class CallcenterShortcutQueueServiceImpl implements ICallcenterShortcutQueueService {
    @Autowired
    private CallcenterShortcutQueueMapper callcenterShortcutQueueMapper;

    /**
     * 查询快捷热线队列设置
     *
     * @param id 快捷热线队列设置ID
     * @return 快捷热线队列设置
     */
    @Override
    public CallcenterShortcutQueue selectCallcenterShortcutQueueById( Long id ) {
        return callcenterShortcutQueueMapper.selectCallcenterShortcutQueueById( id );
    }

    /**
     * 查询快捷热线队列设置列表
     *
     * @param param 快捷热线队列设置
     * @return 快捷热线队列设置
     */
    @Override
//    @DataScopeDeptSkillGroup( tebleAlias = "csq" )
    public List<CallcenterShortcutQueue> selectCallcenterShortcutQueueList( CallcenterShortcutQueueParam param ) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        param.setAddrArea(user.getAddrArea());
        return callcenterShortcutQueueMapper.selectCallcenterShortcutQueueList( param );
    }

    /**
     * 新增快捷热线队列设置
     *
     * @param callcenterShortcutQueue 快捷热线队列设置
     * @return 结果
     */
    @Override
    public int insertCallcenterShortcutQueue( CallcenterShortcutQueue callcenterShortcutQueue ) {
        callcenterShortcutQueue.setCreateTime( DateUtils.getNowDate() );
        SysUser user = SecurityUtils.getLoginUser().getUser();
        callcenterShortcutQueue.setCreateId( user.getUserId() );
        callcenterShortcutQueue.setCreateName( user.getNickName() );
        callcenterShortcutQueue.setDomain(SecurityUtils.getLoginUser().getUser().getDomain());
        return callcenterShortcutQueueMapper.insertCallcenterShortcutQueue( callcenterShortcutQueue );
    }

    /**
     * 修改快捷热线队列设置
     *
     * @param callcenterShortcutQueue 快捷热线队列设置
     * @return 结果
     */
    @Override
    public int updateCallcenterShortcutQueue( CallcenterShortcutQueue callcenterShortcutQueue ) {
        callcenterShortcutQueue.setUpdateTime( DateUtils.getNowDate() );
        SysUser user = SecurityUtils.getLoginUser().getUser();
        callcenterShortcutQueue.setUpdateId( user.getUserId() );
        callcenterShortcutQueue.setUpdateName( user.getNickName() );
        return callcenterShortcutQueueMapper.updateCallcenterShortcutQueue( callcenterShortcutQueue );
    }

    /**
     * 批量删除快捷热线队列设置
     *
     * @param ids 需要删除的快捷热线队列设置ID
     * @return 结果
     */
    @Override
    public int deleteCallcenterShortcutQueueByIds( Long[] ids ) {
        return callcenterShortcutQueueMapper.deleteCallcenterShortcutQueueByIds( ids );
    }

    /**
     * 删除快捷热线队列设置信息
     *
     * @param id 快捷热线队列设置ID
     * @return 结果
     */
    @Override
    public int deleteCallcenterShortcutQueueById( Long id ) {
        return callcenterShortcutQueueMapper.deleteCallcenterShortcutQueueById( id );
    }

    /**
     * 查询 外部号码列表
     */
    @Override
    public List<ExternalNumberTypeVO> getExternalNumberList() {
        ExternalNumberParam param = new ExternalNumberParam();
        param.setAddrArea(SecurityUtils.getLoginUser().getUser().getAddrArea());
        return callcenterShortcutQueueMapper.getExternalNumberList(param);
    }


    /**
     * 查询 外部号码树
     * @param code 外部类型code
     */
    @Override
    public List<ExternalNumberTreeVO> getExternalNumberTree(String code) {
        List<ExternalNumberTreeVO> tree = new ArrayList<>();


        ExternalNumberParam param = new ExternalNumberParam();
//        param.setAddrArea(SecurityUtils.getLoginUser().getUser().getAddrArea());
        param.setCode(code);
        List<ExternalNumberTreeVO> treeVOS = callcenterShortcutQueueMapper.getExternalNumberTree( param );
        if( !ObjectUtils.isEmpty( treeVOS ) ){
            tree = recursionGetExternalNumberTree( treeVOS.get( 0 ).getParentValue(), treeVOS );
        }
        if( tree.size() == 0 ){
            tree = treeVOS;
        }

        return tree;
    }

    /**
     * 外部号码树 组装数据
     *
     * @param parentId
     * @param externalNumberTreeVOS
     * @return
     */
    private List<ExternalNumberTreeVO> recursionGetExternalNumberTree( Long parentId, List<ExternalNumberTreeVO> externalNumberTreeVOS ) {
        List<ExternalNumberTreeVO> tempList = new ArrayList<>();
        if( parentId == null ){
            return tempList;
        }
        for( ExternalNumberTreeVO treeVO : externalNumberTreeVOS ){
            Long parentValue = treeVO.getParentValue();
            Long value = treeVO.getValue();
            if( parentId.equals( parentValue ) ){
                List<ExternalNumberTreeVO> list = recursionGetExternalNumberTree( value, externalNumberTreeVOS );
                treeVO.setChildren( list );
                tempList.add( treeVO );
            }
        }
        return tempList;
    }

    /**
     * 添加 外部号码类型
     * @param param 外部号码入参
     * @return
     */
    @Override
    public int addExternalNumberType(ExternalNumberParam param){
        SysUser user = SecurityUtils.getLoginUser().getUser();
        param.setAddrArea(user.getAddrArea());
        param.setCreateBy(user.getNickName());
        param.setCreateById(user.getUserId());
        param.setCreateTime(new Date());
        param.setDelFlag("0"); // 是否删除（0：存在  1：删除）
        return callcenterShortcutQueueMapper.addExternalNumberType(param);
    }

    /**
     * 修改 外部号码类型
     * @param param 外部号码入参
     * @return
     */
    @Override
    public int updateExternalNumberType(ExternalNumberParam param){
        SysUser user = SecurityUtils.getLoginUser().getUser();
        param.setAddrArea(user.getAddrArea());
        param.setUpdateBy(user.getNickName());
        param.setUpdateById(user.getUserId());
        param.setUpdateTime(new Date());
        return callcenterShortcutQueueMapper.updateExternalNumberType(param);
    }


    /**
     * 删除 外部号码类型
     * @param id
     * @return
     */
    @Override
    public int delExternalNumberType(Long id){
        SysUser user = SecurityUtils.getLoginUser().getUser();
        ExternalNumberParam param = new ExternalNumberParam();
        param.setId(id);
        param.setDeleteBy(user.getNickName());
        param.setDeleteById(user.getUserId());
        param.setDeleteTime(new Date());
        param.setDelFlag("1");
        param.setAddrArea(user.getAddrArea());
        return callcenterShortcutQueueMapper.updateExternalNumberType(param);
    }
}
