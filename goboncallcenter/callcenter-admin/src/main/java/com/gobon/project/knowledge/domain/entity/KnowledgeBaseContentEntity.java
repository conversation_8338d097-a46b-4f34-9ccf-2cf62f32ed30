package com.gobon.project.knowledge.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 知识库内容
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("knowledge_base_content")
@ApiModel(value="KnowledgeBaseContent对象", description="知识库内容")
public class KnowledgeBaseContentEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "知识库分类id")
    private Long knowledgeBaseTypeId;

    @ApiModelProperty(value = "知识库标题")
    private String knowledgeBaseTitle;

    @ApiModelProperty(value = "知识库内容文字信息")
    private String knowledgeBaseContentText;

    @ApiModelProperty(value = "知识库内容")
    private String knowledgeBaseContent;

    @ApiModelProperty(value = "律师意见")
    private String lawyerSuggest;

    @ApiModelProperty(value = "法律规定")
    private String legalProvisions;

    @ApiModelProperty(value = "附件数组")
    private String fileObj;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "永久有效：permanent / 选择时间：temporary")
    private String validStatus;

    @ApiModelProperty(value = "有效开始时间")
    private Date validStartTime;

    @ApiModelProperty(value = "有效结束时间")
    private Date validEndTime;

    @ApiModelProperty(value = "创建者")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    private String updateBy;

    @ApiModelProperty(value = "更新人id")
    private Long updateById;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "是否是常用文件标记(0 否；1 是)")
    private String commonlyUsedFileMark;

    @ApiModelProperty(value = "使用域")
    private String domain;

    @ApiModelProperty(value = "创建人id")
    private Long createById;

    @ApiModelProperty(value = "内容分类：1-知识点；2-问题")
    private Integer type;

    @ApiModelProperty(value = "录音文件地址")
    private String soundUrl;

    @ApiModelProperty(value = "律师点击量")
    private Integer lawyerClickCount;

    @ApiModelProperty(value = "客服点击量")
    private Integer agentClickCount;


}
