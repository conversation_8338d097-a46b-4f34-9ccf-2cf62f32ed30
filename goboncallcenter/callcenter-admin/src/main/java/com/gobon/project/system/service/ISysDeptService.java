package com.gobon.project.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gobon.framework.web.domain.TreeSelect;
import com.gobon.project.system.domain.SysDept;
import com.gobon.project.system.domain.entity.SysDeptEntity;
import com.gobon.project.system.domain.param.DeptUserParam;
import com.gobon.project.system.domain.param.SysDeptParam;
import com.gobon.project.system.domain.vo.DeptTreeVO;

import java.util.List;

/**
 * 部门管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysDeptService extends IService<SysDeptEntity>
{
    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    public List<SysDept> selectDeptList(SysDept dept);

    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    public List<SysDept> buildDeptTree(List<SysDept> depts);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts);

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    public List<Integer> selectDeptListByRoleId(Long roleId);

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    public SysDept selectDeptById(Long deptId);

    /**
     * 是否存在部门子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public boolean hasChildByDeptId(Long deptId);

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    public boolean checkDeptExistUser(Long deptId);

    /**
     * 校验部门名称是否唯一
     *
     * @param dept 部门信息
     * @return 结果
     */
    public String checkDeptNameUnique(SysDept dept);

    /**
     * 新增保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    public int insertDept(SysDept dept);

    /**
     * 修改保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    public int updateDept(SysDept dept);

    /**
     * 修改部门下级 使用域id为空 的使用域
     *
     * @param deptIds 部门id集合
     * @return 结果
     */
    int updateDeptChildrenDomainByDomainIdIsNull(SysDept dept);


    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public int deleteDeptById(Long deptId);

    /**
     * 根据部门父级id获取所有子部门  权限查询
     * @param param
     * @return
     */
    public List<DeptTreeVO> getDeptTreeByParentIdData(DeptUserParam param);

    /**
     * 根据部门父级id获取所有子部门  免权限查询
     * @param param
     * @return
     */
    public List<DeptTreeVO> getDeptTreeByParentIdExemptionJurisdiction(DeptUserParam param);


    /**
     * 根据部门子级id获取所有父级部门
     *
     * @param
     * @return
     */
    List<SysDept> getDeptParentByChildrenId(SysDept sysDept);

    /**
     * 按权限加载的部门（组装成下拉框）
     * @param sysDept
     * @return
     */
    List<SysDept> getDeptTreeByParentIdSelect(SysDept sysDept);

    /**
     * 根据部门id 获取父级域
     * @param sysDept
     * @return
     */
    List<String> getDeptParentDomainByDeptId(SysDept sysDept);

    /**
     * 获取所有或指定 顶级域部门
     * @return
     */
    List<SysDept> getAllOrDesignationTopLevelDept( SysDeptParam param );

    /**
     * 获取顶级域所有部门
     * @return
     */
    List<SysDept> getAllTopLevelAllDept(Long deptId, boolean domainIdIsNotNull);


    /**
     * 获取当前部门上一个顶级部门
     * @param deptId
     * @return
     */
    SysDept getTopLevelDept(Long deptId);
}
