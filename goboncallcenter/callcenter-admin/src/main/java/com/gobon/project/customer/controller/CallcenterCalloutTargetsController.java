package com.gobon.project.customer.controller;

import com.gobon.common.utils.SecurityUtils;
import com.gobon.common.utils.ServletUtils;
import com.gobon.common.utils.poi.ExcelUtil;
import com.gobon.framework.aspectj.lang.annotation.Log;
import com.gobon.framework.aspectj.lang.enums.BusinessType;
import com.gobon.framework.security.LoginUser;
import com.gobon.framework.security.service.TokenService;
import com.gobon.framework.web.controller.BaseController;
import com.gobon.framework.web.domain.AjaxResult;
import com.gobon.framework.web.page.TableDataInfo;
import com.gobon.project.customer.domain.CallcenterCalloutTargets;
import com.gobon.project.customer.domain.vo.CallcenterCalloutTargetsVo;
import com.gobon.project.customer.domain.vo.DialplanParams;
import com.gobon.project.customer.domain.vo.ImportCustomerPlanResultVo;
import com.gobon.project.customer.service.ICallcenterCalloutTargetsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 外呼计划目标客户Controller
 *
 * <AUTHOR>
 * @date 2020-03-24
 */
@Api(description = "外呼计划目标客户")
@RestController
@RequestMapping("/customer/targets")
public class CallcenterCalloutTargetsController extends BaseController {
    @Autowired
    private ICallcenterCalloutTargetsService callcenterCalloutTargetsService;
    @Autowired
    private TokenService tokenService;

    /**
     * 查询外呼计划目标客户列表
     */
    @ApiOperation("查询外呼计划目标客户列表")
    @GetMapping("/list")
    public TableDataInfo list(CallcenterCalloutTargets callcenterCalloutTargets) {
        startPage();
        List<CallcenterCalloutTargetsVo> list = callcenterCalloutTargetsService.selectCallcenterCalloutTargetsList(callcenterCalloutTargets);
        return getDataTable(list);
    }

    /**
     * 我的呼叫列表
     */
    @ApiOperation("我的呼叫列表")
    @GetMapping("/myCallList")
    public AjaxResult myCallList(CallcenterCalloutTargets callcenterCalloutTargets) {
        callcenterCalloutTargets.setUserId(SecurityUtils.getLoginUser().getUser().getUserId());
        callcenterCalloutTargets.setStateList("1");
        List<CallcenterCalloutTargetsVo> list = callcenterCalloutTargetsService.selectCallcenterCalloutTargetsList(callcenterCalloutTargets);
        return AjaxResult.success(list);
    }

    /**
     * 导出外呼计划目标客户列表
     */
    @ApiOperation("导出外呼计划目标客户列表")
    @Log(title = "外呼计划目标客户", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(CallcenterCalloutTargets callcenterCalloutTargets) {
        List<CallcenterCalloutTargetsVo> list = callcenterCalloutTargetsService.selectCallcenterCalloutTargetsList(callcenterCalloutTargets);
        ExcelUtil<CallcenterCalloutTargetsVo> util = new ExcelUtil<CallcenterCalloutTargetsVo>(CallcenterCalloutTargetsVo.class);
        return util.exportExcel(list, "targets");
    }

    /**
     * 获取外呼计划目标客户详细信息
     */
    @ApiOperation("获取外呼计划目标客户详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(callcenterCalloutTargetsService.selectCallcenterCalloutTargetsById(id));
    }

    /**
     * 新增外呼计划目标客户
     */
    @ApiOperation("新增外呼计划目标客户")
    @Log(title = "外呼计划目标客户", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CallcenterCalloutTargets callcenterCalloutTargets) {
        return toAjax(callcenterCalloutTargetsService.insertCallcenterCalloutTargets(callcenterCalloutTargets));
    }

    /**
     * 修改外呼计划目标客户
     */
    @ApiOperation("修改外呼计划目标客户")
    @Log(title = "外呼计划目标客户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CallcenterCalloutTargetsVo callcenterCalloutTargets) {
        return toAjax(callcenterCalloutTargetsService.updateCallcenterCalloutTargets(callcenterCalloutTargets));
    }

    /**
     * 删除外呼计划目标客户
     */
    @ApiOperation("删除外呼计划目标客户")
    @Log(title = "外呼计划目标客户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(callcenterCalloutTargetsService.deleteCallcenterCalloutTargetsByIds(ids));
    }

    /**
     * 添加至外呼列表
     */
    @ApiOperation("添加至外呼列表")
    @Log(title = "添加至外呼列表", businessType = BusinessType.UPDATE)
    @PostMapping("/updateCallList")
    public AjaxResult updateCallList(@RequestBody DialplanParams param) {
        return toAjax(callcenterCalloutTargetsService.updateCallList(param));
    }

    /**
     * 更新呼叫次数
     */
    @ApiOperation("更新呼叫次数")
    @Log(title = "更新呼叫次数", businessType = BusinessType.UPDATE)
    @GetMapping("/updateCalls")
    public AjaxResult updateCalls(Long id) {
        return toAjax(callcenterCalloutTargetsService.updateCalls(id));
    }


    @ApiOperation("新建外呼任务客户导入")
    @Log(title = "新建外呼任务客户导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importPlanData")
    public AjaxResult importPlanData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<CallcenterCalloutTargets> util = new ExcelUtil<CallcenterCalloutTargets>(CallcenterCalloutTargets.class);
        List<CallcenterCalloutTargets> userList = util.importExcel(file.getInputStream());
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String operName = loginUser.getUsername();
        ImportCustomerPlanResultVo message = callcenterCalloutTargetsService.importPlanCustomer(userList, operName);
        return AjaxResult.success(message);
    }

    /**
     * 获取导入模板
     *
     * @return
     */
    @ApiOperation("获取导入模板")
    @GetMapping("/importTemplate")
    public AjaxResult importTemplate() {
        ExcelUtil<CallcenterCalloutTargets> util = new ExcelUtil<CallcenterCalloutTargets>(CallcenterCalloutTargets.class);
        return util.importTemplateExcel("客户导入模板");
    }
}
