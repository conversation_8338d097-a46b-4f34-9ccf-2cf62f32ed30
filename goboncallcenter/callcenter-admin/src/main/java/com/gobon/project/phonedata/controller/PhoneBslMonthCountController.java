package com.gobon.project.phonedata.controller;

import com.gobon.framework.web.controller.BaseController;
import com.gobon.framework.web.domain.R;
import com.gobon.project.phonedata.pojo.params.PhoneBslMonthCountAddParam;
import com.gobon.project.phonedata.pojo.params.PhoneBslMonthCountDelParam;
import com.gobon.project.phonedata.pojo.params.PhoneBslMonthCountUpdateParam;
import com.gobon.project.phonedata.pojo.params.PhoneBslQueryParam;
import com.gobon.project.phonedata.pojo.vo.PhoneBslMonthCountVO;
import com.gobon.project.phonedata.service.PhoneBslMonthCountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 办事量统计(PhoneBslMonthCount)表控制层
 *
 * <AUTHOR>
 * @since 2024-12-26 07:48:23
 */
@RestController
@RequestMapping("/phonedata/phoneBslMonthCount")
@Api(tags = "办事量月度统计接口")
public class PhoneBslMonthCountController extends BaseController {
    /**
     * 服务对象
     */
    @Autowired
    private PhoneBslMonthCountService phoneBslMonthCountService;


    /**
     * 查询列表
     * @param param
     * @return
     */
    @ApiOperation(value = "查询列表(不分页)")
    @GetMapping(value = "findList")
    public R<List<PhoneBslMonthCountVO>> findList(PhoneBslQueryParam param){
        return R.ok(phoneBslMonthCountService.findList(param));
    }


    /**
     * 办事量统计新增接口
     *
     * @param param
     * @return
     */
    @PostMapping(value = "add")
    @ApiOperation(value = "办事量月度统计新增接口")
    public R add(@Validated @RequestBody PhoneBslMonthCountAddParam param) {
        return phoneBslMonthCountService.add(param);
    }

    /**
     * 办事量统计修改接口
     *
     * @param param
     * @return
     */
    @PutMapping(value = "update")
    @ApiOperation(value = "办事量月度统计修改接口")
    public R update(@Validated @RequestBody PhoneBslMonthCountUpdateParam param) {
        return phoneBslMonthCountService.update(param);
    }


    /**
     * 办事量统计删除接口
     *
     * @param param
     * @return
     */
    @DeleteMapping(value = "delete")
    @ApiOperation(value = "办事量月度统计删除接口")
    public R delete(@Validated @RequestBody PhoneBslMonthCountDelParam param) {
        return phoneBslMonthCountService.delete(param);
    }

    /**
     * 导出数据
     * @param param
     */
    @GetMapping(value = "exportData")
    @ApiOperation(value = "导出数据")
    public R exportData(PhoneBslQueryParam param){
        return phoneBslMonthCountService.exportData(param);
    }

}
