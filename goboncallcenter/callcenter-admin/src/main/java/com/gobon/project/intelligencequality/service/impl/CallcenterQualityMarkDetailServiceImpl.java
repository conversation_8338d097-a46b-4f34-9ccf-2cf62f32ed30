package com.gobon.project.intelligencequality.service.impl;

import com.gobon.common.utils.DateUtils;
import com.gobon.project.intelligencequality.domain.CallcenterQualityMarkDetail;
import com.gobon.project.intelligencequality.domain.param.QualityScoreResultParam;
import com.gobon.project.intelligencequality.domain.vo.QualityMarkDetailVo;
import com.gobon.project.intelligencequality.mapper.CallcenterQualityMarkDetailMapper;
import com.gobon.project.intelligencequality.service.ICallcenterQualityMarkDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 质检标记明细Service业务层处理
 * <AUTHOR>
 * @date 2020-08-26
 */
@Service
public class CallcenterQualityMarkDetailServiceImpl implements ICallcenterQualityMarkDetailService {
    @Autowired
    private CallcenterQualityMarkDetailMapper callcenterQualityMarkDetailMapper;

    /**
     * 查询质检标记明细
     * @param id 质检标记明细ID
     * @return 质检标记明细
     */
    @Override
    public CallcenterQualityMarkDetail selectCallcenterQualityMarkDetailById(Long id) {
        return callcenterQualityMarkDetailMapper.selectCallcenterQualityMarkDetailById(id);
    }

    /**
     * 查询质检标记明细列表
     * @param callcenterQualityMarkDetail 质检标记明细
     * @return 质检标记明细
     */
    @Override
    public List<CallcenterQualityMarkDetail> selectCallcenterQualityMarkDetailList(CallcenterQualityMarkDetail callcenterQualityMarkDetail) {
        return callcenterQualityMarkDetailMapper.selectCallcenterQualityMarkDetailList(callcenterQualityMarkDetail);
    }

    /**
     * 新增质检标记明细
     * @param callcenterQualityMarkDetail 质检标记明细
     * @return 结果
     */
    @Override
    public int insertCallcenterQualityMarkDetail(CallcenterQualityMarkDetail callcenterQualityMarkDetail) {
        callcenterQualityMarkDetail.setCreateTime(DateUtils.getNowDate());
        return callcenterQualityMarkDetailMapper.insertCallcenterQualityMarkDetail(callcenterQualityMarkDetail);
    }

    /**
     * 修改质检标记明细
     * @param callcenterQualityMarkDetail 质检标记明细
     * @return 结果
     */
    @Override
    public int updateCallcenterQualityMarkDetail(CallcenterQualityMarkDetail callcenterQualityMarkDetail) {
        return callcenterQualityMarkDetailMapper.updateCallcenterQualityMarkDetail(callcenterQualityMarkDetail);
    }

    /**
     * 批量删除质检标记明细
     * @param ids 需要删除的质检标记明细ID
     * @return 结果
     */
    @Override
    public int deleteCallcenterQualityMarkDetailByIds(Long[] ids) {
        return callcenterQualityMarkDetailMapper.deleteCallcenterQualityMarkDetailByIds(ids);
    }

    /**
     * 删除质检标记明细信息
     * @param id 质检标记明细ID
     * @return 结果
     */
    @Override
    public int deleteCallcenterQualityMarkDetailById(Long id) {
        return callcenterQualityMarkDetailMapper.deleteCallcenterQualityMarkDetailById(id);
    }

    @Override
    public List<QualityMarkDetailVo> selectMarkDetails(QualityScoreResultParam param) {
        return callcenterQualityMarkDetailMapper.selectMarkDetails(param);
    }
}
