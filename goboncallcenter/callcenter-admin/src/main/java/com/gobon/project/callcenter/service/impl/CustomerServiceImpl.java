package com.gobon.project.callcenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gobon.common.CustomerServiceStatus;
import com.gobon.common.constant.ConfigCommonConstants;
import com.gobon.common.constant.Constants;
import com.gobon.common.constant.freeswitch.FreeswitchOptionEnum;
import com.gobon.common.exception.CustomException;
import com.gobon.common.spring.SpringUtils;
import com.gobon.common.utils.CommonUtils;
import com.gobon.common.utils.DateUtils;
import com.gobon.common.utils.SecurityUtils;
import com.gobon.common.utils.StringUtils;
import com.gobon.common.utils.bean.BeanCopierUtils;
import com.gobon.framework.aspectj.lang.annotation.DataScopeDeptSkillGroup;
import com.gobon.framework.minio.MinioConstants;
import com.gobon.framework.minio.MinioProp;
import com.gobon.framework.redis.RedisCache;
import com.gobon.framework.security.LoginUser;
import com.gobon.framework.web.domain.BaseEntity;
import com.gobon.project.callcenter.domain.CallObjectInfo;
import com.gobon.project.callcenter.domain.CustomerServiceInfo;
import com.gobon.project.callcenter.domain.SkillGroupCustomerServiceInfo;
import com.gobon.project.callcenter.domain.param.StateSwitchParam;
import com.gobon.project.callcenter.domain.param.UserAuthParam;
import com.gobon.project.callcenter.domain.param.UserAuthSqlParam;
import com.gobon.project.callcenter.domain.vo.UserTodoCount;
import com.gobon.project.callcenter.enums.CallcenterMultipartyErrorCode;
import com.gobon.project.callcenter.enums.UserAuthEnum;
import com.gobon.project.callcenter.exception.CallCenterMultipartyException;
import com.gobon.project.callcenter.mapper.CustomerServiceMapper;
import com.gobon.project.callcenter.redis.UserServiceStatusPublisher;
import com.gobon.project.callcenter.service.ICustomerService;
import com.gobon.project.system.constant.SkillGroupConstant;
import com.gobon.project.system.domain.SysDept;
import com.gobon.project.system.domain.SysDomain;
import com.gobon.project.system.domain.SysUser;
import com.gobon.project.system.domain.entity.SysSkillGroupEntity;
import com.gobon.project.system.domain.vo.SysSkillGroupVO;
import com.gobon.project.system.mapper.SysUserMapper;
import com.gobon.project.system.service.ISysDomainService;
import com.gobon.project.system.service.ISysSkillGroupService;
import com.gobon.project.system.service.ISysUserService;
import com.gobontech.cc.callcenter.acd.*;
import com.gobontech.cc.callcenter.acd.queue.GroupWrap;
import com.gobontech.cc.callcenter.constant.MqConstant;
import com.gobontech.cc.callcenter.constant.RedisConstant;
import com.gobontech.cc.callcenter.domain.mq.MsgObj;
import com.gobontech.cc.callcenter.enums.AgentOpsEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @projectName: goboncallcenter
 * @package: com.gobon.project.callcenter.service.impl
 * @className: CustomerServiceImpl
 * @author: KimHung Yic
 * @description:
 * @date: 2020/7/9 15:03
 * @version: 1.0
 */
@Service
@Slf4j
public class CustomerServiceImpl implements ICustomerService {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Resource
    private CustomerServiceMapper customerServiceMapper;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private UserServiceStatusPublisher userStatusPublisher;

//    @Autowired
//    private MinioProp minioProp;

    @Value("${localFilePath}")
    private String localFilePath;

    @Autowired
    public RedisTemplate redisTemplate;

    @Autowired
    public StringRedisTemplate stringRedisTemplate;

    @Autowired
    public ISysDomainService sysDomainService;

    @Autowired
    public ISysSkillGroupService sysSkillGroupService;

    /*@Value("${token.expireTime}")
    private Long expireTime;*/

    @Override
    public List<SysUser> getCustomerService(SysUser sysUser) {
        return sysUserMapper.selectUserListBySkillGroup(sysUser);
    }

    /**
     * 更新用户服务状态
     */
    @Override
    public void updateCustomerServiceStatus(StateSwitchParam stateSwitchParam) {
        LoginUser loginUser = stateSwitchParam.getLoginUser();
        AgentOpsEnum agentOpsEnum = stateSwitchParam.getAgentOpsEnum();
        if(loginUser == null || AgentOpsEnum.ADD == agentOpsEnum) {
            return;
        }

        FreeswitchOptionEnum freeswitchOptionEnum = stateSwitchParam.getFreeswitchOptionEnum();
        String addrArea = loginUser.getUser().getAddrArea();
        String freeswitchPhone = loginUser.getUser().getFreeswitchPhone();
        Integer serviceStatus = -1;
        SysUser user = loginUser.getUser();
        if(StringUtils.isNotEmpty(addrArea) && StringUtils.isNotEmpty(freeswitchPhone)){
            if(AgentOpsEnum.AVAILABLE == agentOpsEnum ||
                    (freeswitchOptionEnum != null && freeswitchOptionEnum.stringValue().startsWith("2"))){
                serviceStatus = CustomerServiceStatus.NORMAL.getCode();
            } else {
                serviceStatus = CustomerServiceStatus.NO_SERVICE.getCode();
            }
        }

        if(CustomerServiceStatus.NORMAL.getCode().equals(serviceStatus)) {
            user.setBeginFreeTime(DateUtils.ignoreMs(new Date()));
        }

        String key = String.format(Constants.CUSTOMER_SERVICE_INFO,user.getAddrArea(),user.getUserId());
        //redisCache.setCacheObject(key,loginUser,expire.intValue(), TimeUnit.MINUTES);
        CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo().setInfo(loginUser);
        customerServiceInfo.setFreeswitchOptionEnum(freeswitchOptionEnum);
        customerServiceInfo.setOptionTypeCode(freeswitchOptionEnum.value());
        customerServiceInfo.setServiceState(serviceStatus);
        // redisCache.setCacheObject(key,loginUser);
        redisCache.setCacheObject(key, customerServiceInfo,5, TimeUnit.HOURS);
        // 小休不推送
        if(freeswitchOptionEnum == null || !freeswitchOptionEnum.stringValue().startsWith("2")){
            userStatusPublisher.publish("",customerServiceInfo);
        }
    }

    @Override
    public void checkCustomerServiceStatus(List<Long> userIds) throws CallCenterMultipartyException {
        if(ObjectUtils.isEmpty(userIds)){
            return;
        }

        SysUser user = sysUserMapper.selectUserById(userIds.get(0));
        Collection<String> keys = redisCache.keys(String.format(Constants.CUSTOMER_SERVICE_INFO,user.getAddrArea(),"*"));

        if(!ObjectUtils.isEmpty(keys)){
            List<String> unServiceableNickNames = keys.stream().map(key -> {
                CustomerServiceInfo customerServiceInfo = redisCache.getCacheObject(key);
                return customerServiceInfo;
            }).filter(customerServiceInfo -> userIds.contains(customerServiceInfo.getUserId())
                    && CustomerServiceStatus.NO_SERVICE.getCode().equals(customerServiceInfo.getServiceState()))
                    .map(customerServiceInfo -> customerServiceInfo.getNickName())
                    .collect(Collectors.toList());

            if(!ObjectUtils.isEmpty(unServiceableNickNames)){
                String msg = CallcenterMultipartyErrorCode.CALL_USER_UNSERVICEABLE.getMsg();
                msg = String.format(msg, StringUtils.join(unServiceableNickNames, ","));
                throw new CallCenterMultipartyException(msg,CallcenterMultipartyErrorCode.CALL_USER_UNSERVICEABLE.getCode());
            }
        }
    }

    @Override
    public void checkCustomerServiceStatus(String phone) throws CallCenterMultipartyException {
        if(StringUtils.isEmpty(phone)){
            return;
        }
        SysUser sysUser = CommonUtils.getInstance().getSysUser();
        Collection<String> keys = redisCache.keys(String.format(Constants.CUSTOMER_SERVICE_INFO,sysUser.getAddrArea(),"*"));

        CustomerServiceInfo customerServiceInfo = keys.stream().map(key -> {
            CustomerServiceInfo customerService = redisCache.getCacheObject(key);
            return customerService;
        }).filter(customerService -> phone.equals(customerService.getFreeswitchPhone()))
                .findFirst().orElse(null);
        if(customerServiceInfo == null || CustomerServiceStatus.NO_SERVICE.getCode().equals(customerServiceInfo.getServiceState())){
            throw new CallCenterMultipartyException("当前客服状态不可呼入",CallcenterMultipartyErrorCode.CALL_USER_UNSERVICEABLE.getCode());
        }
    }

    @Override
    public CallObjectInfo getCallObject(UserAuthParam param) {
        //权限为空返回空
//        if (StringUtils.isBlank(param.getAuth())) {
//            throw new CustomException("无权限查询坐席信息！");
//        }
        SysUser sysUser = CommonUtils.getInstance().getSysUser();
//        String configValue = redisCache.getCacheObject(String.format(Constants.CROSS_DOMAIN_CALL_CONFIG_KEY, sysUser.getAddrArea()));


        List<CustomerServiceInfo> customerServices = null;
        /*if(Constants.YES.equals(configValue)){
            customerServices = customerServiceMapper.selectCustomerServiceListByAddrAreaOrDomain(sysUser.getAddrArea(),"");
        } else {
            if(StringUtils.isNotEmpty(sysUser.getDomain())){
                customerServices = customerServiceMapper.selectCustomerServiceListByAddrAreaOrDomain("",sysUser.getDomain());
            }
        }*/
        UserAuthSqlParam authSqlParam = new UserAuthSqlParam();
        authSqlParam.setAuthType(param.getAuth());
        authSqlParam.setUserTable("su");
        authSqlParam.setUserDomain(sysUser.getDomain());
        //根据权限获取用户
        customerServices = customerServiceMapper.selectUserByAuth(getAuthSql(authSqlParam));
        // 在线客服信息
        List<CustomerServiceInfo> onlineCustomerService = getOnlineCustomerService(customerServices);
//        Map<Long, SkillGroupCustomerServiceInfo> skillGroup = getSkillGroup(onlineCustomerService);
        List<SysSkillGroupEntity> list = sysSkillGroupService.lambdaQuery().eq(SysSkillGroupEntity::getState, Constants.YES)
                .orderByAsc(SysSkillGroupEntity::getCreateTime).list();

        CallObjectInfo callObjectInfo = new CallObjectInfo();
        callObjectInfo.setOnlineCustomerServices(onlineCustomerService);
        callObjectInfo.setSkillGroupVOS(BeanCopierUtils.copyList(list, SysSkillGroupVO.class));
//        callObjectInfo.setCustomerServices(customerServices);
//        callObjectInfo.setSkillGroupCustomerServiceInfoMap(skillGroup);
        return callObjectInfo;
    }

    /**
     * 获取权限sql
     * @param param
     * @return
     */
    public String getAuthSql(UserAuthSqlParam param) {
        StringBuilder sb = new StringBuilder();
        if (param.getAuthType().equals(UserAuthEnum.ALL.getCode())) {
            //所有权限
            sb.append("");
        } else if (param.getAuthType().equals(UserAuthEnum.CURRENT.getCode())) {
            //本分中心
            sb.append(String.format(" and %s.domain = '%s'", param.getUserTable(), param.getUserDomain()));
        } else if (param.getAuthType().equals(UserAuthEnum.CURRENT_AND_DOWN.getCode())) {
            //本分中心及以下
            sb.append(String.format(" and %s.domain like concat(REPLACE('%s','.com',''), '%%')", param.getUserTable(), param.getUserDomain()));
        } else if (param.getAuthType().equals(UserAuthEnum.SAME_GROUP.getCode())) {
            //同组分中心
            //获取当前用户域所在分组
            SysDomain domainQuery = new SysDomain();
            domainQuery.setDomain(param.getUserDomain());
            List<SysDomain> sysDomains = sysDomainService.selectSysDomainList(domainQuery);
            if (CollectionUtils.isEmpty(sysDomains)) {
                sb.append(String.format(" and %s.domain = '%s'", param.getUserTable(), param.getUserDomain()));
            } else {
                StringBuilder typeSql = new StringBuilder();
                String domainType = sysDomains.get(0).getDomainType();
                if (StringUtils.isBlank(domainType)) {
                    typeSql.append(" find_in_set('', domain_type)");
                } else {
                    String[] split = domainType.split(",");
                    //查询分组列表
                    for (int i = 0; i < split.length; i++) {
                        if (i == 0){
                            typeSql.append(String.format(" find_in_set('%s', domain_type)", split[i]));
                        } else {
                            typeSql.append(String.format(" or find_in_set('%s', domain_type)", split[i]));
                        }
                    }
                    sb.append(String.format(" and (%s.domain = '%s' or domain in " +
                            "(select domain from sys_domain where 1 = 1 and %s))", param.getUserTable(), param.getUserDomain(), typeSql.toString()));
                }

            }
        } else {
            log.error("无匹配通话权限: {} {}", param.getUserDomain(), param.getAuthType());
            //默认本分中心
            sb.append(String.format(" and %s.domain = '%s'", param.getUserTable(), param.getUserDomain()));
        }
        return sb.toString();

    }

    @Override
    public void changeCustomerServiceStatus(StateSwitchParam stateSwitchParam) {
        LoginUser loginUser = stateSwitchParam.getLoginUser();
        FreeswitchOptionEnum freeswitchOptionEnum = stateSwitchParam.getFreeswitchOptionEnum();
        AgentOpsEnum agentOpsEnum = stateSwitchParam.getAgentOpsEnum();

        if(loginUser == null || agentOpsEnum == AgentOpsEnum.ADD){
            return;
        }

        // 更新redis坐席状态
        CustomerServiceInfo customerServiceInfo = setCustomerServiceInfo(stateSwitchParam);

        /*if(freeswitchOptionEnum == FreeswitchOptionEnum.HANDLE){
            log.info("**********不推送客服状态**********{},{}",agentOpsEnum,freeswitchOptionEnum);
            return;
        }*/

        // 判断 签入时 是否还存在旧通道id,存在就发命令给服务端 清除通道
        if (freeswitchOptionEnum == FreeswitchOptionEnum.ONLINE) {
            Object mapValue = this.redisTemplate.opsForHash().get(String.format(RedisConstant.ACD_QUEUE_AGENT_ALL, ConfigCommonConstants.domain), loginUser.getUser().getUserNumber());
            if (null != mapValue) {
                AcdAgent aa = (AcdAgent)mapValue;
                double differSecond = DateUtils.diff(aa.getLastUsedDate(), new Date());

                // 判断 当前客服 上一通分配的电话时间  是否 已超过15分钟 都没分配电话
                if (differSecond > 900) {
                    final AcdAgentStatus status = aa.getStatusEnum();
                    if ((status == AcdAgentStatus.Ready || status == AcdAgentStatus.NotReady) && (null != aa.getCallChannelId() || null != aa.getChannelId())) {
                        stateSwitchParam.setUpdateOpt(AcdAgentOpt.CLEAR);
                    }
                }
            }
        }

        String detailKey = String.format(Constants.CUSTOMER_SERVICE_DETAIL_KEY, loginUser.getUser().getUserId());
//        String onlineStatus = stringRedisTemplate.opsForValue().get(detailKey);
//        // 如果推送状态是处理中但上一次是在线则不推送
//        if (StringUtils.isNotBlank(onlineStatus) && onlineStatus.equals(FreeswitchOptionEnum.ONLINE.name()) && FreeswitchOptionEnum.HANDLE.name().equals(freeswitchOptionEnum.name())) {
//            log.info("推送状态是处理中但上一次是在线则不推送:{}", loginUser.getUser().getNickName());
//            return;
//        }
        stringRedisTemplate.opsForValue().set(detailKey, freeswitchOptionEnum.name());
        // 推送坐席信息
        SysUser user = loginUser.getUser();
        AcdAgent acdAgent = new AcdAgent(
                user.getAddrArea(),
                user.getFreeswitchPhone(),
                user.getUserNumber(),
                user.getFreeswitchPassword(),
                "user/" + user.getFreeswitchPhone() + "@" + user.getAddrArea());
        acdAgent.setAudioFile(customerServiceInfo.getUserNumberAudio());
        acdAgent.setUserId(user.getUserId());
        acdAgent.setName(user.getNickName());
        acdAgent.setPriority(user.getPriority());
        acdAgent.setUpdateOpt(stateSwitchParam.getUpdateOpt());
        acdAgent.setOriginalState(freeswitchOptionEnum.name());
        // --- 2021-01-28
        acdAgent.setUniqueCode(user.getDomain());

        AtomicReference<AcdAgentStatus> atomicReference = acdAgent.getStatus();
        atomicReference.set(getAgentStatus(freeswitchOptionEnum));
        //acdAgent.setStatus(getAgentStatus(freeswitchOptionEnum));
        Set<GroupWrap> groupWraps = user.getGroupWraps();
        if(!ObjectUtils.isEmpty(groupWraps)){
            Set<AcdGroup> newGroup = groupWraps.stream().map(group -> {
                AcdGroup acdGroup = new AcdGroup();
                BeanUtils.copyProperties(group, acdGroup);
                acdGroup.setDomain(user.getAddrArea());
                return acdGroup;
            }).collect(Collectors.toSet());
            acdAgent.setGroups(newGroup);
        }
        AcdRegionQueue acdRegionQueue = new AcdRegionQueue();
        acdRegionQueue.setDomain(user.getAddrArea());
        acdRegionQueue.setUniqueCode(user.getDomain());
        acdRegionQueue.setName(Optional.ofNullable(user.getDept()).map(SysDept::getDeptName).orElse(""));
        acdRegionQueue.setId(user.getDeptId()+"");
        acdAgent.setRegionQueue(acdRegionQueue);
        acdAgent.setMessageId(System.currentTimeMillis());
        MsgObj<AcdAgent> msgObj = new MsgObj<AcdAgent>()
                .setData(acdAgent)
                .setCreateTime(LocalDateTime.now())
                .setDomain(user.getAddrArea())
                .setMsgId(UUID.randomUUID().toString());
        log.info("************更改状态推送坐席信息************，{}, {}, {}", acdAgent.getName(), acdAgent.getEmpNo(), acdAgent.getStatus());
//        log.info("************更改状态推送坐席信息************，{}", msgObj);
        rabbitTemplate.convertAndSend(MqConstant.CC_EXCHANGE, MqConstant.ACD_AGENT_CHANGE_STATUS_ROUTE_KEY,msgObj);
    }

    private AcdAgentStatus getAgentStatus(FreeswitchOptionEnum freeswitchOptionEnum) {
        AcdAgentStatus acdAgentStatus = null;
        switch (freeswitchOptionEnum) {
            case ONLINE:
                acdAgentStatus = AcdAgentStatus.Ready;
                break;
            case OFFLINE:
            case LOUGOUT:
                acdAgentStatus = AcdAgentStatus.NotReady;
                break;
            case EAT:
                acdAgentStatus = AcdAgentStatus.EAT;
                break;
            case WC:
                acdAgentStatus = AcdAgentStatus.WC;
                break;
            case REST:
                acdAgentStatus = AcdAgentStatus.REST;
                break;
            case HANG:
                acdAgentStatus = AcdAgentStatus.HANG;
                break;
            case MEETING:
                acdAgentStatus = AcdAgentStatus.MEETING;
                break;
            case TRAIN:
                acdAgentStatus = AcdAgentStatus.REST;
                break;
            case OTHER:
                acdAgentStatus = AcdAgentStatus.REST;
                break;
            case HANDLE:
                acdAgentStatus = AcdAgentStatus.PROCESSING;
                break;
            case CALLING:
                acdAgentStatus = AcdAgentStatus.Busy;
                break;
            default:
                break;
        }
        return acdAgentStatus;
    }

    @Override
    public void updateRedisCustomerServiceInfo(SysUser sysUser) {
        String key = String.format(Constants.CUSTOMER_SERVICE_INFO, sysUser.getAddrArea(), sysUser.getUserId());
        CustomerServiceInfo customerServiceInfo = redisCache.getCacheObject(key);
        if(customerServiceInfo != null){
            if(StringUtils.isNotEmpty(sysUser.getAddrArea())){
                customerServiceInfo.setAddrArea(sysUser.getAddrArea());
            }
            customerServiceInfo.setNickName(sysUser.getNickName());
            customerServiceInfo.setUserName(sysUser.getUserName());
            customerServiceInfo.setUserNumber(sysUser.getUserNumber());
            customerServiceInfo.setUserNumberAudio(sysUser.getUserAudio());
            customerServiceInfo.setGroupWraps(sysUser.getGroupWraps());
            customerServiceInfo.setLabelSortId(sysUser.getLabelSortId());
            customerServiceInfo.setPriority(sysUser.getPriority());
            redisCache.setCacheObject(key, customerServiceInfo,5, TimeUnit.HOURS);
//            sendAcdAgentInfo(customerServiceInfo);
        }
    }

    @Override
    @DataScopeDeptSkillGroup( tebleAlias = "su", deptAlias = "sd", userAlias = "su", skillType = SkillGroupConstant.CALL )
    public List<CustomerServiceInfo> selectCustomerServiceInfoListToAuth(BaseEntity baseEntity) {
        return customerServiceMapper.selectCustomerServiceList(baseEntity);
    }

    @Override
    public UserTodoCount getUserTodoCount() {
        return customerServiceMapper.getUserTodoCount(SecurityUtils.getLoginUser().getUser().getUserId());
    }

    /**
     * 推送acd客服信息
     * @param customerServiceInfo
     */
    private void sendAcdAgentInfo(CustomerServiceInfo customerServiceInfo) {
        AcdAgent acdAgent = new AcdAgent(
                customerServiceInfo.getAddrArea(),
                customerServiceInfo.getFreeswitchPhone(),
                customerServiceInfo.getUserNumber(),
                customerServiceInfo.getFreeswitchPassword(),
                "user/" + customerServiceInfo.getFreeswitchPhone() + "@" + customerServiceInfo.getAddrArea());
        acdAgent.setAudioFile(customerServiceInfo.getUserNumberAudio());
        acdAgent.setUserId(customerServiceInfo.getUserId());
        acdAgent.setName(customerServiceInfo.getNickName());
        acdAgent.setPriority(customerServiceInfo.getPriority());
        Set<GroupWrap> groupWraps = customerServiceInfo.getGroupWraps();
        if(!ObjectUtils.isEmpty(groupWraps)){
            Set<AcdGroup> newGroup = groupWraps.stream().map(group -> {
                AcdGroup acdGroup = new AcdGroup();
                BeanUtils.copyProperties(group, acdGroup);
                acdGroup.setDomain(customerServiceInfo.getAddrArea());
                return acdGroup;
            }).collect(Collectors.toSet());
            acdAgent.setGroups(newGroup);
        }

        AtomicReference<AcdAgentStatus> atomicReference = acdAgent.getStatus();
        atomicReference.set(getAgentStatus(customerServiceInfo.getFreeswitchOptionEnum()));
        //acdAgent.setStatus(getAgentStatus(customerServiceInfo.getFreeswitchOptionEnum()));

        MsgObj<AcdAgent> msgObj = new MsgObj<AcdAgent>()
                .setData(acdAgent)
                .setCreateTime(LocalDateTime.now())
                .setDomain(customerServiceInfo.getAddrArea())
                .setMsgId(UUID.randomUUID().toString());
        rabbitTemplate.convertAndSend(MqConstant.CC_EXCHANGE, MqConstant.ACD_AGENT_CHANGE_GROUP_ROUTE_KEY,msgObj);
        log.info("************后台更新坐席信息推送************，{}", JSON.toJSONString(acdAgent));
    }

    private CustomerServiceInfo setCustomerServiceInfo(StateSwitchParam stateSwitchParam) {
        LoginUser loginUser = stateSwitchParam.getLoginUser();
        FreeswitchOptionEnum freeswitchOptionEnum = stateSwitchParam.getFreeswitchOptionEnum();
        AgentOpsEnum agentOpsEnum = stateSwitchParam.getAgentOpsEnum();
        SysUser user = loginUser.getUser();
        Integer serviceStatus = -1;

        if(AgentOpsEnum.AVAILABLE == agentOpsEnum ||
                (freeswitchOptionEnum != null && freeswitchOptionEnum.stringValue().startsWith("2"))){
            serviceStatus = CustomerServiceStatus.NORMAL.getCode();
        } else {
            serviceStatus = CustomerServiceStatus.NO_SERVICE.getCode();
        }

        if(CustomerServiceStatus.NORMAL.getCode().equals(serviceStatus)) {
            user.setBeginFreeTime(DateUtils.ignoreMs(new Date()));
        }

        String key = String.format(Constants.CUSTOMER_SERVICE_INFO, user.getAddrArea(), user.getUserId());
        CustomerServiceInfo customerServiceInfo = new CustomerServiceInfo().setInfo(loginUser);
        customerServiceInfo.setFreeswitchOptionEnum(freeswitchOptionEnum);
        customerServiceInfo.setOptionTypeCode(freeswitchOptionEnum.value());
        customerServiceInfo.setServiceState(serviceStatus);
        customerServiceInfo.setUserNumberAudio(localFilePath + MinioConstants.MINIO_USERNUMBER_BUCKET + "/" + user.getUserNumberAudio());
        redisCache.setCacheObject(key, customerServiceInfo,5, TimeUnit.HOURS);
        return customerServiceInfo;
    }

    /**
     * 构建技能组
     * @param customerServiceInfos
     * @return
     */
    private Map<Long, SkillGroupCustomerServiceInfo> getSkillGroup(List<CustomerServiceInfo> customerServiceInfos){
        SysUser currentUser = CommonUtils.getInstance().getSysUser();
        Map<Long, SkillGroupCustomerServiceInfo> map = new HashMap<>(16);
        if(!ObjectUtils.isEmpty(customerServiceInfos)){
            for (CustomerServiceInfo customerServiceInfo : customerServiceInfos) {
                Set<GroupWrap> groupWraps = customerServiceInfo.getGroupWraps();
                if(!currentUser.getUserId().equals(customerServiceInfo.getUserId()) && !ObjectUtils.isEmpty(groupWraps)){
                    for(GroupWrap groupWrap : groupWraps){
                        SkillGroupCustomerServiceInfo skillGroupCustomerServiceInfo = map.get(groupWrap.getUnique());
                        if(skillGroupCustomerServiceInfo == null){
                            skillGroupCustomerServiceInfo = new SkillGroupCustomerServiceInfo();
                            BeanUtils.copyProperties(groupWrap,skillGroupCustomerServiceInfo,"sysUserList");
                            List<CustomerServiceInfo> customerServiceInfoList = new ArrayList<>();
                            customerServiceInfoList.add(customerServiceInfo);
                            skillGroupCustomerServiceInfo.setSysUserList(customerServiceInfoList);
                            if (StringUtils.isNotBlank(groupWrap.getType()) && groupWrap.getType().contains("2")) {
                                map.put(groupWrap.getUnique(),skillGroupCustomerServiceInfo);
                            }
                        } else {
                            List<CustomerServiceInfo> sysUserList = skillGroupCustomerServiceInfo.getSysUserList();
                            sysUserList.add(customerServiceInfo);
                        }
                    }
                }
            }
        }
        return map;
    }

    /**
     * redis 获取在线客服
     * @return
     */
    private List<CustomerServiceInfo> getOnlineCustomerService(List<CustomerServiceInfo> customerServices){
        if(ObjectUtils.isEmpty(customerServices)){
            return null;
        }
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Collection<String> keys = redisCache.keys(String.format(Constants.CUSTOMER_SERVICE_INFO,user.getAddrArea(),"*"));
        List<CustomerServiceInfo> customers = null;
        List<CustomerServiceInfo> sortCustomers = null;
        if(!ObjectUtils.isEmpty(keys)){
            customers = keys.stream().map(key -> redisCache.<CustomerServiceInfo>getCacheObject(key)).collect(Collectors.toList());
        }
        if(!ObjectUtils.isEmpty(customers)){
            Date now = DateUtils.ignoreMs(new Date());
            sortCustomers = customers
                    .stream()
                    .filter(c ->
                        {
                            if (!(FreeswitchOptionEnum.ONLINE == c.getFreeswitchOptionEnum())) {
                                return false;
                            }
                            if (StringUtils.isBlank(c.getSkillGroupId())) {
                                return false;
                            }
                            if (StringUtils.isNotBlank(c.getCallType()) && !c.getCallType().contains("2")) {
                                return false;
                            }
                            c.setDuration((int)Math.ceil(DateUtils.diff(c.getBeginFreeTime(), now)));
                            return true;
                        }
                     )
                        .sorted(Comparator.comparing(CustomerServiceInfo::getDuration)).collect(Collectors.toList());
        }
        return sortCustomers;
    }

}
