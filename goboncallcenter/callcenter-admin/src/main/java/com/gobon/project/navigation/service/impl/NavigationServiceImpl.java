package com.gobon.project.navigation.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gobon.common.BeanUtil;
import com.gobon.common.constant.ConfigCommonConstants;
import com.gobon.common.constant.ConfigConstants;
import com.gobon.common.constant.Constants;
import com.gobon.common.exception.CustomException;
import com.gobon.common.utils.SecurityUtils;
import com.gobon.framework.config.properties.GobonProperties;
import com.gobon.framework.minio.MinioConstants;
import com.gobon.framework.minio.MinioUtil;
import com.gobon.framework.redis.RedisCache;
import com.gobon.project.customizevoice.mapper.SysCustomizeVoiceMapper;
import com.gobon.project.ivrnavigation.domain.vo.IVRSettingVo;
import com.gobon.project.ivrnavigation.service.impl.SysNavigationServiceImpl;
import com.gobon.project.navigation.domain.*;
import com.gobon.project.navigation.domain.enums.ActionLevelTypeEnum;
import com.gobon.project.navigation.domain.enums.NavigationTypeEnum;
import com.gobon.project.navigation.domain.enums.NodeKeyEnum;
import com.gobon.project.navigation.domain.enums.ServiceTimeTypeEnum;
import com.gobon.project.navigation.domain.param.*;
import com.gobon.project.navigation.domain.vo.*;
import com.gobon.project.navigation.mapper.*;
import com.gobon.project.navigation.service.INavigationActionLevelService;
import com.gobon.project.navigation.service.INavigationService;
import com.gobon.project.system.domain.SysSkillGroup;
import com.gobon.project.system.mapper.SysSkillGroupMapper;
import com.gobon.project.system.service.ISysConfigService;
import com.gobon.project.tool.json.DataToXml;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 导航配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-21
 */
@Service
@Slf4j
public class NavigationServiceImpl extends ServiceImpl<NavigationMapper, Navigation> implements INavigationService {


    @Resource
    private NavigationMapper navigationMapper;
    @Resource
    private NavigationActionLevelMapper navigationActionLevelMapper;
    @Resource
    private NavigationServiceTimeMapper serviceTimeMapper;
    @Resource
    private NavigationVisitMapper navigationVisitMapper;
    @Resource
    private INavigationActionLevelService actionLevelService;
    @Resource
    private SysCustomizeVoiceMapper customizeVoiceMapper;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private SysNavigationServiceImpl sysNavigationService;
    @Resource
    private NavigationNodeMapper nodeMapper;
    @Resource
    private SysSkillGroupMapper skillGroupMapper;
    @Resource
    private NavigationNodeRouteMapper nodeRouteMapper;
//    @Resource
//    private MinioUtil minioUtil;
    @Resource
    private RedisCache redisCache;

    @Value("${minio.localpath}")
    private String minioLocalPath;
    @Value("${localFilePath}")
    private String localFilePath;

    public final static Map<String, String> LANGUAGE = new HashMap<>();

    static {
        LANGUAGE.put("1", "普通话");
        LANGUAGE.put("2", "粤语");
        LANGUAGE.put("3", "潮汕话");
        LANGUAGE.put("4", "客家话");
    }




    @Override
    public List<NavigationPageVo> listPage(NavigationQueryParam param) {

        return navigationMapper.getNavigationPage(param);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addOrUpdateNavigation(NavigationAddParam param) {
        List<Navigation> list = this.list(Wrappers.<Navigation>lambdaQuery()
                .eq(Navigation::getIvrRootName, param.getIvrRootName()));
        if (CollectionUtils.isNotEmpty(list) && param.getId() == null) {
            log.error("IVR根节点名称重复:" + param.getIvrRootName());
            throw new CustomException("IVR根节点名称重复:" + param.getIvrRootName());
        }
        //获取顶级域
        String domain = sysConfigService.selectConfigByKey(Constants.ROOT_DOMAIN);
        param.setDomain(domain);
        Navigation entity = new Navigation();
        BeanUtils.copyProperties(param, entity);
        if (param.getId() == null) {
            entity.setCreateBy(SecurityUtils.getUsername());
            entity.setCreateTime(new Date());
        }
        entity.setUpdateBy(SecurityUtils.getUsername())
            .setUpdateTime(new Date())
            .setDelFlag(0);
        entity.setDomain(ConfigConstants.domain);
        //保存导航
        this.saveOrUpdate(entity);
        //判断动作内容是否为语音
        boolean audioFlag = false;
        if (NavigationTypeEnum.AUDION.getCode().equals(param.getNavigationType()) ||
                NavigationTypeEnum.PHONE.getCode().equals(param.getNavigationType())) {
            audioFlag = true;
        }
        //保存导航动作内容
        String actionContentStr = param.getNavigationContent();
        if (StringUtils.isNotBlank(param.getNavigationContent())) {
            Integer contentType = actionLevelService.getContentType(param.getNavigationType(), null);
            NavigationActionLevelSaveParam actionLevelSaveParam = new NavigationActionLevelSaveParam();
            //动作内容转换为页面显示
            if (StringUtils.isNotBlank(actionContentStr) && audioFlag) {
                actionContentStr = customizeVoiceMapper.selectSysCustomizeVoiceById(Long.valueOf(actionContentStr)).getCustomizeVoicePath();
            }

            actionLevelSaveParam.setContentType(contentType)
                    .setNavigationId(entity.getId())
                    .setActionId(entity.getId())
                    .setActionContent(param.getNavigationContent())
                    .setActionContentStr(actionContentStr)
                    .setId(param.getId());
            actionLevelService.saveActionInfo(actionLevelSaveParam);
        }
        //保存导航进入技能组
        if (StringUtils.isNotBlank(param.getSkillId())) {
            NavigationActionLevelSaveParam actionLevelSaveParam = new NavigationActionLevelSaveParam();
            actionLevelSaveParam.setContentType(ActionLevelTypeEnum.TEXT_TIP.getCode())
                    .setNavigationId(entity.getId())
                    .setActionId(entity.getId())
                    .setActionContent(param.getSkillId())
                    .setActionContentStr(param.getSkillId())
                    .setId(entity.getId());
            actionLevelService.saveActionInfo(actionLevelSaveParam);
        }
        //保存服务时间内容
        if (CollectionUtils.isNotEmpty(param.getServiceTimeParams())) {
            if (param.getId() != null) {
                //先删除原有关联
                serviceTimeMapper.delete(Wrappers.<NavigationServiceTime>lambdaQuery()
                        .eq(NavigationServiceTime::getNavigationId, param.getId())
                        .eq(NavigationServiceTime::getNodeId, param.getId()));
            }
            String contentStr = param.getNoserviceRespond();
            //非服务时间提示语转换为页面显示
            if (StringUtils.isNotBlank(contentStr) && audioFlag) {
                contentStr = customizeVoiceMapper.selectSysCustomizeVoiceById(Long.valueOf(contentStr)).getCustomizeVoicePath();
            }
            for (NavigationServiceTimeParam serviceTimeParam : param.getServiceTimeParams()) {
                NavigationServiceTime timeEntity = new NavigationServiceTime()
                        .setNavigationId(entity.getId())
                        .setNodeId(entity.getId())
                        .setServiceStartTime(serviceTimeParam.getStartTime())
                        .setServiceEndTime(serviceTimeParam.getEndTime())
                        .setServiceDate(param.getServiceTimeType())
                        .setNoserviceRespond(param.getNoserviceRespond())
                        .setNoserviceRespondStr(contentStr)
                        .setCreateBy(SecurityUtils.getUsername())
                        .setCreateTime(new Date())
                        .setUpdateBy(SecurityUtils.getUsername())
                        .setUpdateTime(new Date())
                        .setDelFlag(0);
                serviceTimeMapper.insert(timeEntity);
            }

        }
        //保存导航号码以及网址信息
        if (StringUtils.isNotBlank(param.getIvrVisit())) {
            List<NavigationVisit> navigationVisits = navigationVisitMapper.selectList(Wrappers.<NavigationVisit>lambdaQuery()
                    .eq(NavigationVisit::getNavigationId, entity.getId()));
            if (CollectionUtils.isNotEmpty(navigationVisits)) {
                for (NavigationVisit navigationVisit : navigationVisits) {
                    redisCache.hashDelete(Constants.DOMAIN_IVR, navigationVisit.getVisitFlag());
                }
                //删除原有
                navigationVisitMapper.delete(Wrappers.<NavigationVisit>lambdaQuery()
                        .eq(NavigationVisit::getNavigationId, entity.getId()));
            }
            String[] split = param.getIvrVisit().split(",");
            for (String flag : split) {
                NavigationVisit visit = new NavigationVisit();
                visit.setCreateBy(SecurityUtils.getUsername())
                    .setCreateTime(new Date())
                    .setVisitType(param.getNavigationType() + "")
                    .setVisitFlag(flag)
                    .setNavigationId(entity.getId());
                navigationVisitMapper.insert(visit);
            }
        }
        return entity.getId();
    }

    @Override
    public NavigationDetailVo getNavigationInfo(Long id) {
        NavigationDetailVo result = new NavigationDetailVo();
        Navigation navigationEntity = this.getById(id);
        if (navigationEntity != null) {
            BeanUtil.copyProperties(navigationEntity, result);
            //获取动作内容
            List<NavigationActionLevel> actionLevels = navigationActionLevelMapper.selectList(Wrappers.<NavigationActionLevel>lambdaQuery()
                    .eq(NavigationActionLevel::getNavigationId, id)
                    .eq(NavigationActionLevel::getActionId, id));
            if (CollectionUtils.isNotEmpty(actionLevels)) {
                NavigationActionLevel navigationActionLevel = actionLevels.get(0);
                result.setNavigationContent(navigationActionLevel.getActionContent());
            }
            // 获取导航技能组
            //获取动作内容
            List<NavigationActionLevel> skills = navigationActionLevelMapper.selectList(Wrappers.<NavigationActionLevel>lambdaQuery()
                    .eq(NavigationActionLevel::getNavigationId, id)
                    .eq(NavigationActionLevel::getContentType, ActionLevelTypeEnum.TEXT_TIP.getCode())
                    .eq(NavigationActionLevel::getActionId, id));
            if (CollectionUtils.isNotEmpty(skills)) {
                NavigationActionLevel skill = skills.get(0);
                result.setSkillId(skill.getActionContent());
            }
            //获取服务时间
            List<NavigationServiceTime> navigationServiceTimes = serviceTimeMapper.selectList(Wrappers.<NavigationServiceTime>lambdaQuery()
                    .eq(NavigationServiceTime::getNavigationId, id)
                    .eq(NavigationServiceTime::getNodeId, id));
            if (CollectionUtils.isNotEmpty(navigationServiceTimes)) {
                List<NavigationServiceTimeVo> serviceTimeVos;
                serviceTimeVos = navigationServiceTimes.stream().map(it -> {
                    NavigationServiceTimeVo serviceTimeVo = new NavigationServiceTimeVo();
                    serviceTimeVo.setStartTime(it.getServiceStartTime());
                    serviceTimeVo.setEndTime(it.getServiceEndTime());
                    return serviceTimeVo;
                }).collect(Collectors.toList());
                result.setServiceTimeParams(serviceTimeVos);
            }
            //获取域信息
            List<NavigationVisit> navigationDomains = navigationVisitMapper.selectList(Wrappers.<NavigationVisit>lambdaQuery()
                    .eq(NavigationVisit::getNavigationId, id));
            if (CollectionUtils.isNotEmpty(navigationDomains)) {
                String[] ivrVisit = navigationDomains.stream().map(NavigationVisit::getVisitFlag).toArray(String[]::new);
                result.setIvrVisit(StringUtils.join(ivrVisit, ","));
            }
        } else {
            throw new CustomException("无IVR导航匹配数据" + id);
        }
        return result;
    }

    @Override
    public void deleteNavigationById(Long id) {
        Navigation navigationEntity = this.getById(id);
        if (navigationEntity != null) {
            navigationEntity.setDelFlag(1);
            this.updateById(navigationEntity);
        }
    }

    @Override
    public void updateIvrFile(Long id) throws Exception {
        Navigation byId = this.getById(id);
        if (byId == null) {
            throw new CustomException("无IVR导航匹配数据:" + id);
        }
        String domain = byId.getDomain();
        //获取域下所有ivr
        List<NavigationNodeIvrVo> navigationIvr = navigationMapper.getNavigationIvr(domain);
        if (CollectionUtils.isEmpty(navigationIvr)) {
            return;
        }
        StringBuffer sb = new StringBuffer();
        //查找此IVR导航子级结构
        for (NavigationNodeIvrVo navigation : navigationIvr) {
            navigation.setActionType(NodeKeyEnum.TO_NEXT.getCode());
            this.getNavigationByParentId(navigation);
        }
        IVRSettingVo sysIvr = sysNavigationService.getSysIVR();
        sysIvr.setDomain(domain);
        //生成ivr文件
        if (CollectionUtils.isNotEmpty(navigationIvr)) {
            for (NavigationNodeIvrVo nodeIvrVo : navigationIvr) {
                this.getIvrTextStr(sb, nodeIvrVo, sysIvr);
            }
        }
        //添加评价ivr开关
        this.appendSatisfaction(sb, sysIvr);
        //添加留言ivr开关
        this.appendLeaveIvr(sb, sysIvr);
        //获取繁忙ivr开关
        String busymenuIvrSwitch = sysConfigService.selectConfigByKey("busymenu_ivr_switch");
        if (StringUtils.isNotBlank(busymenuIvrSwitch) && "1".equals(busymenuIvrSwitch)) {
            this.appendBusyIvr(sb, sysIvr);
        }
//        String path = DataToXml.stringToTxt("C:/Users/<USER>/Desktop/temp/", sb.toString(), domain);
        String path = DataToXml.stringToTxt(localFilePath + MinioConstants.MINIO_IVRXML_BUCKET + "/", sb.toString(), domain);
        log.info("生成的目录" + path);
        //上传至minio
//        File ivrFile = new File(path);
//        if (ivrFile.exists()) {
//            minioUtil.putObject( MinioConstants.MINIO_IVRXML_BUCKET, path );
//            log.error("上传ivr txt成功");
//        }
    }

    @Override
    public void initIvrNumber() {
        List<NavigationVisit> navigationVisits = navigationVisitMapper.selectList(Wrappers.<NavigationVisit>lambdaQuery()
                .groupBy(NavigationVisit::getVisitFlag).select(NavigationVisit::getVisitFlag));
        if (CollectionUtils.isNotEmpty(navigationVisits)) {
            redisCache.deleteObject(Constants.DOMAIN_IVR);
            for (NavigationVisit navigationVisit : navigationVisits) {
                List<NavigationInitVo> initNavigation = navigationMapper.getInitNavigation(navigationVisit.getVisitFlag());
                for (NavigationInitVo navigationInitVo : initNavigation) {
                    if (StringUtils.isNotBlank(navigationInitVo.getPhoneNumber())) {
                        navigationInitVo.setAudioFile(minioLocalPath + MinioConstants.MINIO_IVR_BUCKET + "/" + navigationInitVo.getAudioFile());
                        navigationInitVo.setUpdateAudio(minioLocalPath + MinioConstants.MINIO_IVR_BUCKET + "/" + navigationInitVo.getUpdateAudio());
                        List<NavigationServiceTime> navigationServiceTimes = serviceTimeMapper.selectList(Wrappers.<NavigationServiceTime>lambdaQuery()
                                .eq(NavigationServiceTime::getNodeId, navigationInitVo.getId())
                        );
                        if (CollectionUtils.isNotEmpty(navigationServiceTimes)) {
                            String serviceTime = navigationServiceTimes.stream().map(it -> (it.getServiceStartTime() + "-" + it.getServiceEndTime())).collect(Collectors.joining(","));
                            navigationInitVo.setServiceTimes(serviceTime);
                            navigationInitVo.setServiceWeekday(ServiceTimeTypeEnum.getIvr(navigationServiceTimes.get(0).getServiceDate()));
                        }
                    }
                }
                redisCache.setCacheMapValue(Constants.DOMAIN_IVR, navigationVisit.getVisitFlag(), initNavigation);
            }
        }
    }

    @Override
    public ConsultSkillVo getConsultSkillByBusiness(ConsultSkillParam param) {
        ConsultSkillVo result = null;
        //获取顶级域
        String domain = sysConfigService.selectConfigByKey(Constants.ROOT_DOMAIN);
        if (domain == null) {
            throw new CustomException("无咨询IVR导航匹配数据:" + param.toString());
        }
        Navigation navigation = this.getOne(Wrappers.<Navigation>lambdaQuery()
                .eq(Navigation::getDomain, domain)
                .eq(Navigation::getNavigationType, NavigationTypeEnum.CONSULT.getCode())
                .eq(Navigation::getNavigationStatus, "1")
                .orderByDesc(Navigation::getCreateTime)
                .last("limit 1")
        );
        if (navigation != null) {
            NavigationNode navigationNode = nodeMapper.selectOne(Wrappers.<NavigationNode>lambdaQuery()
                    .eq(NavigationNode::getNavigationId, navigation.getId())
                    .eq(NavigationNode::getDelFlag, "0")
                    .eq(NavigationNode::getNodeKey, param.getBusinessType())
                    .orderByDesc(NavigationNode::getCreateTime)
                    .last("limit 1")
            );
            if (navigationNode != null) {
                //获取动作内容
                NavigationActionLevel actionLevel = navigationActionLevelMapper.selectOne(Wrappers.<NavigationActionLevel>lambdaQuery()
                        .eq(NavigationActionLevel::getContentType, ActionLevelTypeEnum.TEXT_TIP.getCode())
                        .eq(NavigationActionLevel::getNavigationId, navigationNode.getNavigationId())
                        .eq(NavigationActionLevel::getActionId, navigationNode.getId())
                        .orderByDesc(NavigationActionLevel::getCreateTime)
                        .last("limit 1"));
                if (actionLevel != null) {
                    result = new ConsultSkillVo();
                    //设置默认技能组
                    result.setDefaultSkillId(Long.valueOf(actionLevel.getActionContent()));
                    result.setDefaultDomain(navigationNode.getNodeDomain());
                    //获取是否存在节点路由策略
                    NavigationNodeRoute nodeRoute = nodeRouteMapper.selectOne(Wrappers.<NavigationNodeRoute>lambdaQuery()
                            .eq(NavigationNodeRoute::getNavigationId, navigationNode.getNavigationId())
                            .eq(NavigationNodeRoute::getNodeId, navigationNode.getId())
                            .eq(NavigationNodeRoute::getRouteKey, param.getBelongFlag())
                            .last("limit 1")
                    );
                    result.setSkillId(Optional.ofNullable(nodeRoute).map(NavigationNodeRoute::getSkillId)
                            .orElse(null));
                    result.setDomain(Optional.ofNullable(nodeRoute).map(NavigationNodeRoute::getDomain)
                            .orElse(null));
                }
            }
        }
        if (result != null) {
            if (StringUtils.isNotBlank(param.getBelongFlag())) {
                redisCache.setCacheObject(String.format("%s:%s:%s", Constants.CONSULT_IVR, param.getBusinessType(), param.getBelongFlag()),
                        JSONObject.toJSONString(result));
            } else {
                redisCache.setCacheObject(String.format("%s:%s", Constants.CONSULT_IVR, param.getBusinessType()),
                        JSONObject.toJSONString(result));
            }

        }
        return result;
    }

    @Override
    public List<ConsultBusinessVo> getConsultBusiness() {
        //获取顶级域
        String domain = sysConfigService.selectConfigByKey(Constants.ROOT_DOMAIN);
        if (domain == null) {
            throw new CustomException("无咨询IVR导航匹配数据:" + Constants.ROOT_DOMAIN);
        }
        Navigation navigation = this.getOne(Wrappers.<Navigation>lambdaQuery()
                .eq(Navigation::getDomain, domain)
                .eq(Navigation::getNavigationType, NavigationTypeEnum.CONSULT.getCode())
                .eq(Navigation::getNavigationStatus, "1")
                .orderByDesc(Navigation::getCreateTime)
                .last("limit 1")
        );
        if (navigation != null) {
            return navigationMapper.selectConsultBusiness(navigation.getId());
        }
        return null;
    }

    @Override
    public void initDirectIntoSkill() {
        List<DirectIntoSkillVO> directIntoSkill = navigationMapper.getDirectIntoSkill();
        redisCache.deleteObject(Constants.INIT_DIRECT_INTO_SKILL);
        if (CollectionUtils.isNotEmpty(directIntoSkill)) {
            for (DirectIntoSkillVO directIntoSkillVO : directIntoSkill) {
                redisCache.setCacheMapValue(Constants.INIT_DIRECT_INTO_SKILL, directIntoSkillVO.getPhone(), directIntoSkillVO.getSkillId());
            }
        }
    }

    /**
     * 拼接评价ivr
     * @param ivrStr
     * @return
     */
    public StringBuffer appendSatisfaction(StringBuffer ivrStr, IVRSettingVo sys){
        String afterSatisfied = sysConfigService.selectConfigByKey("after_satisfied_audio");
        String satisfiedAudio = sysConfigService.selectConfigByKey("satisfied_audio");
        // 默认
        ivrStr.append("<menu name=\"satisfaction_ivr\"\n" +
                " greet-long=\""+ satisfiedAudio +"\"\n" +
                " greet-short=\""+ satisfiedAudio +"\"\n" +
                " invalid-sound=\""+ sys.getInvalidSound() +"\"\n" +
                " exit-sound=\""+ sys.getExitSound() +"\"\n" +
                " confirm-macro=\"\"\n" +
                " confirm-key=\"\"\n" +
                " tts-engine=\"\"\n" +
                " tts-voice=\"\"\n" +
                " confirm-attempts=\"3\"\n" +
                " timeout=\"10000\"\n" +
                " inter-digit-timeout=\"2000\"\n" +
                " max-failures=\"3\"\n" +
                " max-timeouts=\"3\"\n" +
                " digit-len=\"1\">\n" +
                "<entry action=\"menu-exec-app\" digits=\"1\" param=\"execute_extension set:satisfaction_class=10,set:satisfaction_goodby="+ afterSatisfied +",park: inline\"/>\n" +
                "<entry action=\"menu-exec-app\" digits=\"2\" param=\"execute_extension set:satisfaction_class=11,set:satisfaction_goodby="+ afterSatisfied +",park: inline\"/>\n" +
                "<entry action=\"menu-exec-app\" digits=\"3\" param=\"execute_extension set:satisfaction_class=12,set:satisfaction_goodby="+ afterSatisfied +",park: inline\"/>\n" +
                "<entry action=\"menu-exec-app\" digits=\"4\" param=\"execute_extension set:satisfaction_class=13,set:satisfaction_goodby="+ afterSatisfied +",park: inline\"/>\n" +
                "<entry action=\"menu-exec-app\" digits=\"5\" param=\"execute_extension set:satisfaction_class=14,set:satisfaction_goodby="+ afterSatisfied +",park: inline\"/>\n" +
                "</menu>");
        // 各语种
        ivrStr.append("<menu name=\"satisfaction_ivr_普通话\"\n" +
                " greet-long=\"/home/<USER>/data/ivr/231008普通话请为我们的服务做出评价.wav\"\n" +
                " greet-short=\"/home/<USER>/data/ivr/231008普通话请为我们的服务做出评价.wav\"\n" +
                " invalid-sound=\"/home/<USER>/data/ivr/231008普通话您的输入有误.wav\"\n" +
                " exit-sound=\"/home/<USER>/data/ivr/231008普通话感谢您的评价.wav\"\n" +
                " confirm-macro=\"\"\n" +
                " confirm-key=\"\"\n" +
                " tts-engine=\"\"\n" +
                " tts-voice=\"\"\n" +
                " confirm-attempts=\"3\"\n" +
                " timeout=\"10000\"\n" +
                " inter-digit-timeout=\"2000\"\n" +
                " max-failures=\"3\"\n" +
                " max-timeouts=\"3\"\n" +
                " digit-len=\"1\">\n" +
                "<entry action=\"menu-exec-app\" digits=\"1\" param=\"execute_extension set:satisfaction_class=10,set:satisfaction_goodby=/home/<USER>/data/ivr/231008普通话感谢您的评价.wav,park: inline\"/>\n" +
                "<entry action=\"menu-exec-app\" digits=\"2\" param=\"execute_extension set:satisfaction_class=11,set:satisfaction_goodby=/home/<USER>/data/ivr/231008普通话感谢您的评价.wav,park: inline\"/>\n" +
                "<entry action=\"menu-exec-app\" digits=\"3\" param=\"execute_extension set:satisfaction_class=12,set:satisfaction_goodby=/home/<USER>/data/ivr/231008普通话感谢您的评价.wav,park: inline\"/>\n" +
                "<entry action=\"menu-exec-app\" digits=\"4\" param=\"execute_extension set:satisfaction_class=13,set:satisfaction_goodby=/home/<USER>/data/ivr/231008普通话感谢您的评价.wav,park: inline\"/>\n" +
                "<entry action=\"menu-exec-app\" digits=\"5\" param=\"execute_extension set:satisfaction_class=14,set:satisfaction_goodby=/home/<USER>/data/ivr/231008普通话感谢您的评价.wav,park: inline\"/>\n" +
                "</menu>");
        ivrStr.append("<menu name=\"satisfaction_ivr_粤语\"\n" +
                " greet-long=\"/home/<USER>/data/ivr/231008粤语请为我们的服务做出评价.wav\"\n" +
                " greet-short=\"/home/<USER>/data/ivr/231008粤语请为我们的服务做出评价.wav\"\n" +
                " invalid-sound=\"/home/<USER>/data/ivr/231008粤语您的输入有误.wav\"\n" +
                " exit-sound=\"/home/<USER>/data/ivr/231008粤语感谢您的评价.wav\"\n" +
                " confirm-macro=\"\"\n" +
                " confirm-key=\"\"\n" +
                " tts-engine=\"\"\n" +
                " tts-voice=\"\"\n" +
                " confirm-attempts=\"3\"\n" +
                " timeout=\"10000\"\n" +
                " inter-digit-timeout=\"2000\"\n" +
                " max-failures=\"3\"\n" +
                " max-timeouts=\"3\"\n" +
                " digit-len=\"1\">\n" +
                "<entry action=\"menu-exec-app\" digits=\"1\" param=\"execute_extension set:satisfaction_class=10,set:satisfaction_goodby=/home/<USER>/data/ivr/231008粤语感谢您的评价.wav,park: inline\"/>\n" +
                "<entry action=\"menu-exec-app\" digits=\"2\" param=\"execute_extension set:satisfaction_class=11,set:satisfaction_goodby=/home/<USER>/data/ivr/231008粤语感谢您的评价.wav,park: inline\"/>\n" +
                "<entry action=\"menu-exec-app\" digits=\"3\" param=\"execute_extension set:satisfaction_class=12,set:satisfaction_goodby=/home/<USER>/data/ivr/231008粤语感谢您的评价.wav,park: inline\"/>\n" +
                "<entry action=\"menu-exec-app\" digits=\"4\" param=\"execute_extension set:satisfaction_class=13,set:satisfaction_goodby=/home/<USER>/data/ivr/231008粤语感谢您的评价.wav,park: inline\"/>\n" +
                "<entry action=\"menu-exec-app\" digits=\"5\" param=\"execute_extension set:satisfaction_class=14,set:satisfaction_goodby=/home/<USER>/data/ivr/231008粤语感谢您的评价.wav,park: inline\"/>\n" +
                "</menu>");
        ivrStr.append("<menu name=\"satisfaction_ivr_潮汕话\"\n" +
                " greet-long=\"/home/<USER>/data/ivr/231008潮汕话请为我们的服务做出评价.wav\"\n" +
                " greet-short=\"/home/<USER>/data/ivr/231008潮汕话请为我们的服务做出评价.wav\"\n" +
                " invalid-sound=\"/home/<USER>/data/ivr/231008潮汕话您的输入有误.wav\"\n" +
                " exit-sound=\"/home/<USER>/data/ivr/231008潮汕话感谢您的评价.wav\"\n" +
                " confirm-macro=\"\"\n" +
                " confirm-key=\"\"\n" +
                " tts-engine=\"\"\n" +
                " tts-voice=\"\"\n" +
                " confirm-attempts=\"3\"\n" +
                " timeout=\"10000\"\n" +
                " inter-digit-timeout=\"2000\"\n" +
                " max-failures=\"3\"\n" +
                " max-timeouts=\"3\"\n" +
                " digit-len=\"1\">\n" +
                "<entry action=\"menu-exec-app\" digits=\"1\" param=\"execute_extension set:satisfaction_class=10,set:satisfaction_goodby=/home/<USER>/data/ivr/231008潮汕话感谢您的评价.wav,park: inline\"/>\n" +
                "<entry action=\"menu-exec-app\" digits=\"2\" param=\"execute_extension set:satisfaction_class=11,set:satisfaction_goodby=/home/<USER>/data/ivr/231008潮汕话感谢您的评价.wav,park: inline\"/>\n" +
                "<entry action=\"menu-exec-app\" digits=\"3\" param=\"execute_extension set:satisfaction_class=12,set:satisfaction_goodby=/home/<USER>/data/ivr/231008潮汕话感谢您的评价.wav,park: inline\"/>\n" +
                "<entry action=\"menu-exec-app\" digits=\"4\" param=\"execute_extension set:satisfaction_class=13,set:satisfaction_goodby=/home/<USER>/data/ivr/231008潮汕话感谢您的评价.wav,park: inline\"/>\n" +
                "<entry action=\"menu-exec-app\" digits=\"5\" param=\"execute_extension set:satisfaction_class=14,set:satisfaction_goodby=/home/<USER>/data/ivr/231008潮汕话感谢您的评价.wav,park: inline\"/>\n" +
                "</menu>");
        ivrStr.append("<menu name=\"satisfaction_ivr_客家话\"\n" +
                " greet-long=\"/home/<USER>/data/ivr/231008客家话请为我们的服务做出评价.wav\"\n" +
                " greet-short=\"/home/<USER>/data/ivr/231008客家话请为我们的服务做出评价.wav\"\n" +
                " invalid-sound=\"/home/<USER>/data/ivr/231008客家话您的输入有误.wav\"\n" +
                " exit-sound=\"/home/<USER>/data/ivr/231008客家话感谢您的评价.wav\"\n" +
                " confirm-macro=\"\"\n" +
                " confirm-key=\"\"\n" +
                " tts-engine=\"\"\n" +
                " tts-voice=\"\"\n" +
                " confirm-attempts=\"3\"\n" +
                " timeout=\"10000\"\n" +
                " inter-digit-timeout=\"2000\"\n" +
                " max-failures=\"3\"\n" +
                " max-timeouts=\"3\"\n" +
                " digit-len=\"1\">\n" +
                "<entry action=\"menu-exec-app\" digits=\"1\" param=\"execute_extension set:satisfaction_class=10,set:satisfaction_goodby=/home/<USER>/data/ivr/231008客家话感谢您的评价.wav,park: inline\"/>\n" +
                "<entry action=\"menu-exec-app\" digits=\"2\" param=\"execute_extension set:satisfaction_class=11,set:satisfaction_goodby=/home/<USER>/data/ivr/231008客家话感谢您的评价.wav,park: inline\"/>\n" +
                "<entry action=\"menu-exec-app\" digits=\"3\" param=\"execute_extension set:satisfaction_class=12,set:satisfaction_goodby=/home/<USER>/data/ivr/231008客家话感谢您的评价.wav,park: inline\"/>\n" +
                "<entry action=\"menu-exec-app\" digits=\"4\" param=\"execute_extension set:satisfaction_class=13,set:satisfaction_goodby=/home/<USER>/data/ivr/231008客家话感谢您的评价.wav,park: inline\"/>\n" +
                "<entry action=\"menu-exec-app\" digits=\"5\" param=\"execute_extension set:satisfaction_class=14,set:satisfaction_goodby=/home/<USER>/data/ivr/231008客家话感谢您的评价.wav,park: inline\"/>\n" +
                "</menu>");
        return ivrStr;
    }

    /**
     * 繁忙ivr
     * @param ivrStr
     * @return
     */
    public StringBuffer appendBusyIvr(StringBuffer ivrStr, IVRSettingVo sys){
        String busymenuIvrAudio = sysConfigService.selectConfigByKey("busymenu_ivr_audio");
        String busymenuIvrAudioShort = sysConfigService.selectConfigByKey("busymenu_ivr_audio_short");
        String busyIvrBusyAudio = sysConfigService.selectConfigByKey("busy_ivr_busy_audio");
        return ivrStr.append("<menu name=\"busymenu_ivr\"\n" +
                " greet-long=\"" + busymenuIvrAudio + "\"\n" +
                " greet-short=\"" + busymenuIvrAudioShort + "\"\n" +
                " invalid-sound=\"" + sys.getInvalidSound() + "\"\n" +
                " exit-sound=\"" + sys.getExitSound() + "\"\n" +
                " confirm-macro=\"\"\n" +
                " confirm-key=\"\"\n" +
                " tts-engine=\"\"\n" +
                " tts-voice=\"\"\n" +
                " confirm-attempts=\"3\"\n" +
                " timeout=\"1000\"\n" +
                " inter-digit-timeout=\"2000\"\n" +
                " exec-on-max-timeouts=\"execute_extension playback:'" + busyIvrBusyAudio + "',sleep:1000,hangup inline\"\n" +
                " exec-on-max-failures=\"execute_extension playback:'" + busyIvrBusyAudio + "',sleep:1000,hangup inline\"\n" +
                " max-failures=\"3\"\n" +
                " max-timeouts=\"2\"\n" +
                " digit-len=\"4\">\n" +
                "<entry action=\"menu-exec-app\" digits=\"1\" param=\"execute_extension set:Outbound_Socket=true," +
                "set:busymenu_from_ivr=true,transfer:'100086 XML " + sys.getDomain() + "' inline\"/>\n" +
                "</menu>");
    }

    /**
     * 留言ivr
     * @param ivrStr
     * @return
     */
    public StringBuffer appendLeaveIvr(StringBuffer ivrStr, IVRSettingVo sys){
        String leaveIvrAudio = sysConfigService.selectConfigByKey("leave_ivr_audio");
        return ivrStr.append("<menu name=\"rc01\"\n" +
                " greet-long=\"" + leaveIvrAudio + "\"\n" +
                " greet-short=\"" + leaveIvrAudio + "\"\n" +
                " invalid-sound=\"" + sys.getInvalidSound() + "\"\n" +
                " exit-sound=\"" + sys.getExitSound() + "\"\n" +
                " confirm-macro=\"\"\n" +
                " confirm-key=\"\"\n" +
                " tts-engine=\"\"\n" +
                " tts-voice=\"\"\n" +
                " confirm-attempts=\"1\"\n" +
                " timeout=\"10000\"\n" +
                " inter-digit-timeout=\"1000\"\n" +
                " exec-on-max-timeouts=\"execute_extension set:Outbound_Socket=true,set:sip_h_X-operation-type=RECORD,set:Record_Status=START,transfer:'100086 XML gd12348.com' inline\"\n" +
                " exec-on-max-failures=\"execute_extension set:Outbound_Socket=true,set:sip_h_X-operation-type=RECORD,set:Record_Status=START,transfer:'100086 XML gd12348.com' inline\"\n" +
                " max-failures=\"1\"\n" +
                " max-timeouts=\"1\"\n" +
                " digit-len=\"0\">\n" +
                "</menu>");
    }

    /**
     * 递归生成ivr文字信息
     * @param sb
     * @param ivr
     * @return
     */
    public String getIvrTextStr(StringBuffer sb, NavigationNodeIvrVo ivr, IVRSettingVo sysIvr) {
        if (ivr != null) {
            if (NodeKeyEnum.TO_PREVIEW.getCode().equals(ivr.getActionType()) || NodeKeyEnum.TO_FIRST.getCode().equals(ivr.getActionType())
                    || NodeKeyEnum.TO_GROUP.getCode().equals(ivr.getActionType())) {
                return "";
            }
            if (StringUtils.isBlank(ivr.getActionContent())) {
                return "";
            }
            String invalidSound = sysIvr.getInvalidSound();
            if (StringUtils.isNotBlank(ivr.getNodeLanguage())) {
                invalidSound = sysConfigService.selectConfigByKey(LANGUAGE.get(ivr.getNodeLanguage()) + "_invalid-sound");
            }
            if (ivr.getNodeLevel() != null && -1 == ivr.getNodeLevel()) {
                String special_ivr_audio_switch = sysConfigService.selectConfigByKey("special_ivr_audio_switch");
                String audioUrl = minioLocalPath + MinioConstants.MINIO_IVR_BUCKET + "/" + ivr.getActionContent();
                if ("1".equals(special_ivr_audio_switch)) {
                    audioUrl = sysConfigService.selectConfigByKey("special_ivr_audio");
                }
                sb.append( "<menu name=\"" + ivr.getIvrName() + "\"\n" +
                        " greet-long=\"" + audioUrl + "\"\n" +
                        " greet-short=\"" + audioUrl + "\"\n" +
                        " invalid-sound=\"" + invalidSound + "\"\n" +
                        " exit-sound=\"" + sysIvr.getExitSound() + "\"\n" +
                        " confirm-macro=\"\"\n" +
                        " confirm-key=\"\"\n" +
                        " tts-engine=\"\"\n" +
                        " tts-voice=\"\"\n" +
                        " confirm-attempts=\"3\"\n" +
                        " timeout=\"" + sysIvr.getTimeout() + "\"\n" +
                        " inter-digit-timeout=\"" + sysIvr.getInterDigitTimeout() + "\"\n" +
                        " max-failures=\"" + sysIvr.getMaxFailures() + "\"\n" +
                        " max-timeouts=\"" + sysIvr.getMaxTimeouts() + "\"\n" +
                        " digit-len=\"4\">\n" );
            } else {
                sb.append( "<menu name=\"" + ivr.getIvrName() + "\"\n" +
                        " greet-long=\"" + minioLocalPath + MinioConstants.MINIO_IVR_BUCKET + "/" + ivr.getActionContent() + "\"\n" +
                        " greet-short=\"" + minioLocalPath + MinioConstants.MINIO_IVR_BUCKET + "/" + ivr.getActionContent() + "\"\n" +
                        " invalid-sound=\"" + invalidSound + "\"\n" +
                        " exit-sound=\"" + sysIvr.getExitSound() + "\"\n" +
                        " confirm-macro=\"\"\n" +
                        " confirm-key=\"\"\n" +
                        " tts-engine=\"\"\n" +
                        " tts-voice=\"\"\n" +
                        " confirm-attempts=\"3\"\n" +
                        " timeout=\"" + sysIvr.getTimeout() + "\"\n" +
                        " inter-digit-timeout=\"" + sysIvr.getInterDigitTimeout() + "\"\n" +
                        " max-failures=\"" + sysIvr.getMaxFailures() + "\"\n" +
                        " max-timeouts=\"" + sysIvr.getMaxTimeouts() + "\"\n" +
                        " digit-len=\"4\">\n" );
            }
            List<NavigationNodeIvrVo> nodeIvrVoList = ivr.getNodeIvrVoList();
            if (CollectionUtils.isNotEmpty(nodeIvrVoList)) {
                for (NavigationNodeIvrVo ivrVo : nodeIvrVoList) {
                    //下一级导航
                    if (ivrVo.getActionType().equals(NodeKeyEnum.TO_NEXT.getCode())) {
                        sb.append( "<entry action=\"menu-sub\" digits=\"" + ivrVo.getNodeKey() + "\" " +
                                "param=\"" + ivrVo.getId() + "\"/>\n" );

                    } else if (ivrVo.getActionType().equals(NodeKeyEnum.TO_GROUP.getCode())) {
                        //跳转技能组
                        if (!StringUtils.isNotBlank(ivrVo.getActionContent())) {
                            continue;
                        }
                        SysSkillGroup group = skillGroupMapper.selectSysSkillGroupById(Long.valueOf(ivrVo.getActionContent()));
                        sb.append( "<entry action=\"menu-exec-app\" digits=\"" + ivrVo.getNodeKey() + "\" " +
                                "param=\"execute_extension set:Outbound_Socket=true,set:Bridge_Profile=SpecifyGroup,set:Bridge_Target=" +
                                "" + group.getId() + "," +
                                "set:Bridege_Target_Name=" + group.getGroupName() + "," +
                                "set:distribute_unique_code=" + ivrVo.getNodeDomain() + ",");
                        if (StringUtils.isNotBlank(ivrVo.getNodeLanguage())) {
                            sb.append("set:Select_Language=" + LANGUAGE.get(ivrVo.getNodeLanguage()) + ",");
                        }
                        if (ivrVo.getLeaveFlag() == 1) {
                            sb.append("set:Bridge_busy_Action='ivr:rc01',");
                        } else {
                            if (StringUtils.isNotBlank(ivrVo.getNoserviceRespondStr())) {
                                sb.append("set:Bridge_busy_wav=" + minioLocalPath + MinioConstants.MINIO_IVR_BUCKET + "/" + ivrVo.getNoserviceRespondStr() + ",set:Bridge_busy_Action=hangup,");
                            }
                        }
                        sb.append("transfer:'100086 XML " + sysIvr.getDomain() + "' inline\"/>\n");
                    } else if (ivrVo.getActionType().equals(NodeKeyEnum.TO_VOICE.getCode())) {
                        //播放自定义语音
                        sb.append( "<entry action=\"menu-play-sound\" digits=\"" + ivrVo.getNodeKey() + "\" " +
                                "param=\"" + minioLocalPath + MinioConstants.MINIO_IVR_BUCKET + "/" + ivrVo.getActionContent() + "\"/>\n" );
                    } else if(ivrVo.getActionType().equals(NodeKeyEnum.TO_REPEAT.getCode())){
                        //重听一遍
                        if (ivrVo.getNodeLevel() == 1) {
                            ivrVo.setLastNode(ivr.getIvrName());
                        }
                        sb.append( "<entry action=\"menu-sub\" digits=\"" + ivrVo.getNodeKey() + "\" param=\"" + ivrVo.getLastNode() + "\"/>\n" );
                        //此时不需要子级
                    } else if(ivrVo.getActionType().equals(NodeKeyEnum.TO_OTHER_PHONE.getCode())){
                        // 第三方电话
                        String ivrGateWay = sysConfigService.selectConfigByKey(ConfigCommonConstants.IVR_GATE_WAY_KEY);
//                        sb.append(
//                                "<entry action=\"menu-exec-app\" digits=\""
//                                        + ivrVo.getNodeKey()
//                                        + "\" param=\"execute_extension bridge:'sofia/gateway/"+ ivrGateWay + "/"
//                                        + ivrVo.getActionContent()
//                                        + "' inline\"/>\n"
//                        );
                        sb.append("<entry action=\"menu-exec-app\" digits=\"" + ivrVo.getNodeKey() + "\"" +
                        " param=\"execute_extension set:ivrTransferPhone=" + ivrVo.getActionContent() + ",transfer:'ivrTransferCall XML " + ConfigConstants.domain + "' inline\"/>");

                    } else if(ivrVo.getActionType().equals(NodeKeyEnum.TO_PREVIEW.getCode())){
                        // 返回上一级
                        sb.append( "<entry action=\"menu-sub\" digits=\"" + ivrVo.getNodeKey() + "\" " +
                                "param=\"" + ((ivrVo.getNodeLevel() <= 2) ? ivr.getIvrRootName() : ivrVo.getLastNode()) + "\"/>\n" );
                    } else if(ivrVo.getActionType().equals(NodeKeyEnum.TO_FIRST.getCode())){
                        // 返回首层
                        sb.append( "<entry action=\"menu-sub\" digits=\"" + ivrVo.getNodeKey() + "\" param=\"" + ivrVo.getDomain() + "\"/>\n" );
                    }
                }
                sb.append( "</menu>\n" );
                for (NavigationNodeIvrVo nodeIvrVo : nodeIvrVoList) {
                    this.getIvrTextStr(sb, nodeIvrVo, sysIvr);
                }
            } else {
                sb.append( "</menu>\n" );
            }
        }

        return sb.toString();
    }

    /**
     * 递归查找ivr数结构
     * @param nodeIvrVo
     * @return
     */
    public void getNavigationByParentId(NavigationNodeIvrVo nodeIvrVo) {
        if (nodeIvrVo != null) {
            List<NavigationNodeIvrVo> nodeIvr = nodeMapper.getNodeIvr(nodeIvrVo.getId());
            if (CollectionUtils.isNotEmpty(nodeIvr)) {
                nodeIvrVo.setNodeIvrVoList(nodeIvr);
                for (NavigationNodeIvrVo navigationNodeIvrVo : nodeIvr) {
                    NavigationServiceTime serviceTime = serviceTimeMapper.selectOne(Wrappers.<NavigationServiceTime>lambdaQuery()
                            .eq(NavigationServiceTime::getNodeId, navigationNodeIvrVo.getId())
                            .last(" limit 1")
                    );
                    if (serviceTime != null) {
                        navigationNodeIvrVo.setNoserviceRespondStr(serviceTime.getNoserviceRespondStr());
                    }
                    navigationNodeIvrVo.setIvrRootName(nodeIvrVo.getIvrRootName());
                    this.getNavigationByParentId(navigationNodeIvrVo);
                }
            }
        }
    }
}
