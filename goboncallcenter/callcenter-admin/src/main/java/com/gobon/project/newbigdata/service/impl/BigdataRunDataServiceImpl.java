package com.gobon.project.newbigdata.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gobon.common.constant.SqlConstant;
import com.gobon.common.exception.BaseException;
import com.gobon.project.newbigdata.domain.*;
import com.gobon.project.newbigdata.mapper.BigdataRunDataMapper;
import com.gobon.project.newbigdata.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 大数据运行数据结果 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Service
@Slf4j
public class BigdataRunDataServiceImpl extends ServiceImpl<BigdataRunDataMapper, BigdataRunData> implements IBigdataRunDataService {
    @Resource
    private IBigdataTopicModelRuleService modelRuleService;
    @Resource
    private IBigdataTopicModelService bigdataTopicModelService;
    @Resource
    private IBigdataTableColumnService bigdataTableColumnService;
    @Resource
    private IBigdataRunDataTypeService bigdataRunDataTypeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void runData(Long modelId) {
        List<BigdataTopicModelRule> list = modelRuleService.lambdaQuery().eq(BigdataTopicModelRule::getModelId, modelId).list();
        remove(Wrappers.<BigdataRunData>lambdaQuery().eq(BigdataRunData::getTopicModelId, modelId));
        if (CollectionUtils.isEmpty(list)) {
            bigdataTopicModelService.lambdaUpdate().eq(BigdataTopicModel::getId, modelId)
                    .set(BigdataTopicModel::getRunStatus, "1").update();
            return;
        }
        bigdataTopicModelService.lambdaUpdate().eq(BigdataTopicModel::getId, modelId)
                .set(BigdataTopicModel::getRunStatus, "2").update();
        for (BigdataTopicModelRule bigdataTopicModelRule : list) {
            BigdataTableColumn tableColumn = bigdataTableColumnService.lambdaQuery().eq(BigdataTableColumn::getId, bigdataTopicModelRule.getColumnId()).last(SqlConstant.SQL_GETONE).one();
            // 同比替换时间
            List<String> chooseDate = null;
            if ("2".equals(bigdataTopicModelRule.getRunCondition())) {
                // 获取期间所有日期
                chooseDate = getDateBetween(bigdataTopicModelRule.getCalcStartTime(), bigdataTopicModelRule.getCalcEndTime(), bigdataTopicModelRule.getCalcDate());
                getTbDate(bigdataTopicModelRule);
            }
            // 组装时间
            String querySql = replaceQuerySql(bigdataTopicModelRule, tableColumn);
            // 执行语句
            List<BigdataRunData> bigdataRunData = baseMapper.executeSql(querySql);
            List<BigdataRunData> saveList = new ArrayList<>();
            if ("1".equals(tableColumn.getDateFlag())) {
                // 获取期间所有日期
                List<String> dateBetween = getDateBetween(bigdataTopicModelRule.getCalcStartTime(), bigdataTopicModelRule.getCalcEndTime(), bigdataTopicModelRule.getCalcDate());
                if (!CollectionUtils.isEmpty(dateBetween)) {
                    // 获取同比时间与选择时间对应关系
                    Map<String, String> oldChoose = new HashMap<>();
                    if (chooseDate != null) {
                        for (int i = dateBetween.size() - 1; i >= 0; i--) {
                            oldChoose.put(dateBetween.get(i), chooseDate.get(i));
                        }
                    }
                    Map<String, BigdataRunData> dataMap = bigdataRunData.stream().collect(Collectors.toMap(BigdataRunData::getDataName, self -> self));
                    for (String dateStr : dateBetween) {
                        BigdataRunData bigdataRunDatum = dataMap.get(dateStr);
                        if (bigdataRunDatum == null) {
                            bigdataRunDatum = new BigdataRunData();
                            bigdataRunDatum.setDataName(dateStr);
                            bigdataRunDatum.setDataValue("0");
                        }
                        if (oldChoose.size() > 0) {
                            bigdataRunDatum.setDataName(oldChoose.get(bigdataRunDatum.getDataName()));
                        }
                        bigdataRunDatum.setChartType(bigdataTopicModelRule.getChartType());
                        bigdataRunDatum.setRuleId(bigdataTopicModelRule.getId());
                        bigdataRunDatum.setRuleName(bigdataTopicModelRule.getTableName() + "-" + bigdataTopicModelRule.getColumnName());
                        bigdataRunDatum.setTopicModelId(bigdataTopicModelRule.getModelId());
                        bigdataRunDatum.setDataExt(tableColumn.getDataExt());
                        bigdataRunDatum.setDateFlag(tableColumn.getDateFlag());
                        saveList.add(bigdataRunDatum);
                    }
                    saveBatch(saveList);
                }
            } else {
                List<BigdataRunDataType> runDataTypeList = bigdataRunDataTypeService.lambdaQuery().eq(BigdataRunDataType::getColumnId, tableColumn.getId()).list();
                if (CollectionUtils.isEmpty(runDataTypeList)) {
                    for (BigdataRunData bigdataRunDatum : bigdataRunData) {
                        if (StringUtils.isBlank(bigdataRunDatum.getDataName())) {
                            continue;
                        }
                        bigdataRunDatum.setRuleId(bigdataTopicModelRule.getId());
                        bigdataRunDatum.setRuleName(bigdataTopicModelRule.getTableName() + "-" + bigdataTopicModelRule.getColumnName());
                        bigdataRunDatum.setTopicModelId(bigdataTopicModelRule.getModelId());
                        bigdataRunDatum.setDataExt(tableColumn.getDataExt());
                        bigdataRunDatum.setChartType(bigdataTopicModelRule.getChartType());
                        bigdataRunDatum.setDateFlag(tableColumn.getDateFlag());
                        saveList.add(bigdataRunDatum);
                    }
                } else {
                    Map<String, BigdataRunData> dataMap = bigdataRunData.stream().collect(Collectors.toMap(BigdataRunData::getDataName, self -> self));
                    for (BigdataRunDataType bigdataRunDataType : runDataTypeList) {
                        BigdataRunData bigdataRunDatum = dataMap.get(bigdataRunDataType.getDataName());
                        if (bigdataRunDatum == null) {
                            bigdataRunDatum = new BigdataRunData();
                            bigdataRunDatum.setDataName(bigdataRunDataType.getDataName());
                            bigdataRunDatum.setDataValue("0");
                        }
                        bigdataRunDatum.setRuleId(bigdataTopicModelRule.getId());
                        bigdataRunDatum.setRuleName(bigdataTopicModelRule.getTableName() + "-" + bigdataTopicModelRule.getColumnName());
                        bigdataRunDatum.setTopicModelId(bigdataTopicModelRule.getModelId());
                        bigdataRunDatum.setDataExt(tableColumn.getDataExt());
                        bigdataRunDatum.setChartType(bigdataTopicModelRule.getChartType());
                        bigdataRunDatum.setDateFlag(tableColumn.getDateFlag());
                        saveList.add(bigdataRunDatum);
                    }
                }
                saveBatch(saveList);
            }
        }
        bigdataTopicModelService.lambdaUpdate().eq(BigdataTopicModel::getId, modelId)
                .set(BigdataTopicModel::getRunStatus, "1").update();
    }


    public String replaceQuerySql(BigdataTopicModelRule bigdataTopicModelRule, BigdataTableColumn tableColumn) {
        // 统计周期（1-按天；2-按月；3-按年）
        String calcDate = bigdataTopicModelRule.getCalcDate();
        String executeSql = tableColumn.getExecuteSql();
        if ("1".equals(calcDate)) {
            executeSql = executeSql.replace("${queryStartTime}", bigdataTopicModelRule.getCalcStartTime() + " 00:00:00")
                    .replace("${queryEndTime}", bigdataTopicModelRule.getCalcEndTime() + " 23:59:59");
        } else if ("2".equals(calcDate)) {
            executeSql = executeSql.replace("${queryStartTime}", bigdataTopicModelRule.getCalcStartTime() + "-01 00:00:00")
                    .replace("${queryEndTime}", bigdataTopicModelRule.getCalcEndTime() + "-31 23:59:59");
        } else if ("3".equals(calcDate)) {
            executeSql = executeSql.replace("${queryStartTime}", bigdataTopicModelRule.getCalcStartTime() + "-01-01 00:00:00")
                    .replace("${queryEndTime}", bigdataTopicModelRule.getCalcEndTime() + "-12-31 23:59:59");
        } else {
            log.error("执行异常：无法识别统计周期。{}", bigdataTopicModelRule);
            throw new BaseException("执行异常：无法识别统计周期。" + bigdataTopicModelRule);
        }
        // 替换时间格式化
        executeSql = executeSql.replace("${dateFormat}",
                "1".equals(bigdataTopicModelRule.getCalcDate()) ? "%Y-%m-%d" :
                        ("2".equals(bigdataTopicModelRule.getCalcDate()) ? "%Y-%m" :
                                ("3".equals(bigdataTopicModelRule.getCalcDate()) ? "%Y" : "")));
        return executeSql;
    }


    public void getTbDate(BigdataTopicModelRule bigdataTopicModelRule) {
        List<String> datesBetween = getDateBetween(bigdataTopicModelRule.getCalcStartTime(), bigdataTopicModelRule.getCalcEndTime(), bigdataTopicModelRule.getCalcDate());
        if (CollectionUtils.isEmpty(datesBetween)) {
            return;
        }
        if ("1".equals(bigdataTopicModelRule.getCalcDate())) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate start = LocalDate.parse(bigdataTopicModelRule.getCalcStartTime(), formatter);
            bigdataTopicModelRule.setCalcStartTime(formatter.format(start.minusDays(datesBetween.size())));
            LocalDate end = LocalDate.parse(bigdataTopicModelRule.getCalcEndTime(), formatter);
            bigdataTopicModelRule.setCalcEndTime(formatter.format(end.minusDays(datesBetween.size())));
        } else if ("2".equals(bigdataTopicModelRule.getCalcDate())) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            YearMonth start = YearMonth.parse(bigdataTopicModelRule.getCalcStartTime(), formatter);
            bigdataTopicModelRule.setCalcStartTime(formatter.format(start.minusMonths(datesBetween.size())));
            YearMonth end = YearMonth.parse(bigdataTopicModelRule.getCalcEndTime(), formatter);
            bigdataTopicModelRule.setCalcEndTime(formatter.format(end.minusMonths(datesBetween.size())));
        } else if ("3".equals(bigdataTopicModelRule.getCalcDate())) {
            Integer startYear = Integer.valueOf(bigdataTopicModelRule.getCalcStartTime());
            Integer endYear = Integer.valueOf(bigdataTopicModelRule.getCalcEndTime());
            bigdataTopicModelRule.setCalcStartTime(String.valueOf(startYear - datesBetween.size()));
            bigdataTopicModelRule.setCalcEndTime(String.valueOf(endYear - datesBetween.size()));
        }
    }



    public List<String> getDateBetween(String startDateStr, String endDateStr, String calcDateType) {
        return "1".equals(calcDateType) ? getDatesBetween(startDateStr, endDateStr) : (
                "2".equals(calcDateType) ? getMonthsBetween(startDateStr, endDateStr) : (
                        "3".equals(calcDateType) ? getYearBetween(startDateStr, endDateStr) : null
                        )
                );
    }

    public List<String> getDatesBetween(String startDateStr, String endDateStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(startDateStr, formatter);
        LocalDate endDate = LocalDate.parse(endDateStr, formatter);;
        List<String> dates = new ArrayList<>();
        LocalDate currentDate = startDate;

        while (currentDate.isBefore(endDate) || currentDate.isEqual(endDate)) {
            dates.add(currentDate.format(formatter));
            currentDate = currentDate.plus(1, ChronoUnit.DAYS);
        }

        return dates;
    }

    public List<String> getMonthsBetween(String startDateStr, String endDateStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

        // 定义起始和结束的 YearMonth
        YearMonth startMonth = YearMonth.parse(startDateStr, formatter);
        YearMonth endMonth = YearMonth.parse(endDateStr, formatter);
        List<String> dates = new ArrayList<>();

        // 输出两个 YearMonth 之间的所有月份
        while (!startMonth.isAfter(endMonth)) {
            String formattedMonth = startMonth.format(formatter);
            startMonth = startMonth.plusMonths(1);
            dates.add(formattedMonth);
        }
        return dates;
    }

    public List<String> getYearBetween(String startDateStr, String endDateStr) {
        List<String> result = new ArrayList<>();
        Set<Integer> resultSet = new HashSet<>();
        Integer startYear = Integer.valueOf(startDateStr);
        resultSet.add(startYear);
        Integer endYear = Integer.valueOf(endDateStr);
        for (Integer temYear = startYear;temYear <= endYear; temYear++) {
            resultSet.add(temYear);
        }
        return resultSet.stream().map(Objects::toString).collect(Collectors.toList());
    }

}
