package com.gobon.project.intelligencequality.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 呼叫详情
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CallInfoVo implements Serializable {
    private static final long serialVersionUID = -7863671605664274738L;
    /**
     * 通话详情ID
     */
    private Long id;
    /**
     * 主记录ID
     */
    private Long recordId;
    /**
     * 呼叫类型
     */
    private Integer callType;
    /**
     * 客服姓名
     */
    private String userName;
    /**
     * 呼叫时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date callTime;
    /**
     * 通话开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
     * 通话结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
     * 通话时长
     */
    private int duration;
    /**
     * 满意度值
     */
    private Long satisficeValue;
    /**
     * 满意度名称
     */
    private String satisficeName;
    /**
     * 满意度code
     */
    private String satisficeCode;
    /**
     * 是否多方
     */
    private Integer multipleFlag;
    /**
     * 是否求助
     */
    private Integer helpFlag;
    /**
     * 主叫电话号码
     */
    private String callPhone;
    /**
     * 被叫电话号码
     */
    private String userPhone;
    /**
     * 电话号码归属省份
     */
    private String province;
    /**
     * 电话号码归属城市
     */
    private String city;
    /**
     * 录音文件
     */
    private String sound;
    /**
     * 语音文子内容
     */
    private String soundText;

    /**
     * 通话流水号
     */
    private String billNumber;
}
