package com.gobon.project.intelligencequality.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.gobon.project.intelligencequality.utils.QualityResult;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 评分规则
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class QualityScoringRuleVo implements Serializable {
    private static final long serialVersionUID = 6697157819659788384L;

    private static final String KEY_KEYWORD_RULE = "1";
    private static final String KEY_BEHAVIOR_RULE = "2";
    private static final Pattern PATTERN_INTEGER = Pattern.compile("^[1-9]\\d*$");
    /**
     * 默认评分申诉有效天数
     */
    public static final Integer DEFAULT_APPEAL_TIME_OF_DAY = 30;

    /**
     * ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 评分规则名称
     */
    private String ruleName;
    /**
     * 评分对象(1: 技能组, 2: 客服)
     */
    private String scoringObject;
    /**
     * 技能组ID
     */
    private String scoringSkillIds;
    /**
     * 申诉期限
     */
    private String appealPeriodCode;
    /**
     * 申诉有效期
     */
    private Integer appealPeriodTime;
    /**
     * 客服人员ID
     */
    private String scoringPeople;
    /**
     * 描述
     */
    private String description;
    /**
     * 规则总分
     */
    private Integer grossScore;
    /**
     * 评分等级
     */
    private String scoringGradeCode;
    /**
     * 评分分数
     */
    private String scoringGradeScore;
    /**
     * 分数
     */
    private Integer[] scoringGradeScoreItems;
    /**
     * 评分分数描述
     */
    private String scoringGradeScoreDescribe;
    /**
     * 描述
     */
    private String[] scoringGradeScoreDescItems;
    /**
     * 规则类型列表
     */
    @Setter(AccessLevel.NONE)
    private List<QualityScoringRuleTypeVo> scoringRuleTypes;
    /**
     * 规则明细列表
     */
    private List<QualityScoringRuleItemVo> scoringRuleItems;

    /**
     * Map<规则类型, Map<规则ID, QualityScoringRuleItemVo>>
     */
    @Setter(AccessLevel.NONE)
    private Map<String, Map<String, List<QualityScoringRuleItemVo>>> scoringRuleMap;

    /**
     * 根据明细项转换成类型节点数据
     * @return {@code this}
     */
    public QualityScoringRuleVo transform() {
        if (this.scoringRuleItems != null && !this.scoringRuleItems.isEmpty()) {
            Map<Integer, Map<String, List<QualityScoringRuleItemVo>>> map = this.scoringRuleItems.stream()
                    .collect(Collectors.groupingBy(QualityScoringRuleItemVo::getRuleType,
                            ConcurrentHashMap::new,
                            Collectors.groupingBy(QualityScoringRuleItemVo::getTypeName)));
            List<QualityScoringRuleTypeVo> ruleTypes = new ArrayList<>(map.size());
            map.forEach((k, m) ->
                    m.forEach((n, v) -> {
                        QualityScoringRuleTypeVo ruleType = new QualityScoringRuleTypeVo();
                        ruleType.setId(k);
                        ruleType.setName(n);
                        ruleType.setScoringRuleItems(v);
                        ruleTypes.add(ruleType);
                    }));
            this.scoringRuleTypes = ruleTypes;
        }
        return this;
    }

    public QualityScoringRuleVo transformMap() {
        if (this.scoringRuleItems != null && !this.scoringRuleItems.isEmpty()) {
            this.scoringRuleMap = this.scoringRuleItems.stream()
                    .collect(Collectors.groupingBy(it -> String.valueOf(it.getRuleType()),
                            ConcurrentHashMap::new,
                            Collectors.groupingBy(it -> String.valueOf(it.getTargetRuleId()))));
        }
        return this;
    }

    public Map<String, List<QualityScoringRuleItemVo>> scoringRuleMap(final String key) {
        return Optional.ofNullable(this.scoringRuleMap).map(it -> it.get(key)).orElse(null);
    }

    public Map<String, List<QualityScoringRuleItemVo>> keyWordScoringRuleMap() {
        return scoringRuleMap(KEY_KEYWORD_RULE);
    }

    public Map<String, List<QualityScoringRuleItemVo>> behaviorScoringRuleMap() {
        return scoringRuleMap(KEY_BEHAVIOR_RULE);
    }

    public QualityScoringRuleVo parse() {
        if (this.scoringGradeScore != null) {
            this.scoringGradeScoreItems = Arrays.stream(this.scoringGradeScore.split(",")).map(Integer::valueOf)
                    .toArray(Integer[]::new);
        }
        if (this.scoringGradeScoreDescribe != null) {
            this.scoringGradeScoreDescItems = this.scoringGradeScoreDescribe.split(",");
        }
        return this;
    }
    
    public QualityScoringRuleVo parseAppealTime() {
        try {
            if (PATTERN_INTEGER.matcher(this.appealPeriodCode).matches()) {
                this.appealPeriodTime = Integer.valueOf(this.appealPeriodCode);
            } else {
                this.appealPeriodTime = DEFAULT_APPEAL_TIME_OF_DAY;
            }
        } catch (Exception ignore) {
            this.appealPeriodTime = DEFAULT_APPEAL_TIME_OF_DAY;
        }
        return this;
    }

    public QualityResult calculate(final Integer score) {
        try {
            final String text;
            switch (this.scoringGradeCode) {
                case "2":
                    text = this.scoringGradeScoreItems[0] > score ? this.scoringGradeScoreDescItems[0]
                            : this.scoringGradeScoreDescItems[1];
                    break;
                case "3":
                    if (score < this.scoringGradeScoreItems[0]) {
                        text = this.scoringGradeScoreDescItems[0];
                    } else if (this.scoringGradeScoreItems[1] <= score && score < this.scoringGradeScoreItems[2]) {
                        text = this.scoringGradeScoreDescItems[1];
                    } else {
                        text = this.scoringGradeScoreDescItems[2];
                    }
                    break;
                case "4":
                    if (score < this.scoringGradeScoreItems[0]) {
                        text = this.scoringGradeScoreDescItems[0];
                    } else if (this.scoringGradeScoreItems[1] <= score && score < this.scoringGradeScoreItems[2]) {
                        text = this.scoringGradeScoreDescItems[1];
                    } else if (this.scoringGradeScoreItems[3] <= score && score < this.scoringGradeScoreItems[4]) {
                        text = this.scoringGradeScoreDescItems[2];
                    } else {
                        text = this.scoringGradeScoreDescItems[3];
                    }
                    break;
                case "5":
                    if (score < this.scoringGradeScoreItems[0]) {
                        text = this.scoringGradeScoreDescItems[0];
                    } else if (this.scoringGradeScoreItems[1] <= score && score < this.scoringGradeScoreItems[2]) {
                        text = this.scoringGradeScoreDescItems[1];
                    } else if (this.scoringGradeScoreItems[3] <= score && score < this.scoringGradeScoreItems[4]) {
                        text = this.scoringGradeScoreDescItems[2];
                    } else if (this.scoringGradeScoreItems[5] <= score && score < this.scoringGradeScoreItems[6]) {
                        text = this.scoringGradeScoreDescItems[3];
                    } else {
                        text = this.scoringGradeScoreDescItems[4];
                    }
                    break;
                default:
                    return QualityResult.Qualified;
            }
            return QualityResult.parse(text);
        } catch (Exception ignore) {
            return QualityResult.Qualified;
        }
    }
}
