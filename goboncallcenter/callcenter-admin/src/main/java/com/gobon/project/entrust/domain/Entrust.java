package com.gobon.project.entrust.domain;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 委托服务表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_entrust")
@ApiModel(value="Entrust对象", description="委托服务表")
public class Entrust implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "通话记录id")
    private Long recordDetailId;

    @ApiModelProperty(value = "委托订单号")
    private String orderNo;

    @ApiModelProperty(value = "委托类型（1-律师服务；2-司法鉴定服务）")
    private String type;

    @ApiModelProperty(value = "委托业务类型")
    private String businessType;

    @ApiModelProperty(value = "客户姓名")
    private String customerName;

    @ApiModelProperty(value = "客户联系方式")
    private String customerPhone;

    @ApiModelProperty(value = "客户预算")
    private String customerBudget;

    @ApiModelProperty(value = "案由描述")
    private String caseDesc;

    @ApiModelProperty(value = "委托需求")
    private String entrustDemand;

    @ApiModelProperty(value = "按键标的额")
    private BigDecimal caseMoney;

    @ApiModelProperty(value = "信息公开范围（1-仅对相关人员；2-全范围）")
    private String infoPublicType;

    @ApiModelProperty(value = "是否有心仪律师")
    private String likeLawyerFlag;

    @ApiModelProperty(value = "心仪律师工号")
    private String likeLawyerNumber;

    @ApiModelProperty(value = "性别要求（0-无；1-男；2-女）")
    private String sexDemand;

    @ApiModelProperty(value = "服务办理地code（-连接）")
    private String entrustAreaCode;

    @ApiModelProperty(value = "服务办理地")
    private String entrustAreaName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "附件")
    private String fileContent;

    @ApiModelProperty(value = "状态（0-草稿；1-提交；2-审核通过；3-已确认；9-已撤回；10-已选择律师）")
    private String status;

    @ApiModelProperty(value = "评价")
    private String evaluate;

    @ApiModelProperty(value = "评价时间")
    private Date evaluateTime;

    @ApiModelProperty(value = "审核状态")
    private String approvalStatus;

    @ApiModelProperty(value = "审核内容")
    private String approvalContent;

    @ApiModelProperty(value = "审核人id")
    private Long approvalById;

    @ApiModelProperty(value = "审核人")
    private String approvalByName;

    @ApiModelProperty(value = "审核时间")
    private Date approvalTime;

    @ApiModelProperty(value = "客户确认时间")
    private Date confirmTime;

    @ApiModelProperty(value = "客户撤销时间")
    private Date cancelTime;

    @ApiModelProperty(value = "短信链接")
    private String smsUrl;

    @ApiModelProperty(value = "推送选择律师时间")
    private Date sendPushLawyerTime;

    @ApiModelProperty(value = "创建人id")
    private Long createById;

    @ApiModelProperty(value = "创建人")
    private String createByName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateById;

    @ApiModelProperty(value = "更新人")
    private String updateByName;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "删除标识")
    private String delFlag;


}
