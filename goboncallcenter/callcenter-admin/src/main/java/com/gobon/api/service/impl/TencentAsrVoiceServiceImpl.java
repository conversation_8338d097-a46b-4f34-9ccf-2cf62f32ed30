package com.gobon.api.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.gobon.api.service.TencentAsrVoiceService;
import com.gobon.api.service.job.TencentAsyncQueryResultJob;
import com.gobon.common.core.lang.UUID;
import com.gobon.framework.manager.AsyncManager;
import com.gobon.framework.web.domain.R;
import com.gobon.project.conversation.domain.CallcenterRecordDetail;
import com.gobon.project.conversation.domain.CallcenterRecordDetailSound;
import com.gobon.project.conversation.domain.CallcenterRecordDetailSoundAsrresultEntity;
import com.gobon.project.conversation.service.ICallcenterRecordDetailService;
import com.gobon.project.system.service.ISysConfigService;
import com.gobon.utils.FileUtil;
import com.tencentcloudapi.asr.v20190614.AsrClient;
import com.tencentcloudapi.asr.v20190614.models.*;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 腾讯语音转文字service
 *
 * <AUTHOR>
 * @date 2024/7/26 17:00
 * @see TencentAsrVoiceServiceImpl
 * @since
 */
@Service
@Slf4j
public class TencentAsrVoiceServiceImpl implements TencentAsrVoiceService {

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private ICallcenterRecordDetailService callcenterRecordDetailService;

    public final String PRE_WAV_FILE_REDIS_KEY = "OPEN_PRE_WAV_FILE:";

    /**
     * 等待结果：超时时间：2分钟
     */
    public final int waitTimeoutSecond = 120;

    /**
     * 等待结果：查询结果间隔：2秒
     */
    public final int waitIntervalSecond = 2;

    /**
     * 最大超时时间：30分钟
     */
    public final int maxTimeoutSecond = 30 * 60;

    @Override
    public R<String> getWavFile(String fileId) {
        log.info("getWavFile:{}", fileId);
        if (StrUtil.isEmpty(fileId)) {
            return R.fail("参数有误!");
        }
        String key = PRE_WAV_FILE_REDIS_KEY + fileId;
        Object val = redisTemplate.opsForValue().get(key);
        if (val != null && val.toString().length() > 0) {
            return R.ok(val.toString());
        }
        return R.fail("文件已失效!");
    }

    @Override
    public R transferTextByDetailId(String detailId) {
        String detailKey = PRE_WAV_FILE_REDIS_KEY + detailId;
        if (redisTemplate.opsForValue().get(detailKey) != null) {
            return R.fail("正在转文字处理，请稍后!");
        }

        CallcenterRecordDetail detail = callcenterRecordDetailService.selectDetailByID(Long.parseLong(detailId));
        if (detail == null || StrUtil.isEmpty(detail.getSound())) {
            return R.fail("数据不存在或无录音.");
        }

        //录音文件前缀
        String configKey = "speech_to_text_get_audio_url";
        String audioUrl = sysConfigService.selectConfigByKey(configKey);
        if (StrUtil.isEmpty(audioUrl)) {
            log.warn("录音文件路径前缀尚未配置!{}", configKey);
            return R.fail("系统配置有误,请联系管理员!");
        }

        configKey = "tencent_voice_to_text_config";
        String tencentAsrConfig = sysConfigService.selectConfigByKey(configKey);
        if (StrUtil.isEmpty(tencentAsrConfig)) {
            tencentAsrConfig = "{openFlag:true,secretId:'AKIDIWcicQ5qcEe9LSRJ3c1FByY2LXXPG2f5',secretKey:'LWbXM4bw52pV0L9vmQ3u02ynWEAA6uog',tempPath:'/home/<USER>/voice_temp'}";
        }

        JSONObject asrConfig = JSONObject.parseObject(tencentAsrConfig);

        if (!audioUrl.endsWith("/")) {
            audioUrl = audioUrl + "/";
        }

        String tempSavePath = asrConfig.getString("tempPath");
        if (!tempSavePath.endsWith("/")) {
            tempSavePath = tempSavePath + "/";
        }

        String tempFileName = String.format("tmp_file_%d.wav", System.currentTimeMillis());
        String wavFullPath = audioUrl + detail.getSound();
        String localFullPath = FileUtil.getRemoteFile(wavFullPath, tempSavePath, tempFileName);
        if (StrUtil.isEmpty(localFullPath)) {
            log.warn("通话({}),语音文件({})在服务不存在!", detailId, detail.getSound());
            return R.fail("语音文件在服务不存在.");
        }

        //缓存路径
        String fileUuid = UUID.fastUUID().toString();
        String outKey = PRE_WAV_FILE_REDIS_KEY + fileUuid;
        redisTemplate.opsForValue().set(outKey, localFullPath, 30l, TimeUnit.MINUTES);
        redisTemplate.opsForValue().set(detailKey, localFullPath, 30l, TimeUnit.MINUTES);

        //调用腾讯语音转文字
        R callRet = callTencentAsrTranfer(asrConfig, fileUuid);
        if (callRet.getCode() == R.FAIL) {
            redisTemplate.delete(outKey);
            redisTemplate.delete(detailKey);
            return callRet;
        }

        //清除缓存参数
        JSONObject asrCacheParam = new JSONObject();
        asrCacheParam.put("outKey", outKey);
        asrCacheParam.put("detailKey", detailKey);
        asrCacheParam.put("tempWavFile", localFullPath);


        //启动定时查询任务
        Long taskId = Long.parseLong(callRet.getData().toString());
        asrCacheParam.put("taskId", taskId);
        //智能质检调用方（华哥要求要同步返回） 2024-07-26
        long timeoutMillSecond = waitTimeoutSecond * 1000;
        while (true) {
            callRet = callTencentAsrTransResult(asrConfig, taskId);
            if (callRet.getCode() != R.SUCCESS) {
                deleteCache(asrCacheParam);
                return callRet;
            }

            TaskStatus taskStatus = (TaskStatus) callRet.getData();
            //status:3-失败，0-等待中，1-执行中，2-成功
            int statusCode = taskStatus.getStatus().intValue();
            //识别失败出错
            if (statusCode == 3) {
                deleteCache(asrCacheParam);
                log.warn("识别失败!detailId:{},{}", taskStatus.getErrorMsg());
                return R.fail("语音识别失败!" + taskStatus.getErrorMsg());
            }
            //识别成功
            else if (statusCode == 2) {
                deleteCache(asrCacheParam);
                return saveAsrTranserResult(taskStatus, detail);
            }
            //0-等待中，1-执行中 2秒后继续查询
            try {
                long interval = waitIntervalSecond + 1000;
                if (timeoutMillSecond <= 0) {
                    log.warn("正在转换处理中,等待时间到先返回!{}", detail.getId());
                    //开启异步执行(2秒查询一次)
                    AsyncManager.me().execute(new TencentAsyncQueryResultJob(this, asrConfig, asrCacheParam, detail), 2, TimeUnit.SECONDS);
                    return R.ok("正在转换处理中.");
                }
                Thread.sleep(interval);
                timeoutMillSecond -= interval;
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    @Override
    public void deleteCache(JSONObject delCacheParam) {
        redisTemplate.delete(delCacheParam.getString("outKey"));
        redisTemplate.delete(delCacheParam.getString("detailKey"));
        File file = new File(delCacheParam.getString("tempWavFile"));
        if (file.exists()) {
            file.delete();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public R saveAsrTranserResult(TaskStatus taskStatus, CallcenterRecordDetail detail) {
        if (taskStatus.getResultDetail() == null || taskStatus.getResultDetail().length == 0) {
            return R.ok("转换成功，无文字.");
        }
        //识别坐席与客服。
        //
        Map<Long, String> agentRole = new LinkedHashMap<>();
        //坐席关键字
        //法律咨询服务热线,什么法律问题咨询,我是xx律师/客服,工号为您服务,以下通话可能会被录音 为保证服务质量,12348法律咨询服务热线
        String[] welcomeKeyWords = {"为保证服务质量", "以下通话可能会被录音", "工号|为您服务"};
        String[] agentKeyWords = {"请问|什么法律问题咨询", "什么法律问题咨询", "法律问题咨询", "我是|律师", "我是|客服", "感谢来电", "麻烦评价", "12348法律咨询服务热线", "法律咨询服务热线", "咨询服务热线"};
        Map<Long, StringBuilder> speekTextMap = new LinkedHashMap<>();
        for (SentenceDetail item : taskStatus.getResultDetail()) {
            Long speakerId = item.getSpeakerId();
            if (speakerId == null) {
                continue;
            }
            //组合每个人说话内容
            String finalSentence = StrUtil.isNotEmpty(item.getFinalSentence()) ? item.getFinalSentence().trim() : "";
            if (!speekTextMap.containsKey(speakerId)) {
                speekTextMap.put(speakerId, new StringBuilder(finalSentence));
            } else {
                speekTextMap.get(speakerId).append(finalSentence);
            }
        }

        //通过关键字转换坐席
        if (!speekTextMap.isEmpty()) {
            List<SpeekerUserItem> speekerUser = new ArrayList(speekTextMap.size());
            for (Map.Entry<Long, StringBuilder> it : speekTextMap.entrySet()) {
                speekerUser.add(new SpeekerUserItem(it.getKey(), it.getValue().toString()).checkIsAgent(agentKeyWords, welcomeKeyWords));
            }
            speekerUser = speekerUser.stream().sorted(Comparator.comparing(SpeekerUserItem::getFirstMatchIndex).thenComparing(SpeekerUserItem::getWelcomeMatchIndex)).collect(Collectors.toList());
            if (speekerUser.size() <= 1) {
                if (speekerUser.get(0).isAgent()) {
                    agentRole.put(speekerUser.get(0).getSpeekerId(), "agent");
                }
            } else {
                for (int i = 0; i < speekerUser.size() - 1; i++) {
                    SpeekerUserItem item = speekerUser.get(i);
                    if (item.isAgent()) {
                        agentRole.put(item.getSpeekerId(), "agent");
                    }
                    if (i == (speekerUser.size() - 2)) {
                        //如果前面都是有非坐席，最后一个再检查是否是坐席
                        if (agentRole.size() != (speekerUser.size() - 1)) {
                            item = speekerUser.get(i + 1);
                            if (item.isAgent()) {
                                agentRole.put(item.getSpeekerId(), "agent");
                            }
                        }
                        //如果前面都是坐席，那最后一个不检查
                    }

                }
            }
        }


        //保存到数据表
        List<CallcenterRecordDetailSound> soundList = new ArrayList<>();
        for (SentenceDetail item : taskStatus.getResultDetail()) {
            String finalSentence = StrUtil.isNotEmpty(item.getFinalSentence()) ? item.getFinalSentence().trim() : "";
            Long speakerId = item.getSpeakerId();
            CallcenterRecordDetailSound tar = new CallcenterRecordDetailSound();
            tar.setSound(detail.getSound());
            tar.setCallRecordDetailId(detail.getId());
            tar.setSoundText(finalSentence);
            if (item.getStartMs() != null) {
                tar.setStartSecond(NumberUtil.div(item.getStartMs().longValue(), 1000) + "");
            }
            if (item.getEndMs() != null) {
                tar.setEndSecond(NumberUtil.div(item.getEndMs().longValue(), 1000) + "");
            }
            tar.setCreateTime(new Date());
            //识别坐席不了
            if (!agentRole.isEmpty()) {
                if (agentRole.containsKey(speakerId)) {
                    tar.setConversationRole("坐席");
                    tar.setUserId(detail.getUserId());
                } else {
                    tar.setConversationRole("客户");
                }

            }
            soundList.add(tar);
        }
        //批量保存
        callcenterRecordDetailService.deleteSoundsByDetailId(detail.getId());
        callcenterRecordDetailService.saveBatchSounds(soundList);

        //保存转换结果
        CallcenterRecordDetailSoundAsrresultEntity asrResult = new CallcenterRecordDetailSoundAsrresultEntity();
        asrResult.setResultJson(JSONObject.toJSONString(taskStatus.getResultDetail()));
        asrResult.setCreateTime(new Date());
        asrResult.setCallRecordDetailId(detail.getId());
        callcenterRecordDetailService.deleteSoundAsrResultByDetailId(detail.getId());
        callcenterRecordDetailService.saveSoundAsrResult(asrResult);

        //更新转换结果
        // 更新 通话记录表 为已录音分割
        CallcenterRecordDetail detailParam = new CallcenterRecordDetail();
        detailParam.setId(detail.getId());
        detailParam.setSoundText(taskStatus.getResult()); //转换结果文字
        detailParam.setDivisionMark("1"); // 语音分割标记（0-分割 ，1-分割成功）
        callcenterRecordDetailService.updateCallcenterRecordDetail(detailParam);

        return R.ok();
    }

    @Data
    public static class SpeekerUserItem {
        private Long speekerId;
        private String voiceText;
        private int firstMatchIndex = 999999;
        private int welcomeMatchIndex = 999999;
        private boolean isAgent = false;
        private List<String> matchWelComeKeyWords = new ArrayList<>();
        private List<String> matchAgentKeyWords = new ArrayList<>();

        public SpeekerUserItem(Long speekerId, String voiceText) {
            this.speekerId = speekerId;
            this.voiceText = voiceText;
        }

        public SpeekerUserItem checkIsAgent(String[] agentKeyWords, String[] welcomeKeyWords) {
            String sentences = voiceText != null ? voiceText.toString().trim() : "";
            //对语句进行分段判断 根据标点符号进行分割
            String[] splitSentence = sentences.split("(?<=[,，。！？!?])");
            List<Integer> matchIndex = new ArrayList<>();
            List<Integer> welcomeMatchIndex = new ArrayList<>();
            for (int i = 0; i < splitSentence.length; i++) {
                String finalSentence = splitSentence[i];
                if (StrUtil.isNotEmpty(finalSentence)) {
                    finalSentence = finalSentence.trim();
                    for (String keyWord : agentKeyWords) {
                        if (keyWord.contains("|")) {
                            String arr[] = keyWord.split("\\|");
                            int idx = finalSentence.indexOf(arr[0]);
                            if (idx != -1 && arr.length > 1) {
                                int endIdx = finalSentence.indexOf(arr[1], idx);
                                if (endIdx != -1) {
                                    this.isAgent = true;
                                    matchIndex.add(idx);
                                    matchAgentKeyWords.add(finalSentence);
                                }
                            }
                        } else {
                            int idx = finalSentence.indexOf(keyWord);
                            if (idx != -1) {
                                this.isAgent = true;
                                matchAgentKeyWords.add(finalSentence);
                                matchIndex.add(idx);
                            }
                        }
                    }
                    for (String keyWord : welcomeKeyWords) {
                        if (keyWord.contains("|")) {
                            String arr[] = keyWord.split("\\|");
                            int idx = finalSentence.indexOf(arr[0]);
                            if (idx != -1 && arr.length > 1) {
                                int endIdx = finalSentence.indexOf(arr[1], idx);
                                if (endIdx != -1) {
                                    this.isAgent = true;
                                    welcomeMatchIndex.add(idx);
                                    matchWelComeKeyWords.add(finalSentence);
                                }
                            }
                        } else {
                            int idx = finalSentence.indexOf(keyWord);
                            if (idx != -1) {
                                this.isAgent = true;
                                welcomeMatchIndex.add(idx);
                                matchAgentKeyWords.add(finalSentence);
                            }
                        }
                    }
                }
            }
            if (!matchIndex.isEmpty()) {
                Collections.sort(matchIndex);
                this.firstMatchIndex = matchIndex.get(0);
            }
            if (!welcomeMatchIndex.isEmpty()) {
                Collections.sort(welcomeMatchIndex);
                this.welcomeMatchIndex = welcomeMatchIndex.get(0);
            }
            return this;
        }

    }
//    test
//    @PostConstruct
//    public void test() throws IOException {
//        long detailId =3987982l;
//        String filePath = "D:\\temp\\gd12348-asr\\3987982.json";
//        String json = FileCopyUtils.copyToString(new FileReader(filePath));
//        List<SentenceDetail> detailList = JSONArray.parseArray(json,SentenceDetail.class);
//        CallcenterRecordDetail detail = callcenterRecordDetailService.selectDetailByID(detailId);
//        TaskStatus taskStatus = new TaskStatus();
//        taskStatus.setResultDetail(detailList.toArray(new SentenceDetail[]{}));
//        saveAsrTranserResult(taskStatus,detail);
//
//        detailId = 3987985l;
//        filePath = "D:\\temp\\gd12348-asr\\3987985.json";
//        json = FileCopyUtils.copyToString(new FileReader(filePath));
//        detail = callcenterRecordDetailService.selectDetailByID(detailId);
//        detailList = JSONArray.parseArray(json,SentenceDetail.class);
//        taskStatus = new TaskStatus();
//        taskStatus.setResultDetail(detailList.toArray(new SentenceDetail[]{}));
//        saveAsrTranserResult(taskStatus,detail);
//
//    }


    /**
     * 调用开始转换接口
     *
     * @param asrConfig
     * @param outFileUuid
     * @return
     */
    private R callTencentAsrTranfer(JSONObject asrConfig, String outFileUuid) {
        String secretId = asrConfig.getString("secretId");
        String secretKey = asrConfig.getString("secretKey");
        String outVisitUrl = asrConfig.getString("outVisitUrl");
        String speekerNumber = StrUtil.emptyToDefault(asrConfig.getString("speekerNumber"), "3");//识别人数：默认三个
        try {
            //外部访问地址
            String outUrl = outVisitUrl + "?fileId=" + outFileUuid;

            Credential cred = new Credential(secretId, secretKey);

            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setSignMethod(ClientProfile.SIGN_TC3_256); // 指定签名算法(默认为TC3-HMAC-SHA256)
            clientProfile.setDebug(true);//打印日志

            // 实例化一个cvm实例信息查询请求对象,每个接口都会对应一个request对象。
            CreateRecTaskRequest req = new CreateRecTaskRequest();
            // 8k_zh：中文电话通讯；
            // 8k_zh_large：普方大模型引擎【大模型版】。当前模型同时支持中文、多种中文方言等语言的识别，模型参数量极大，语言模型性能增强，针对电话音频中各类场景、各类中文方言的识别准确率极大提升，点击这里 对比常规版本与普方大模型版本的识别效果；
            // 16k_zh：中文普通话通用引擎，支持中文普通话和少量英语，使用丰富的中文普通话语料训练，覆盖场景广泛，适用于除电话通讯外的所有中文普通话识别场景；
            // 16k_zh_large：普方英大模型引擎【大模型版】。当前模型同时支持中文、英文、多种中文方言等语言的识别，模型参数量极大，语言模型性能增强，针对噪声大、回音大、人声小、人声远等低质量音频的识别准确率极大提升，点击这里 对比中文普通话常规版本与普方英大模型版本的识别效果
            req.setEngineModelType("8k_zh");


            //识别声道数
            //1：单声道（16k音频仅支持单声道，请勿设置为双声道）；
            //2：双声道（仅支持8k电话音频，且双声道应分别为通话双方）
            req.setChannelNum(1L);
            //识别结果返回样式
            // 0：基础识别结果（仅包含有效人声时间戳，无词粒度的详细识别结果）；
            //1：基础识别结果之上，增加词粒度的详细识别结果（包含词级别时间戳、语速值，不含标点）；
            //2：基础识别结果之上，增加词粒度的详细识别结果（包含词级别时间戳、语速值和标点）；
            //3：基础识别结果之上，增加词粒度的详细识别结果（包含词级别时间戳、语速值和标点），且识别结果按标点符号分段，适用字幕场景；
            req.setResTextFormat(2l);//改为2返回样式，change by zhang 20240802

            //	音频数据来源
            //0：音频URL；
            //1：音频数据（post body）
            req.setSourceType(0L);

            //音频URL的地址（需要公网环境浏览器可下载）
            //当 SourceType 值为 0 时须填写该字段，为 1 时不需要填写
            //注意：
            //1. 请确保录音文件时长在5个小时（含）之内，否则可能识别失败；
            //2. 请保证文件的下载速度，否则可能下载失败
            //示例值：https://audio.cos.ap-guangzhou.myqcloud.com/example.wav
            req.setUrl(outUrl);

            // 是否开启说话人分离
            //0：不开启； 1：开启（仅支持以下引擎：8k_zh/16k_zh/16k_ms/16k_en/16k_id/16k_zh_large/16k_zh_dialect，且ChannelNum=1时可用）；
            //默认值为 0
            //注意：
            //8k双声道电话音频请按 ChannelNum 识别声道数 的参数描述使用默认值
            //示例值：0
            req.setSpeakerDiarization(1l);

            //说话人分离人数
            //需配合开启说话人分离使用，不开启无效，取值范围：0-10
            //0：自动分离（最多分离出20个人）；
            //1-10：指定人数分离；
            //默认值为 0
            //示例值：0
            req.setSpeakerNumber(Long.parseLong(speekerNumber));

            // 实例化要请求产品(以cvm为例)的client对象,clientProfile是可选的
            AsrClient client = new AsrClient(cred, "ap-guangzhou", clientProfile);

            // 返回的resp是一个DescribeInstancesResponse类的实例，与请求对象对应
            CreateRecTaskResponse resp = client.CreateRecTask(req);

            if (resp != null && resp.getData() != null && resp.getData().getTaskId() != null) {
                log.info("调用转换接口成功.{}.", JSONObject.toJSONString(resp));
                return R.ok(resp.getData().getTaskId(), "success");
            } else {
                log.warn("调用腾讯转换接口出错!req={},resp={}", JSONObject.toJSONString(req), JSONObject.toJSONString(resp));
                return R.fail("调用转换接口出错!");
            }

        } catch (Exception ex) {
            log.error("调用腾讯接口失败!" + ex.getMessage(), ex);
            return R.fail("调用转换接口出错!");
        }

    }

    /**
     * 调用查询转换结果接口
     *
     * @param asrConfig
     * @param taskId
     * @return
     */
    @Override
    public R callTencentAsrTransResult(JSONObject asrConfig, Long taskId) {
        String secretId = asrConfig.getString("secretId");
        String secretKey = asrConfig.getString("secretKey");
        try {
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setSignMethod(ClientProfile.SIGN_TC3_256); // 指定签名算法(默认为TC3-HMAC-SHA256)
            clientProfile.setDebug(true);//打印日志

            Credential cred = new Credential(secretId, secretKey);

            //查询转换结果
            DescribeTaskStatusRequest req = new DescribeTaskStatusRequest();
            req.setTaskId(taskId);

            // 实例化要请求产品(以cvm为例)的client对象,clientProfile是可选的
            AsrClient client = new AsrClient(cred, "ap-shanghai", clientProfile);

            // 返回的resp是一个DescribeInstancesResponse类的实例，与请求对象对应
            DescribeTaskStatusResponse resp = client.DescribeTaskStatus(req);
            if (resp != null && resp.getData() != null) {
                log.info("调用查询结果接口成功.{}.", JSONObject.toJSONString(resp));
                return R.ok(resp.getData());
            } else {
                log.warn("调用查询结果接口出错!req={},resp={}", JSONObject.toJSONString(req), JSONObject.toJSONString(resp));
                return R.fail("调用查询结果接口出错!");
            }
        } catch (Exception ex) {
            log.error("调用腾讯查询结果接口失败!" + ex.getMessage(), ex);
            return R.fail("调用查询结果接口出错!");
        }
    }

}
