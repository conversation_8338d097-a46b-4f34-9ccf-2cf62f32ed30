package com.gobon.api.controller;

import com.gobon.common.utils.SecurityUtils;
import com.gobon.framework.task.IntelligenceQualityTask;
import com.gobon.framework.task.LocalAsrTask;
import com.gobon.framework.task.WlQualityTask;
import com.gobon.framework.web.domain.AjaxResult;
import com.gobon.framework.web.domain.R;
import com.gobon.project.conversation.domain.CallcenterRecordDetailEntity;
import com.gobon.project.conversation.mapper.CallcenterRecordDetailMapper;
import com.gobon.project.conversation.service.ICallcenterRecordDetailService;
import com.gobon.project.conversation.service.ICallcenterRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 语音转文字录音文件给外部调用(腾讯语音转文字)
 * <AUTHOR>
 * @date 2024/7/26 16:21
 * @see AsrOpenFileController
 * @since
 */
@Slf4j
@RestController
@RequestMapping("/open")
public class AsrOpenFileController {

    @Resource
    private LocalAsrTask localAsrTask;

    @Resource
    private IntelligenceQualityTask intelligenceQualityTask;

    @Resource
    private WlQualityTask wlQualityTask;

    @Resource
    private ICallcenterRecordDetailService callcenterRecordDetailService;

    @Resource
    private ICallcenterRecordService callcenterRecordService;

    @Resource
    private CallcenterRecordDetailMapper callcenterRecordDetailMapper;

    @GetMapping(value = "/runAsrTask")
    public R runAsrTask() {
        localAsrTask.execute();
        return new R();
    }

    @GetMapping("/runQualityTask")
    public R runQualityTask(@RequestParam("id") String id) {
        intelligenceQualityTask.scanCallRecordParam(id, "gd12348.com", SecurityUtils.getLoginUser(), "1",false);
        return new R();
    }

    @GetMapping("/runWlQualityTask")
    public R runWlQualityTask() {
        wlQualityTask.execute();
        return new R();
    }


    @PostMapping("/transformNoMark")
    public AjaxResult tranFormNoMark() {
        try{

            List<CallcenterRecordDetailEntity> list = callcenterRecordDetailMapper.getNoMarkDetailList(LocalDateTime.now().minusDays(4), LocalDateTime.now().minusHours(2));

            for(CallcenterRecordDetailEntity entity: list) {
                log.info("transformCall: {} - {}", entity.getId(), entity.getBillNumber());
                callcenterRecordService.transformCall(entity.getId());
            }

            return AjaxResult.success("成功");
        }catch (Exception e){
            return AjaxResult.error(e.getMessage());
        }
    }
}
