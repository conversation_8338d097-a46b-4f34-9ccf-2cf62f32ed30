<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gobon.project.callcenter.mapper.CallcenterMultipartyDetailMapper">

    <resultMap type="com.gobon.project.callcenter.domain.vo.CallcenterMultipartyListVO" id="CallcenterMultipartyDetailResult">
        <result property="id"    column="id"    />
        <result property="multipartyId"    column="multiparty_id"    />
        <result property="userNumber"    column="user_number"    />
        <result property="userPhone"    column="user_phone"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="inviteTime"    column="invite_time"    />
        <result property="inTime"    column="in_time"    />
        <result property="outTime"    column="out_time"    />
        <result property="outType"    column="out_type"    />
        <result property="conversationTime"    column="conversation_time"    />

        <result property="createTime"    column="create_time"    />
        <result property="createUserId"    column="create_user_id"    />
        <result property="createUserName"    column="create_user_name"    />
        <result property="initnum"    column="initnum"    />
        <result property="sound"    column="sound"    />
        <result property="recordDetailId"    column="record_detail_id"    />
        <result property="callTitle"    column="call_title"    />
        <result property="callSummary"    column="call_summary"    />
        <result property="callSummaryState"    column="call_summary_state"    />

        <result property="createUser"    column="create_user"    />
    </resultMap>

    <resultMap id="callcenterMultipartyResult" type="com.gobon.project.callcenter.domain.CallcenterMultiparty">
        <id property="id" column="id" />
        <result property="createUserId" column="create_user_id" />
        <result property="createUserName" column="create_user_name" />
        <result property="createTime" column="create_time" />
        <result property="sound" column="sound" />
        <result property="freeswitchCallId" column="freeswitch_call_id" />
        <result property="initnum" column="initnum" />
        <result property="recordDetailId" column="record_detail_id"/>
        <result property="type" column="type"/>
        <result property="startTime" column="start_time"/>
        <result property="shutTime" column="shut_time"/>
        <result property="state" column="state"/>
        <collection property="multipartyDetails" javaType="ArrayList" ofType="com.gobon.project.callcenter.domain.CallcenterMultipartyDetail">
            <id property="id" column="d_id"/>
            <result property="userNumber" column="user_number"/>
            <result property="userPhone" column="user_phone"/>
            <result property="userId" column="user_id"/>
            <result property="userName" column="user_name"/>
            <result property="inviteTime" column="invite_time"/>
            <result property="inTime" column="in_time"/>
            <result property="outTime" column="out_time"/>
            <result property="outType" column="out_type"/>
            <result property="joinState" column="join_state"/>
            <result property="thirdParty" column="third_party"/>
            <result property="hotLineId" column="hot_line_id"/>
        </collection>
    </resultMap>

    <sql id="selectCallcenterMultipartyDetailVo">
        select id, multiparty_id, user_number, user_phone, user_id, user_name, invite_time, in_time, out_time, out_type from callcenter_multiparty_detail
    </sql>

    <select id="selectCallcenterMultipartyDetailList" parameterType="com.gobon.project.callcenter.domain.param.CallcenterMultipartyListParam" resultMap="CallcenterMultipartyDetailResult">
        select t.* from (
            select
                c.*,
                t.participate_user_id,
                t.participate_name,
                t.user_name,
                t.user_number
            from
            (
                SELECT
                    t.id,
                    c.type,
                    c.id multiparty_id,
                    c.create_user_id,
                    c.create_time,
                    c.call_summary_state,
                    concat( c.create_user_name, '(', t.user_number, ')' ) create_user_name,
                    t.user_number create_user_number,
                    IF ( c.create_user_id = #{userId}, 1, 0 ) create_user,
                    c.call_title,
                    timestampdiff( SECOND, c.start_time, c.shut_time ) conversation_time
                FROM
                callcenter_multiparty c
                LEFT JOIN callcenter_multiparty_detail t ON c.id = t.multiparty_id AND c.create_user_id = t.user_id
                left join sys_user su on su.user_id = c.create_user_id
                left join sys_dept sd on su.dept_id = sd.dept_id
                where
                    1 = 1
                    and c.type = '1'
                    <if test='conversationStartTime !=null and conversationStartTime != "" '>
                        and t.in_time  <![CDATA[>=]]> str_to_date(#{conversationStartTime},'%Y-%m-%d %H:%i:%s')
                    </if>
                    <if test='conversationEndTime !=null and conversationEndTime != "" '>
                        and t.in_time <![CDATA[<=]]> str_to_date(#{conversationEndTime},'%Y-%m-%d %H:%i:%s')
                    </if>
                    <if test='pageSource == "1"'>
                        <!-- 数据范围过滤 -->
                        ${dataScope}
                    </if>
            ) c
            left join
            (
                SELECT
                    t.multiparty_id,
                    GROUP_CONCAT( concat( t.user_id ) SEPARATOR ',' ) participate_user_id,
                    GROUP_CONCAT( concat( t.user_name, '(', t.user_number, ')' ) SEPARATOR '，' ) participate_name,
                    GROUP_CONCAT( concat( t.user_name ) SEPARATOR ',' ) user_name,
                    GROUP_CONCAT( concat( t.user_number ) SEPARATOR ',' ) user_number
                FROM
                (
                    select DISTINCT t.multiparty_id from
                    callcenter_multiparty_detail t
                    where
                    1 = 1
                ) d
                left join callcenter_multiparty_detail t on d.multiparty_id = t.multiparty_id and (t.join_state = 1 or t.in_time is not null)
                WHERE
                    1 = 1
                <if test='conversationStartTime !=null and conversationStartTime != "" '>
                    and t.in_time  <![CDATA[>=]]> str_to_date(#{conversationStartTime},'%Y-%m-%d %H:%i:%s')
                </if>
                <if test='conversationEndTime !=null and conversationEndTime != "" '>
                    and t.in_time <![CDATA[<=]]> str_to_date(#{conversationEndTime},'%Y-%m-%d %H:%i:%s')
                </if>
                GROUP BY
                    t.multiparty_id
                ORDER BY
                    t.in_time DESC
            ) t ON t.multiparty_id = c.multiparty_id
            where 1 = 1
            <if test='pageSource == "2"'>
                and INSTR( t.participate_user_id,  #{userId} ) > 0
            </if>
            <if test="participateName != null  and participateName != ''">
                AND ( INSTR( t.user_number, #{participateName} ) > 0 OR INSTR( t.user_name, #{participateName} ) > 0 )
            </if>
            order by
            c.create_time desc
            ) t
            where
                1 = 1
                and t.type = 1
                <if test="createUserName != null  and createUserName != ''"> and (t.create_user_name like concat('%', #{createUserName}, '%') or t.create_user_number = #{createUserName} )</if>
                <if test="theme != null  and theme != ''">  and t.call_title like concat('%', #{theme}, '%')</if>
                <if test='createUser != null  and createUser != "" and createUser == "1"'> and t.create_user_id = #{userId} </if>
                <if test='createUser != null  and createUser != "" and createUser == "0"'> and FIND_IN_SET(#{userId}, t.participate_user_id) </if>

    </select>

    <resultMap type="com.gobon.project.callcenter.domain.vo.CallcenterMultipartyDetailsVO" id="CallcenterMultipartyDetailsVO">
        <result property="id"    column="id"    />
        <result property="multipartyId"    column="multiparty_id"    />
        <result property="userNumber"    column="user_number"    />
        <result property="userPhone"    column="user_phone"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="inviteTime"    column="invite_time"    />
        <result property="inTime"    column="in_time"    />
        <result property="outTime"    column="out_time"    />
        <result property="outType"    column="out_type"    />

        <result property="createUserId"    column="create_user_id"    />
        <result property="createUserName"    column="create_user_name"    />
        <result property="initnum"    column="initnum"    />
        <result property="sound"    column="sound"    />
        <result property="recordDetailId"    column="record_detail_id"    />
        <result property="callTitle"    column="call_title"    />
        <result property="callSummary"    column="call_summary"    />
        <result property="callSummaryState"    column="call_summary_state"    />

        <result property="conversationTime"    column="conversation_time"    />
        <result property="createUser"    column="create_user"    />
        <result property="participateName"    column="participate_name"    />
        <result property="participateTotal"    column="participate_total"    />

        <collection property="insideMemberInfoVOS"
                    ofType="com.gobon.project.callcenter.domain.vo.InsideMemberInfoVO"
                    select="selectInsideMemberInfoVO"
                    column="multiparty_id" />
    </resultMap>

    <select id="getLists" parameterType="com.gobon.project.callcenter.domain.param.InsideDetailsParam" resultMap="CallcenterMultipartyDetailsVO">
        SELECT
            t.id,
            t.multiparty_id,
            t.user_number,
            t.user_phone,
            t.user_id,
            t.user_name,
            t.invite_time,
            t.in_time,
            t.out_time,
            t.out_type,
            c.create_user_id,
            c.create_user_name,
            c.create_time,
            c.initnum,
            c.sound,
            c.record_detail_id,
            c.call_title,
            c.call_summary,
            c.call_summary_state,
            timestampdiff(SECOND,t.in_time,t.out_time) conversation_time,
            if(c.create_user_id = #{userId}, 1,0) create_user,
            g.participate_name
        FROM
            callcenter_multiparty_detail t
            LEFT JOIN callcenter_multiparty c ON t.multiparty_id = c.id
            left join sys_user su on su.user_id = t.user_id
            left join
            (
                SELECT
                    t.multiparty_id,
                    GROUP_CONCAT( concat( t.user_name, '(', t.user_number, ')' ) SEPARATOR '，' ) participate_name
                FROM
                (
                    SELECT DISTINCT
                        t.multiparty_id,
                        t.user_name,
                        t.user_number
                    FROM
                        callcenter_multiparty_detail t
                    LEFT JOIN callcenter_multiparty c ON t.multiparty_id = c.id
                    LEFT JOIN sys_user u ON u.user_id = c.create_user_id
                    WHERE
                        1 = 1
                        AND (t.in_time IS NOT NULL OR t.out_time IS NOT NULL)
                    ORDER BY
                        t.in_time asc
                ) t
            ) g
            on t.multiparty_id = g.multiparty_id
        WHERE
            1 = 1
            and c.type = '1'
            <choose>
                <when test='pageSource != null and pageSource != "" and pageSource == "2"'>
                    and t.user_id = #{userId}
                </when>
                <otherwise>
                    <!-- 数据范围过滤 -->
                    ${dataScope}
                </otherwise>
            </choose>
    </select>

    <select id="selectDetailById" parameterType="com.gobon.project.callcenter.domain.param.InsideDetailsParam" resultMap="CallcenterMultipartyDetailsVO">
        SELECT
            t.id,
            t.multiparty_id,
            t.user_number,
            t.user_phone,
            t.user_id,
            t.user_name,
            t.invite_time,
            t.in_time,
            t.out_time,
            t.out_type,
            c.create_time,
            c.create_user_id,
            c.create_user_name,
            c.initnum,
            c.sound,
            c.record_detail_id,
            if(c.create_user_id = #{userId},c.call_title,if(c.call_summary_state = 1,c.call_title,'')) call_title,
	        if(c.create_user_id = #{userId},c.call_summary,if(c.call_summary_state = 1,c.call_summary,'')) call_summary,
            c.call_summary_state,
            timestampdiff(SECOND,t.in_time,t.out_time) conversation_time,
            if(c.create_user_id = #{userId}, 1,0) create_user,
            g.participate_name,
            g.participate_total
        FROM
            callcenter_multiparty_detail t
            LEFT JOIN callcenter_multiparty c ON t.multiparty_id = c.id
            left join
            (
            SELECT
                count( 1 ) participate_total,
                t.multiparty_id,
                GROUP_CONCAT( concat( t.nick_name, '(', t.user_number, ')' ) SEPARATOR '，' ) participate_name
            FROM
                (
                SELECT DISTINCT
                    t.user_id,
                    t.user_name,
                    t.user_number,
                    t.multiparty_id,
                    u.nick_name
                FROM
                    callcenter_multiparty_detail t
                    LEFT JOIN ( SELECT * FROM callcenter_multiparty_detail t WHERE t.id = #{id} ) s ON t.multiparty_id = s.multiparty_id
                    LEFT JOIN callcenter_multiparty c ON t.multiparty_id = c.id
                    LEFT JOIN sys_user u ON u.user_id = t.user_id
                WHERE
                    1 = 1
                    AND (t.in_time IS NOT NULL OR t.out_time IS NOT NULL)
                    AND t.multiparty_id = s.multiparty_id
                ORDER BY
                    t.in_time asc
                ) t
            ) g
            on t.multiparty_id = g.multiparty_id
        WHERE
            t.id = #{id}
    </select>

    <select id="selectInsideMemberInfoVO" parameterType="Long" resultType="com.gobon.project.callcenter.domain.vo.InsideMemberInfoVO">
        SELECT
            t.user_number,
            t.user_phone,
            t.user_id,
            t.user_name,
            t.invite_time,
            t.in_time,
            t.out_time,
            t.out_type
        FROM
            callcenter_multiparty_detail t
        WHERE
            1 = 1
            AND (t.in_time IS NOT NULL OR t.out_time IS NOT NULL)
            and t.multiparty_id = #{multiparty_id}
            order by t.in_time asc
    </select>

    <select id="selectCallcenterMultipartyDetailByFreeswitchCallId"  resultMap="callcenterMultipartyResult">
        select
            cm.id,
            cm.create_user_id,
            cm.create_user_name,
            cm.create_time,
            cm.sound,
            cm.freeswitch_call_id,
            cm.initnum,
            cm.record_detail_id,
            cm.`type`,
            cm.start_time,
            cm.shut_time,
            cm.state,
            cmd.id d_id,
            cmd.user_number,
            cmd.user_phone,
            cmd.user_id,
            cmd.user_name,
            cmd.invite_time,
            cmd.in_time,
            cmd.out_time,
            cmd.out_type,
            cmd.join_state,
            cmd.hot_line_id,
            cmd.third_party
    from
        callcenter_multiparty cm
        left join callcenter_multiparty_detail cmd on cm.id = cmd.multiparty_id
    where
        cm.freeswitch_call_id = #{freeswitchCallId}
        and cmd.id in (
            select tmp.d_id from (
                select
                    substring_index(group_concat(m.id order by m.`invite_time` desc),',',1) d_id
                from
                    callcenter_multiparty c
                    left join callcenter_multiparty_detail m on c.id = m.multiparty_id
                    where c.freeswitch_call_id = #{freeswitchCallId}
                 group by m.user_phone
             ) tmp
        )
    </select>

    <update id="addSummary" parameterType="com.gobon.project.callcenter.domain.param.SummaryParam">
        update callcenter_multiparty
        <trim prefix="SET" suffixOverrides=",">
            <if test="callTitle != null  and callTitle != ''">call_title = #{callTitle},</if>
            <if test="callSummary != null  and callSummary != ''">call_summary = #{callSummary},</if>
            <if test="callSummaryState != null  and callSummaryState != ''">call_summary_state = #{callSummaryState},</if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insertCallcenterMultipartyDetail" parameterType="CallcenterMultipartyDetail">
        insert into callcenter_multiparty_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">id,</if>
            <if test="multipartyId != null ">multiparty_id,</if>
            <if test="userNumber != null  and userNumber != ''">user_number,</if>
            <if test="userPhone != null  and userPhone != ''">user_phone,</if>
            <if test="userId != null ">user_id,</if>
            <if test="userName != null  and userName != ''">user_name,</if>
            <if test="inviteTime != null ">invite_time,</if>
            <if test="createTime != null ">create_time,</if>
            <if test="inTime != null ">in_time,</if>
            <if test="outTime != null ">out_time,</if>
            <if test="outType != null  and outType != ''">out_type,</if>
            <if test="hotLineId != null ">hot_line_id,</if>
            <if test="thirdParty != 0 ">third_party,</if>
            <if test="domain != null  and domain != '' ">domain,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">#{id},</if>
            <if test="multipartyId != null ">#{multipartyId},</if>
            <if test="userNumber != null  and userNumber != ''">#{userNumber},</if>
            <if test="userPhone != null  and userPhone != ''">#{userPhone},</if>
            <if test="userId != null ">#{userId},</if>
            <if test="userName != null  and userName != ''">#{userName},</if>
            <if test="inviteTime != null ">#{inviteTime},</if>
            <if test="createTime != null ">#{createTime},</if>
            <if test="inTime != null ">#{inTime},</if>
            <if test="outTime != null ">#{outTime},</if>
            <if test="outType != null  and outType != ''">#{outType},</if>
            <if test="hotLineId != null">#{hotLineId},</if>
            <if test="thirdParty != 0">#{thirdParty},</if>
            <if test="domain != null and domain != '' ">#{domain},</if>
         </trim>
    </insert>

    <update id="updateCallcenterMultipartyDetail" parameterType="CallcenterMultipartyDetail">
        update callcenter_multiparty_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="multipartyId != null ">multiparty_id = #{multipartyId},</if>
            <if test="userNumber != null  and userNumber != ''">user_number = #{userNumber},</if>
            <if test="userPhone != null  and userPhone != ''">user_phone = #{userPhone},</if>
            <if test="userId != null ">user_id = #{userId},</if>
            <if test="userName != null  and userName != ''">user_name = #{userName},</if>
            <if test="inviteTime != null ">invite_time = #{inviteTime},</if>
            <if test="inTime != null ">in_time = #{inTime},</if>
            <if test="outTime != null ">out_time = #{outTime},</if>
            <if test="outType != null  and outType != ''">out_type = #{outType},</if>
            <if test="joinState != 0">join_state = #{joinState},</if>
            <if test="domain != null and domain != '' ">domain = #{domain},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCallcenterMultipartyDetailById" parameterType="Long">
        delete from callcenter_multiparty_detail where id = #{id}
    </delete>

    <delete id="deleteCallcenterMultipartyDetailByIds" parameterType="String">
        delete from callcenter_multiparty_detail where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
