<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gobon.project.intelligencequality.mapper.CallcenterQualityScoringResultMapper">

    <resultMap type="CallcenterQualityScoringResult" id="CallcenterQualityScoringResultResult">
        <result property="id" column="id"/>
        <result property="qualityInfoId" column="quality_info_id"/>
        <result property="qualityDetailId" column="quality_detail_id"/>
        <result property="callDetailId" column="call_detail_id"/>
        <result property="scoringRecordId" column="scoring_record_id"/>
        <result property="scoringRuleId" column="scoring_rule_id"/>
        <result property="scoringRuleName" column="scoring_rule_name"/>
        <result property="scoringRuleDetailId" column="scoring_rule_detail_id"/>
        <result property="scoringRuleDetailName" column="scoring_rule_detail_name"/>
        <result property="grossScore" column="gross_score"/>
        <result property="deductScore" column="deduct_score"/>
        <result property="score" column="score"/>
        <result property="sorted" column="sorted"/>
        <result property="domain" column="domain"/>
        <result property="ruleType" column="rule_type"/>
        <result property="typeName" column="typeName"/>
        <result property="keyWord" column="key_word"/>
    </resultMap>

    <sql id="selectCallcenterQualityScoringResultVo">
        select id, quality_info_id, quality_detail_id, call_detail_id, domain, scoring_record_id, scoring_rule_id, scoring_rule_name, scoring_rule_detail_id, scoring_rule_detail_name, gross_score, deduct_score, score, sorted from callcenter_quality_scoring_result
    </sql>

    <select id="selectCallcenterQualityScoringResultList" parameterType="CallcenterQualityScoringResult"
            resultMap="CallcenterQualityScoringResultResult">
        <include refid="selectCallcenterQualityScoringResultVo"/>
        <where>
            <if test="qualityInfoId != null ">and quality_info_id = #{qualityInfoId}</if>
            <if test="qualityDetailId != null ">and quality_detail_id = #{qualityDetailId}</if>
            <if test="callDetailId != null ">and call_detail_id = #{callDetailId}</if>
            <if test="domain != null and domain != ''">and domain = #{domain}</if>
            <if test="scoringRecordId != null ">and scoring_record_id = #{scoringRecordId}</if>
            <if test="scoringRuleId != null ">and scoring_rule_id = #{scoringRuleId}</if>
            <if test="scoringRuleName != null  and scoringRuleName != ''">and scoring_rule_name like concat('%',
                #{scoringRuleName}, '%')
            </if>
            <if test="scoringRuleDetailId != null ">and scoring_rule_detail_id = #{scoringRuleDetailId}</if>
            <if test="scoringRuleDetailName != null  and scoringRuleDetailName != ''">and scoring_rule_detail_name like
                concat('%', #{scoringRuleDetailName}, '%')
            </if>
            <if test="grossScore != null ">and gross_score = #{grossScore}</if>
            <if test="deductScore != null ">and deduct_score = #{deductScore}</if>
            <if test="score != null ">and score = #{score}</if>
            <if test="sorted != null ">and sorted = #{sorted}</if>
        </where>
    </select>

    <select id="selectCallcenterQualityScoringResultById" parameterType="Long"
            resultMap="CallcenterQualityScoringResultResult">
        <include refid="selectCallcenterQualityScoringResultVo"/>
        where id = #{id}
    </select>

    <insert id="insertCallcenterQualityScoringResult" parameterType="CallcenterQualityScoringResult">
        insert into callcenter_quality_scoring_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">id,</if>
            <if test="qualityInfoId != null ">quality_info_id,</if>
            <if test="qualityDetailId != null ">quality_detail_id,</if>
            <if test="callDetailId != null ">call_detail_id,</if>
            <if test="domain != null and domain != ''">domain,</if>
            <if test="scoringRecordId != null ">scoring_record_id,</if>
            <if test="scoringRuleId != null ">scoring_rule_id,</if>
            <if test="scoringRuleName != null  and scoringRuleName != ''">scoring_rule_name,</if>
            <if test="scoringRuleDetailId != null ">scoring_rule_detail_id,</if>
            <if test="scoringRuleDetailName != null  and scoringRuleDetailName != ''">scoring_rule_detail_name,</if>
            <if test="grossScore != null ">gross_score,</if>
            <if test="deductScore != null ">deduct_score,</if>
            <if test="score != null ">score,</if>
            <if test="sorted != null ">sorted,</if>
            <if test="ruleType != null">rule_type,</if>
            <if test="typeName != null and typeName != ''">type_name,</if>
            <if test="keyWord != null and keyWord != ''">key_word,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">#{id},</if>
            <if test="qualityInfoId != null ">#{qualityInfoId},</if>
            <if test="qualityDetailId != null ">#{qualityDetailId},</if>
            <if test="callDetailId != null ">#{callDetailId},</if>
            <if test="domain != null and domain != ''">#{domain},</if>
            <if test="scoringRecordId != null ">#{scoringRecordId},</if>
            <if test="scoringRuleId != null ">#{scoringRuleId},</if>
            <if test="scoringRuleName != null  and scoringRuleName != ''">#{scoringRuleName},</if>
            <if test="scoringRuleDetailId != null ">#{scoringRuleDetailId},</if>
            <if test="scoringRuleDetailName != null  and scoringRuleDetailName != ''">#{scoringRuleDetailName},</if>
            <if test="grossScore != null ">#{grossScore},</if>
            <if test="deductScore != null ">#{deductScore},</if>
            <if test="score != null ">#{score},</if>
            <if test="sorted != null ">#{sorted},</if>
            <if test="ruleType != null">#{ruleType},</if>
            <if test="typeName != null and typeName != ''">#{typeName},</if>
            <if test="keyWord != null and keyWord != ''">#{keyWord},</if>
        </trim>
    </insert>

    <update id="updateCallcenterQualityScoringResult" parameterType="CallcenterQualityScoringResult">
        update callcenter_quality_scoring_result
        <trim prefix="SET" suffixOverrides=",">
            <if test="qualityInfoId != null ">quality_info_id = #{qualityInfoId},</if>
            <if test="qualityDetailId != null ">quality_detail_id = #{qualityDetailId},</if>
            <if test="callDetailId != null ">call_detail_id = #{callDetailId},</if>
            <if test="domain != null and domain != ''">domain = #{domain},</if>
            <if test="scoringRecordId != null ">scoring_record_id = #{scoringRecordId},</if>
            <if test="scoringRuleId != null ">scoring_rule_id = #{scoringRuleId},</if>
            <if test="scoringRuleName != null  and scoringRuleName != ''">scoring_rule_name = #{scoringRuleName},</if>
            <if test="scoringRuleDetailId != null ">scoring_rule_detail_id = #{scoringRuleDetailId},</if>
            <if test="scoringRuleDetailName != null  and scoringRuleDetailName != ''">scoring_rule_detail_name =
                #{scoringRuleDetailName},
            </if>
            <if test="grossScore != null ">gross_score = #{grossScore},</if>
            <if test="deductScore != null ">deduct_score = #{deductScore},</if>
            <if test="score != null ">score = #{score},</if>
            <if test="sorted != null ">sorted = #{sorted},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCallcenterQualityScoringResultById" parameterType="Long">
        delete from callcenter_quality_scoring_result where id = #{id}
    </delete>

    <delete id="deleteCallcenterQualityScoringResultByIds" parameterType="String">
        delete from callcenter_quality_scoring_result where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="searchCallInfoById" parameterType="Long"
            resultType="com.gobon.project.intelligencequality.domain.vo.CallInfoVo">
        select crd.id,
               crd.record_id,
               crd.call_time,
               crd.start_time,
               crd.type call_type,
               crd.out_time          end_time,
               crd.conversation_time duration,
               crd.user_name,
               crd.satisf_id         satisfice_value,
               crd.statisf_name      satisfice_name,
               crd.service_type_id,
               crd.service_type_name,
               crd.help_flag,
               crd.multiparty_flag multiple_flag,
               crd.call_phone,
               crd.user_phone,
               crd.province,
               crd.city,
               crd.sound,
               crd.sound_text,crd.bill_number as billNumber
        from callcenter_record_detail crd
        where crd.id = #{id}
    </select>

    <select id="searchQualityRuleInfoById" parameterType="Long"
            resultType="com.gobon.project.intelligencequality.domain.vo.QualityScoringRuleVo">
        select cqsru.id,
               cqsru.rule_name,
               cqsru.scoring_grade_describe description,
               cqsru.gross_score
               from callcenter_quality_scoring_rule cqsru
        where cqsru.id = #{id}
    </select>

    <select id="searchQualityRuleItem"
            parameterType="com.gobon.project.intelligencequality.domain.param.QualityScoreResultParam"
            resultType="com.gobon.project.intelligencequality.domain.vo.QualityScoringRuleItemVo">
        select cqsre.id,
               cqsre.quality_info_id,
               cqsre.quality_detail_id,
               cqsre.call_detail_id,
               cqsre.scoring_rule_id,
               cqsre.scoring_rule_name,
               cqsre.rule_type,
               cqsre.type_name,
               cqsre.key_word,
               cqsre.scoring_rule_detail_id,
               cqsre.scoring_rule_detail_name,
               cqsre.gross_score,
               cqsre.deduct_score,
               cqsre.score,
               cqsrd.plus_deduct_score original_deduct_score,
               cqbr.sort as sorted
        from callcenter_quality_scoring_result cqsre
        left join callcenter_quality_scoring_rule_detail cqsrd on cqsre.scoring_rule_detail_id = cqsrd.id
        left join callcenter_quality_behavior_rule cqbr on cqbr.id = cqsrd.rule_id
        where cqsre.quality_info_id = #{quality.qualityInfoId}
          and cqsre.quality_detail_id = #{quality.qualityDetailId}
            <if test="quality.callDetailId != null">
                and cqsre.call_detail_id = #{quality.callDetailId}
            </if>
          and cqsre.scoring_rule_id = #{quality.scoringRuleId}
          and cqsre.scoring_record_id = #{quality.scoringRecordId}
        order by cqsre.rule_type,cqbr.sort, cqsre.sorted
    </select>

    <insert id="batchInsert">
        insert into callcenter_quality_scoring_result (
        id,
        quality_info_id,
        quality_detail_id,
        call_detail_id,
        scoring_record_id,
        scoring_rule_id,
        scoring_rule_name,
        rule_type,
        type_name,
        scoring_rule_detail_id,
        scoring_rule_detail_name,
        key_word,
        gross_score,
        deduct_score,
        score,
        sorted,
        domain
        )
        values
        <foreach collection="scoreResults" item="item" separator=",">
            (#{item.id},#{item.qualityInfoId},#{item.qualityDetailId},#{item.callDetailId},#{item.scoringRecordId},
            #{item.scoringRuleId},#{item.scoringRuleName},#{item.ruleType},#{item.typeName},#{item.scoringRuleDetailId},
            #{item.scoringRuleDetailName},#{item.keyWord},#{item.grossScore},#{item.deductScore},#{item.score},
            #{item.sorted},#{item.domain})
        </foreach>
    </insert>

    <delete id="deleteByScoringRecordId">
        delete from callcenter_quality_scoring_result where scoring_record_id = #{scoringRecordId} and domain = #{domain}
    </delete>
</mapper>
