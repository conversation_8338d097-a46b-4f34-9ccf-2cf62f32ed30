<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gobon.project.intelligencequality.mapper.CallcenterQualityScoringRuleMapper">
    
    <resultMap type="com.gobon.project.intelligencequality.domain.vo.CallcenterQualityScoringRuleVO" id="CallcenterQualityScoringRuleResult">
        <result property="id"    column="id"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="ruleDescribe"    column="rule_describe"    />
        <result property="scoringObject"    column="scoring_object"    />
        <result property="scoringSkillIds"    column="scoring_skill_ids"    />
        <result property="scoringSkillName"    column="scoring_skill_name"    />
        <result property="scoringSkillIdsDisplay"    column="scoring_skill_ids_display"    />
        <result property="scoringPeople"    column="scoring_people"    />
        <result property="scoringPeopleName"    column="scoring_people_name"    />
        <result property="appealPeriodCode"    column="appeal_period_code"    />
        <result property="appealPeriod"    column="appeal_period"    />
        <result property="scoringGrade"    column="scoring_grade"    />
        <result property="scoringGradeCode"    column="scoring_grade_code"    />
        <result property="scoringGradeDescribe"    column="scoring_grade_describe"    />
        <result property="scoringGradeScore"    column="scoring_grade_score"    />
        <result property="scoringGradeScoreDescribe"    column="scoring_grade_score_describe"    />
        <result property="grossScore"    column="gross_score"    />
        <result property="enableState"    column="enable_state"    />
        <result property="releaseState"    column="release_state"    />
        <result property="releaseTime"    column="release_time"    />
        <result property="releaseById"    column="release_by_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createById"    column="create_by_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateById"    column="update_by_id"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="deleteById"    column="delete_by_id"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="deleteBy"    column="delete_by"    />
    </resultMap>

    <sql id="selectCallcenterQualityScoringRuleVo">
        select id, rule_name, rule_describe, scoring_object, scoring_skill_ids, scoring_skill_name, scoring_skill_ids_display, scoring_people, scoring_people_name, appeal_period_code, appeal_period, scoring_grade, scoring_grade_code, scoring_grade_describe, scoring_grade_score, scoring_grade_score_describe, gross_score, enable_state, release_state, release_time, release_by_id, create_by, create_by_id, create_time, update_by_id, update_time, update_by, del_flag, delete_by_id, delete_time, delete_by from callcenter_quality_scoring_rule
    </sql>

    <select id="selectCallcenterQualityScoringRuleList" resultType="com.gobon.project.intelligencequality.domain.CallcenterQualityScoringRule">
        <include refid="selectCallcenterQualityScoringRuleVo"/>
        <where>
            1 = 1
            <if test="ruleName != null  and ruleName != ''"> and rule_name like concat('%', #{ruleName}, '%')</if>
            <if test="ruleDescribe != null  and ruleDescribe != ''"> and rule_describe = #{ruleDescribe}</if>
            <if test="scoringObject != null  and scoringObject != ''"> and scoring_object = #{scoringObject}</if>
            <if test="scoringSkillIds != null  and scoringSkillIds != ''"> and scoring_skill_ids = #{scoringSkillIds}</if>
            <if test="scoringSkillIdsDisplay != null  and scoringSkillIdsDisplay != ''"> and scoring_skill_ids_display = #{scoringSkillIdsDisplay}</if>
            <if test="scoringPeople != null  and scoringPeople != ''"> and scoring_people = #{scoringPeople}</if>
            <if test="appealPeriodCode != null and appealPeriodCode != '' "> and appeal_period_code = #{appealPeriodCode}</if>
            <if test="appealPeriod != null  and appealPeriod != ''"> and appeal_period = #{appealPeriod}</if>
            <if test="scoringGrade != null  and scoringGrade != ''"> and scoring_grade = #{scoringGrade}</if>
            <if test="scoringGradeCode != null and scoringGradeCode != ''"> and scoring_grade_code = #{scoringGradeCode}</if>
            <if test="scoringGradeDescribe != null  and scoringGradeDescribe != ''"> and scoring_grade_describe = #{scoringGradeDescribe}</if>
            <if test="scoringGradeScore != null  and scoringGradeScore != ''"> and scoring_grade_score = #{scoringGradeScore}</if>
            <if test="scoringGradeScoreDescribe != null  and scoringGradeScoreDescribe != ''"> and scoring_grade_score_describe = #{scoringGradeScoreDescribe}</if>
            <if test="grossScore != null "> and gross_score = #{grossScore}</if>
            <if test="enableState != null  and enableState != ''"> and enable_state = #{enableState}</if>
            <if test="releaseState != null  and releaseState != ''"> and release_state = #{releaseState}</if>
            <if test="releaseTime != null "> and release_time = #{releaseTime}</if>
            <if test="releaseById != null "> and release_by_id = #{releaseById}</if>
            <if test="createById != null "> and create_by_id = #{createById}</if>
            <if test="updateById != null "> and update_by_id = #{updateById}</if>
            <if test="deleteById != null "> and delete_by_id = #{deleteById}</if>
            <if test="deleteTime != null "> and delete_time = #{deleteTime}</if>
            <if test="deleteBy != null  and deleteBy != ''"> and delete_by = #{deleteBy}</if>
            <if test="startCreateTime != null">
                and create_time &gt;= #{startCreateTime}
            </if>
            <if test="endCreateTime != null">
                and create_time &lt; (#{endCreateTime} + INTERVAL 1 DAY)
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by like concat('%',#{createBy},'%')
            </if>
            <if test="platForm != null and platForm != ''">
                and plat_form = #{platForm}
            </if>
             and addr_area = #{addrArea}
             and del_flag = '0'
        </where>
        order by create_time desc
    </select>
    
    <select id="selectCallcenterQualityScoringRuleById" parameterType="com.gobon.project.intelligencequality.domain.param.ScoringRuleParam" resultMap="CallcenterQualityScoringRuleResult">
        <include refid="selectCallcenterQualityScoringRuleVo"/>
        where id = #{id} and addr_area = #{addrArea}
    </select>

    <select id="getScoringRule" parameterType="com.gobon.project.intelligencequality.domain.param.ScoringRuleParam" resultType="com.gobon.project.intelligencequality.domain.vo.ScoringRuleOptionVO">
        select
            id as value,
            rule_name as label
        from
            callcenter_quality_scoring_rule
        where
        1 = 1
        <if test="id != null ">and id = #{id}</if>
        <if test="enableState != null  and enableState != ''">and enable_state = #{enableState}</if>
        <if test="releaseState != null  and releaseState != ''">and release_state = #{releaseState}</if>
        <if test="platForm != null and platForm != ''"> and plat_form = #{platForm}</if>
        and addr_area = #{addrArea}
        and del_flag = '0'
    </select>

    <insert id="insertCallcenterQualityScoringRule" parameterType="com.gobon.project.intelligencequality.domain.CallcenterQualityScoringRule"  useGeneratedKeys="true" keyProperty="id">
        insert into callcenter_quality_scoring_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">id,</if>
            <if test="ruleName != null  and ruleName != ''">rule_name,</if>
            <if test="ruleDescribe != null  and ruleDescribe != ''">rule_describe,</if>
            <if test="scoringObject != null  and scoringObject != ''">scoring_object,</if>
            <if test="scoringSkillIds != null  and scoringSkillIds != ''">scoring_skill_ids,</if>
            <if test="scoringSkillName != null  and scoringSkillName != ''">scoring_skill_name,</if>
            <if test="scoringSkillIdsDisplay != null  and scoringSkillIdsDisplay != ''">scoring_skill_ids_display,</if>
            <if test="scoringPeople != null  and scoringPeople != ''">scoring_people,</if>
            <if test="scoringPeopleName != null  and scoringPeopleName != ''">scoring_people_name,</if>
            <if test="appealPeriodCode != null and appealPeriodCode != '' ">appeal_period_code,</if>
            <if test="appealPeriod != null  and appealPeriod != ''">appeal_period,</if>
            <if test="scoringGrade != null  and scoringGrade != ''">scoring_grade,</if>
            <if test="scoringGradeCode != null and scoringGradeCode != '' ">scoring_grade_code,</if>
            <if test="scoringGradeDescribe != null  and scoringGradeDescribe != ''">scoring_grade_describe,</if>
            <if test="scoringGradeScore != null  and scoringGradeScore != ''">scoring_grade_score,</if>
            <if test="scoringGradeScoreDescribe != null  and scoringGradeScoreDescribe != ''">scoring_grade_score_describe,</if>
            <if test="grossScore != null ">gross_score,</if>
            <if test="enableState != null  and enableState != ''">enable_state,</if>
            <if test="releaseState != null  and releaseState != ''">release_state,</if>
            <if test="releaseTime != null ">release_time,</if>
            <if test="releaseById != null ">release_by_id,</if>
            <if test="createBy != null  and createBy != ''">create_by,</if>
            <if test="createById != null ">create_by_id,</if>
            <if test="createTime != null ">create_time,</if>
            <if test="updateById != null ">update_by_id,</if>
            <if test="updateTime != null ">update_time,</if>
            <if test="updateBy != null  and updateBy != ''">update_by,</if>
            <if test="delFlag != null  and delFlag != ''">del_flag,</if>
            <if test="deleteById != null ">delete_by_id,</if>
            <if test="deleteTime != null ">delete_time,</if>
            <if test="deleteBy != null  and deleteBy != ''">delete_by,</if>
            <if test="addrArea != null  and addrArea != ''">addr_area,</if>
            <if test="platForm != null and platForm != ''">plat_form,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">#{id},</if>
            <if test="ruleName != null  and ruleName != ''">#{ruleName},</if>
            <if test="ruleDescribe != null  and ruleDescribe != ''">#{ruleDescribe},</if>
            <if test="scoringObject != null  and scoringObject != ''">#{scoringObject},</if>
            <if test="scoringSkillIds != null  and scoringSkillIds != ''">#{scoringSkillIds},</if>
            <if test="scoringSkillName != null  and scoringSkillName != ''">#{scoringSkillName},</if>
            <if test="scoringSkillIdsDisplay != null  and scoringSkillIdsDisplay != ''">#{scoringSkillIdsDisplay},</if>
            <if test="scoringPeople != null  and scoringPeople != ''">#{scoringPeople},</if>
            <if test="scoringPeopleName != null  and scoringPeopleName != ''">#{scoringPeopleName},</if>
            <if test="appealPeriodCode != null and appealPeriodCode != '' ">#{appealPeriodCode},</if>
            <if test="appealPeriod != null  and appealPeriod != ''">#{appealPeriod},</if>
            <if test="scoringGrade != null  and scoringGrade != ''">#{scoringGrade},</if>
            <if test="scoringGradeCode != null and scoringGradeCode != '' ">#{scoringGradeCode},</if>
            <if test="scoringGradeDescribe != null  and scoringGradeDescribe != ''">#{scoringGradeDescribe},</if>
            <if test="scoringGradeScore != null  and scoringGradeScore != ''">#{scoringGradeScore},</if>
            <if test="scoringGradeScoreDescribe != null  and scoringGradeScoreDescribe != ''">#{scoringGradeScoreDescribe},</if>
            <if test="grossScore != null ">#{grossScore},</if>
            <if test="enableState != null  and enableState != ''">#{enableState},</if>
            <if test="releaseState != null  and releaseState != ''">#{releaseState},</if>
            <if test="releaseTime != null ">#{releaseTime},</if>
            <if test="releaseById != null ">#{releaseById},</if>
            <if test="createBy != null  and createBy != ''">#{createBy},</if>
            <if test="createById != null ">#{createById},</if>
            <if test="createTime != null ">#{createTime},</if>
            <if test="updateById != null ">#{updateById},</if>
            <if test="updateTime != null ">#{updateTime},</if>
            <if test="updateBy != null  and updateBy != ''">#{updateBy},</if>
            <if test="delFlag != null  and delFlag != ''">#{delFlag},</if>
            <if test="deleteById != null ">#{deleteById},</if>
            <if test="deleteTime != null ">#{deleteTime},</if>
            <if test="deleteBy != null  and deleteBy != ''">#{deleteBy},</if>
            <if test="addrArea != null  and addrArea != ''">#{addrArea},</if>
            <if test="platForm != null and platForm != ''">#{platForm},</if>
        </trim>
    </insert>

    <update id="updateCallcenterQualityScoringRule" parameterType="CallcenterQualityScoringRule">
        update callcenter_quality_scoring_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleName != null  and ruleName != ''">rule_name = #{ruleName},</if>
            <if test="ruleDescribe != null  and ruleDescribe != ''">rule_describe = #{ruleDescribe},</if>
            <if test="scoringObject != null  and scoringObject != ''">scoring_object = #{scoringObject},</if>
            <if test="scoringSkillIds != null  and scoringSkillIds != ''">scoring_skill_ids = #{scoringSkillIds},</if>
            <if test="scoringSkillName != null  and scoringSkillName != ''">scoring_skill_Name = #{scoringSkillName},</if>
            <if test="scoringSkillIdsDisplay != null  and scoringSkillIdsDisplay != ''">scoring_skill_ids_display = #{scoringSkillIdsDisplay},</if>

            <choose>
                <when test='scoringObject == "1"'>
                    scoring_people = null,
                    scoring_people_name = null,
                </when>
                <otherwise>
                    <if test="scoringPeople != null  and scoringPeople != ''">scoring_people = #{scoringPeople},</if>
                    <if test="scoringPeopleName != null  and scoringPeopleName != ''">scoring_people_name = #{scoringPeopleName},</if>
                </otherwise>
            </choose>

            <if test="appealPeriodCode != null and appealPeriodCode != '' ">appeal_period_code = #{appealPeriodCode},</if>
            <if test="appealPeriod != null  and appealPeriod != ''">appeal_period = #{appealPeriod},</if>
            <if test="scoringGrade != null  and scoringGrade != ''">scoring_grade = #{scoringGrade},</if>
            <if test="scoringGradeCode != null and scoringGradeCode != '' ">scoring_grade_code = #{scoringGradeCode},</if>
            <if test="scoringGradeDescribe != null  and scoringGradeDescribe != ''">scoring_grade_describe = #{scoringGradeDescribe},</if>
            <if test="scoringGradeScore != null  and scoringGradeScore != ''">scoring_grade_score = #{scoringGradeScore},</if>
            <if test="scoringGradeScoreDescribe != null  and scoringGradeScoreDescribe != ''">scoring_grade_score_describe = #{scoringGradeScoreDescribe},</if>
            <if test="grossScore != null ">gross_score = #{grossScore},</if>
            <if test="enableState != null  and enableState != ''">enable_state = #{enableState},</if>
            <if test="releaseState != null  and releaseState != ''">release_state = #{releaseState},</if>
            <if test="releaseTime != null ">release_time = #{releaseTime},</if>
            <if test="releaseById != null ">release_by_id = #{releaseById},</if>
            <if test="createBy != null  and createBy != ''">create_by = #{createBy},</if>
            <if test="createById != null ">create_by_id = #{createById},</if>
            <if test="createTime != null ">create_time = #{createTime},</if>
            <if test="updateById != null ">update_by_id = #{updateById},</if>
            <if test="updateTime != null ">update_time = #{updateTime},</if>
            <if test="updateBy != null  and updateBy != ''">update_by = #{updateBy},</if>
            <if test="delFlag != null  and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="deleteById != null ">delete_by_id = #{deleteById},</if>
            <if test="deleteTime != null ">delete_time = #{deleteTime},</if>
            <if test="deleteBy != null  and deleteBy != ''">delete_by = #{deleteBy},</if>
            <if test="platForm != null and platForm != ''">plat_form = #{platForm},</if>
        </trim>
        where id = #{id}
        and addr_area = #{addrArea}
    </update>


    <delete id="deleteCallcenterQualityScoringRuleById" parameterType="com.gobon.project.intelligencequality.domain.param.ScoringRuleParam">
        delete from callcenter_quality_scoring_rule where id = #{id}
    </delete>

    <delete id="deleteCallcenterQualityScoringRuleByIds" parameterType="String">
        delete from callcenter_quality_scoring_rule where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>















    <resultMap type="com.gobon.project.intelligencequality.domain.vo.CallcenterQualityScoringRuleDetailVO" id="CallcenterQualityScoringRuleDetailResult">
        <result property="id"    column="id"    />
        <result property="scoringRuleId"    column="scoring_rule_id"    />
        <result property="ruleOption"    column="rule_option"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="ruleIdsDisplay"    column="rule_ids_display"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="keyWordId"    column="key_word_id"    />
        <result property="keyWord"    column="key_word"    />
        <result property="grossScore"    column="gross_score"    />
        <result property="plusDeductScore"    column="plus_deduct_score"    />
        <result property="scoringMechanism"    column="scoring_mechanism"    />
        <result property="sort"    column="sort"    />
        <result property="createBy"    column="create_by"    />
        <result property="createById"    column="create_by_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateById"    column="update_by_id"    />
        <result property="updateTime"    column="update_time"    />
        <result property="appearNumber"    column="appear_number"    />
    </resultMap>

    <sql id="selectCallcenterQualityScoringRuleDetailVo">
        select id, scoring_rule_id, rule_option, rule_id, rule_ids_display, rule_name, key_word_id, key_word, gross_score, plus_deduct_score, scoring_mechanism, sort, create_by, create_by_id, create_time, update_by, update_by_id, update_time, appear_number, one_vote from callcenter_quality_scoring_rule_detail
    </sql>

    <select id="selectCallcenterQualityScoringRuleDetailList" parameterType="com.gobon.project.intelligencequality.domain.param.ScoringRuleParam" resultMap="CallcenterQualityScoringRuleDetailResult">
        <include refid="selectCallcenterQualityScoringRuleDetailVo"/>
        <where>
            1 = 1
            <if test="id != null "> and scoring_rule_id = #{id}</if>
            <if test="ruleOption != null  and ruleOption != ''"> and rule_option = #{ruleOption}</if>
             and addr_area = #{addrArea}
        </where>
    </select>

    <select id="selectCallcenterQualityScoringRuleDetailById" parameterType="Long" resultMap="CallcenterQualityScoringRuleDetailResult">
        <include refid="selectCallcenterQualityScoringRuleDetailVo"/>
        where id = #{id} <!--and addr_area = #{addrArea}-->
    </select>

    <insert id="insertCallcenterQualityScoringRuleDetail" parameterType="com.gobon.project.intelligencequality.domain.CallcenterQualityScoringRuleDetail">
        insert into callcenter_quality_scoring_rule_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">id,</if>
            <if test="scoringRuleId != null ">scoring_rule_id,</if>
            <if test="ruleOption != null  and ruleOption != ''">rule_option,</if>
            <if test="ruleId != null ">rule_id,</if>
            <if test="ruleIdsDisplay != null  and ruleIdsDisplay != ''">rule_ids_display,</if>
            <if test="ruleName != null  and ruleName != ''">rule_name,</if>
            <if test="keyWordId != null ">key_word_id,</if>
            <if test="keyWord != null  and keyWord != ''">key_word,</if>
            <if test="grossScore != null ">gross_score,</if>
            <if test="plusDeductScore != null ">plus_deduct_score,</if>
            <if test="scoringMechanism != null  and scoringMechanism != ''">scoring_mechanism,</if>
            <if test="sort != null ">sort,</if>
            <if test="createBy != null  and createBy != ''">create_by,</if>
            <if test="createById != null ">create_by_id,</if>
            <if test="createTime != null ">create_time,</if>
            <if test="updateBy != null  and updateBy != ''">update_by,</if>
            <if test="updateById != null ">update_by_id,</if>
            <if test="updateTime != null ">update_time,</if>
            <if test="appearNumber != null ">appear_number,</if>
            <if test="addrArea != null and addrArea != '' ">addr_area,</if>
            <if test="oneVote != null and oneVote != '' ">one_vote,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">#{id},</if>
            <if test="scoringRuleId != null ">#{scoringRuleId},</if>
            <if test="ruleOption != null  and ruleOption != ''">#{ruleOption},</if>
            <if test="ruleId != null ">#{ruleId},</if>
            <if test="ruleIdsDisplay != null  and ruleIdsDisplay != ''">#{ruleIdsDisplay},</if>
            <if test="ruleName != null  and ruleName != ''">#{ruleName},</if>
            <if test="keyWordId != null ">#{keyWordId},</if>
            <if test="keyWord != null  and keyWord != ''">#{keyWord},</if>
            <if test="grossScore != null ">#{grossScore},</if>
            <if test="plusDeductScore != null ">#{plusDeductScore},</if>
            <if test="scoringMechanism != null  and scoringMechanism != ''">#{scoringMechanism},</if>
            <if test="sort != null ">#{sort},</if>
            <if test="createBy != null  and createBy != ''">#{createBy},</if>
            <if test="createById != null ">#{createById},</if>
            <if test="createTime != null ">#{createTime},</if>
            <if test="updateBy != null  and updateBy != ''">#{updateBy},</if>
            <if test="updateById != null ">#{updateById},</if>
            <if test="updateTime != null ">#{updateTime},</if>
            <if test="appearNumber != null ">#{appearNumber},</if>
            <if test="addrArea != null and addrArea != '' ">#{addrArea},</if>
            <if test="oneVote != null and oneVote != '' ">#{oneVote},</if>
        </trim>

    </insert>

    <update id="updateCallcenterQualityScoringRuleDetail" parameterType="com.gobon.project.intelligencequality.domain.CallcenterQualityScoringRuleDetail">
        update callcenter_quality_scoring_rule_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="scoringRuleId != null ">scoring_rule_id = #{scoringRuleId},</if>
            <if test="ruleOption != null  and ruleOption != ''">rule_option = #{ruleOption},</if>
            <if test="ruleId != null ">rule_id = #{ruleId},</if>
            <if test="ruleIdsDisplay != null  and ruleIdsDisplay != ''">rule_ids_display = #{ruleIdsDisplay},</if>
            <if test="ruleName != null  and ruleName != ''">rule_name = #{ruleName},</if>
            <if test="keyWordId != null ">key_word_id = #{keyWordId},</if>
            <if test="keyWord != null  and keyWord != ''">key_word = #{keyWord},</if>
            <if test="grossScore != null ">gross_score = #{grossScore},</if>
            <if test="plusDeductScore != null ">plus_deduct_score = #{plusDeductScore},</if>
            <if test="scoringMechanism != null  and scoringMechanism != ''">scoring_mechanism = #{scoringMechanism},</if>
            <if test="sort != null ">sort = #{sort},</if>
            <if test="createBy != null  and createBy != ''">create_by = #{createBy},</if>
            <if test="createById != null ">create_by_id = #{createById},</if>
            <if test="createTime != null ">create_time = #{createTime},</if>
            <if test="updateBy != null  and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateById != null ">update_by_id = #{updateById},</if>
            <if test="updateTime != null ">update_time = #{updateTime},</if>
            <if test="appearNumber != null ">appear_number = #{appearNumber},</if>
            <if test="oneVote != null">one_vote = #{oneVote}</if>
        </trim>
        where id = #{id} and addr_area = #{addrArea}
    </update>

    <delete id="deleteCallcenterQualityScoringRuleByScoringRuleId" parameterType="com.gobon.project.intelligencequality.domain.param.ScoringRuleParam">
        delete from callcenter_quality_scoring_rule_detail where scoring_rule_id = #{id} and addr_area = #{addrArea}
    </delete>

    <delete id="deleteCallcenterQualityScoringRuleDetailById" parameterType="com.gobon.project.intelligencequality.domain.param.ScoringRuleParam">
        delete from callcenter_quality_scoring_rule_detail where id = #{detailedId} and addr_area = #{addrArea}
    </delete>

    <delete id="deleteCallcenterQualityScoringRuleDetailByIds" parameterType="com.gobon.project.intelligencequality.domain.param.ScoringRuleParam">
        delete from callcenter_quality_scoring_rule_detail where id in
        <foreach item="item" collection="param.detailedIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        and addr_area = #{param.addrArea}
    </delete>
    
    <select id="selectQualityScoringRule" 
            parameterType="com.gobon.project.intelligencequality.domain.param.ScoringRuleParam"
            resultType="com.gobon.project.intelligencequality.domain.vo.QualityScoringRuleVo">
        select cqsru.id,
               cqsru.rule_name,
               cqsru.scoring_object,
               cqsru.scoring_skill_ids,
               cqsru.scoring_people,
               cqsru.appeal_period_code,
               cqsru.scoring_grade,
               cqsru.scoring_grade_code,
               cqsru.scoring_grade_describe description,
               cqsru.scoring_grade_score,
               cqsru.scoring_grade_score_describe,
               cqsru.gross_score
        from callcenter_quality_scoring_rule cqsru
        where cqsru.id = #{id}
          and cqsru.addr_area = #{addrArea}
          and cqsru.del_flag = '0'
    </select>

    <select id="selectQualityScoringRuleItem"
            parameterType="com.gobon.project.intelligencequality.domain.param.ScoringRuleParam"
            resultType="com.gobon.project.intelligencequality.domain.vo.QualityScoringRuleItemVo">
        select cqsrd.id,
               cqsrd.id                                     scoring_rule_detail_id,
               cqsrd.rule_name                              scoring_rule_detail_name,
               cqsrd.scoring_rule_id,
               cqsrd.rule_option                            rule_type,
               if(cqsrd.rule_option = '1', '关键词规则', '行为规则') type_name,
               cqsrd.key_word_id,
               cqsrd.key_word,
               cqsrd.gross_score,
               cqsrd.plus_deduct_score                      deduct_score,
               cqsrd.plus_deduct_score                      original_deduct_score,
               cqsrd.scoring_mechanism,
               cqsrd.appear_number,
               cqsrd.rule_id                                target_rule_id,
               cqsrd.sort                                   sorted,
               cqsrd.addr_area                              domain,
               cqsrd.one_vote
        from callcenter_quality_scoring_rule_detail cqsrd
        where cqsrd.scoring_rule_id = #{id}
          and cqsrd.addr_area = #{addrArea}
        order by cqsrd.rule_option, cqsrd.sort
    </select>
    
    <select id="selectAllList" parameterType="java.lang.String" 
            resultType="com.gobon.project.intelligencequality.domain.vo.ScoringRuleOptionVO">
        select cqsru.id, cqsru.id value, cqsru.rule_name, cqsru.rule_name label
        from callcenter_quality_scoring_rule cqsru
        where cqsru.del_flag = '0'
          and cqsru.release_state = '1'
          and cqsru.addr_area = #{domain}
    </select>
    <select id="selectById"
            resultType="com.gobon.project.intelligencequality.domain.CallcenterQualityScoringRule">
        select id, rule_name, rule_describe,
               scoring_object, scoring_skill_ids, scoring_skill_name,
               scoring_skill_ids_display, scoring_people, scoring_people_name, appeal_period_code,
               appeal_period, scoring_grade, scoring_grade_code, scoring_grade_describe, scoring_grade_score,
               scoring_grade_score_describe, gross_score, enable_state, release_state, release_time, release_by_id, create_by,
               create_by_id, create_time, update_by_id, update_time, update_by, del_flag, delete_by_id, delete_time, delete_by
        from callcenter_quality_scoring_rule
        where id = #{configId}
    </select>
</mapper>
