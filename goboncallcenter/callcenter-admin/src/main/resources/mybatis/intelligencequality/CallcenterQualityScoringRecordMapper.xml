<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gobon.project.intelligencequality.mapper.CallcenterQualityScoringRecordMapper">

    <resultMap type="CallcenterQualityScoringRecord" id="CallcenterQualityScoringRecordResult">
        <result property="id" column="id"/>
        <result property="qualityInfoId" column="quality_info_id"/>
        <result property="qualityDetailId" column="quality_detail_id"/>
        <result property="operationType" column="operation_type"/>
        <result property="operationDesc" column="operation_desc"/>
        <result property="delFlag" column="del_flag"/>
        <result property="effectiveFlag" column="effective_flag"/>
        <result property="score" column="score"/>
        <result property="qualityResult" column="quality_result"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createById" column="create_by_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="domain" column="domain"/>
        <result property="accept" column="accept"/>
        <result property="resultCode" column="result_code"/>
        <result property="qualityType" column="quality_type"/>
        <result property="refScoringRecordId" column="ref_scoring_record_id"/>
    </resultMap>

    <sql id="selectCallcenterQualityScoringRecordVo">
        select id, quality_info_id, quality_detail_id, domain, operation_type, operation_desc, del_flag, 
        effective_flag, score, quality_result, remark, create_by, create_by_id, create_time, update_by, 
        update_by_id, update_time, accept, result_code, quality_type, ref_scoring_record_id 
        from callcenter_quality_scoring_record
    </sql>

    <select id="selectCallcenterQualityScoringRecordList" parameterType="CallcenterQualityScoringRecord"
            resultMap="CallcenterQualityScoringRecordResult">
        <include refid="selectCallcenterQualityScoringRecordVo"/>
        <where>
            <if test="qualityInfoId != null ">and quality_info_id = #{qualityInfoId}</if>
            <if test="qualityDetailId != null ">and quality_detail_id = #{qualityDetailId}</if>
            <if test="qualityType != null ">and quality_type = #{qualityType}</if>
            <if test="refScoringRecordId != null ">and ref_scoring_record_id = #{refScoringRecordId}</if>
            <if test="domain != null and domain != ''">and domain = #{domain}</if>
            <if test="operationType != null ">and operation_type = #{operationType}</if>
            <if test="operationDesc != null  and operationDesc != ''">and operation_desc = #{operationDesc}</if>
            <if test="effectiveFlag != null ">and effective_flag = #{effectiveFlag}</if>
            <if test="score != null ">and score = #{score}</if>
            <if test="qualityResult != null  and qualityResult != ''">and quality_result = #{qualityResult}</if>
            <if test="createById != null ">and create_by_id = #{createById}</if>
            <if test="updateById != null ">and update_by_id = #{updateById}</if>
            <if test="accept != null ">and accept = #{accept}</if>
            <if test="resultCode != null ">and result_code = #{resultCode}</if>
        </where>
    </select>

    <select id="selectCallcenterQualityScoringRecordById" parameterType="Long"
            resultMap="CallcenterQualityScoringRecordResult">
        <include refid="selectCallcenterQualityScoringRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertCallcenterQualityScoringRecord" parameterType="CallcenterQualityScoringRecord">
        insert into callcenter_quality_scoring_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">id,</if>
            <if test="qualityInfoId != null ">quality_info_id,</if>
            <if test="qualityDetailId != null ">quality_detail_id,</if>
            <if test="qualityType != null ">quality_type,</if>
            <if test="refScoringRecordId != null ">ref_scoring_record_id,</if>
            <if test="domain != null and domain != ''">domain,</if>
            <if test="operationType != null ">operation_type,</if>
            <if test="operationDesc != null  and operationDesc != ''">operation_desc,</if>
            <if test="delFlag != null ">del_flag,</if>
            <if test="effectiveFlag != null ">effective_flag,</if>
            <if test="score != null ">score,</if>
            <if test="qualityResult != null  and qualityResult != ''">quality_result,</if>
            <if test="remark != null  and remark != ''">remark,</if>
            <if test="createBy != null  and createBy != ''">create_by,</if>
            <if test="createById != null ">create_by_id,</if>
            <if test="createTime != null ">create_time,</if>
            <if test="updateBy != null  and updateBy != ''">update_by,</if>
            <if test="updateById != null ">update_by_id,</if>
            <if test="updateTime != null ">update_time,</if>
            <if test="accept != null ">accept,</if>
            <if test="resultCode != null ">result_code,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">#{id},</if>
            <if test="qualityInfoId != null ">#{qualityInfoId},</if>
            <if test="qualityDetailId != null ">#{qualityDetailId},</if>
            <if test="qualityType != null ">#{qualityType},</if>
            <if test="refScoringRecordId != null ">#{refScoringRecordId},</if>
            <if test="domain != null and domain != ''">#{domain},</if>
            <if test="operationType != null ">#{operationType},</if>
            <if test="operationDesc != null  and operationDesc != ''">#{operationDesc},</if>
            <if test="delFlag != null ">#{delFlag},</if>
            <if test="effectiveFlag != null ">#{effectiveFlag},</if>
            <if test="score != null ">#{score},</if>
            <if test="qualityResult != null  and qualityResult != ''">#{qualityResult},</if>
            <if test="remark != null  and remark != ''">#{remark},</if>
            <if test="createBy != null  and createBy != ''">#{createBy},</if>
            <if test="createById != null ">#{createById},</if>
            <if test="createTime != null ">#{createTime},</if>
            <if test="updateBy != null  and updateBy != ''">#{updateBy},</if>
            <if test="updateById != null ">#{updateById},</if>
            <if test="updateTime != null ">#{updateTime},</if>
            <if test="accept != null ">#{accept},</if>
            <if test="resultCode != null ">#{resultCode},</if>
        </trim>
    </insert>

    <update id="updateCallcenterQualityScoringRecord" parameterType="CallcenterQualityScoringRecord">
        update callcenter_quality_scoring_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="qualityInfoId != null ">quality_info_id = #{qualityInfoId},</if>
            <if test="qualityDetailId != null ">quality_detail_id = #{qualityDetailId},</if>
            <if test="qualityType != null ">quality_type = #{qualityType},</if>
            <if test="refScoringRecordId != null ">ref_scoring_record_id = #{refScoringRecordId},</if>
            <if test="domain != null and domain != ''">domain = #{domain},</if>
            <if test="operationType != null ">operation_type = #{operationType},</if>
            <if test="operationDesc != null  and operationDesc != ''">operation_desc = #{operationDesc},</if>
            <if test="delFlag != null ">del_flag = #{delFlag},</if>
            <if test="effectiveFlag != null ">effective_flag = #{effectiveFlag},</if>
            <if test="score != null ">score = #{score},</if>
            <if test="qualityResult != null  and qualityResult != ''">quality_result = #{qualityResult},</if>
            <if test="remark != null  and remark != ''">remark = #{remark},</if>
            <if test="createBy != null  and createBy != ''">create_by = #{createBy},</if>
            <if test="createById != null ">create_by_id = #{createById},</if>
            <if test="createTime != null ">create_time = #{createTime},</if>
            <if test="updateBy != null  and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateById != null ">update_by_id = #{updateById},</if>
            <if test="updateTime != null ">update_time = #{updateTime},</if>
            <if test="accept != null ">accept = #{accept},</if>
            <if test="resultCode != null ">result_code = #{resultCode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCallcenterQualityScoringRecordById" parameterType="Long">
        delete from callcenter_quality_scoring_record where id = #{id}
    </delete>

    <delete id="deleteCallcenterQualityScoringRecordByIds" parameterType="String">
        delete from callcenter_quality_scoring_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectList"
            parameterType="com.gobon.project.intelligencequality.domain.param.QualityScoreResultParam"
            resultType="com.gobon.project.intelligencequality.domain.vo.QualityScoringRecordVo">
        select cqsr.id,
               cqsr.quality_info_id,
               cqsr.quality_detail_id,
               cqsr.operation_type,
               cqsr.operation_desc,
               cqsr.effective_flag,
               cqsr.score,
               cqsr.quality_result,
               cqsr.remark,
               cqsr.create_by,
               cqsr.create_time,
               cqsr.update_by,
               cqsr.update_time,
               cqsr.domain,
               cqsr.accept,
               cqsr.result_code,
               cqsr.quality_type,
               cqsr.ref_scoring_record_id
        from callcenter_quality_scoring_record cqsr
                 inner join callcenter_quality_info cqi on cqsr.quality_info_id = cqi.id
                 inner join callcenter_quality_detail cqd on cqsr.quality_detail_id = cqd.id
        where cqsr.del_flag = 0
          and cqsr.quality_info_id = #{qualityInfoId}
          and cqsr.quality_detail_id = #{qualityDetailId}
            <if test="callDetailId != null">
                and cqi.call_detail_id = #{callDetailId}
            </if>
          <if test="sampleRuleId != null">
              and cqd.sample_rule_id = #{sampleRuleId}
          </if>

          and cqd.scoring_rule_id = #{scoringRuleId}
          and cqsr.operation_type >= 1
         order by cqsr.id desc
    </select>

    <select id="selectById" resultMap="CallcenterQualityScoringRecordResult">
        <include refid="selectCallcenterQualityScoringRecordVo"/>
        where id = #{id}
        <if test="domain != null and domain != ''">
            and domain = #{domain}
        </if>
    </select>

    <!-- 查询草稿/最新复检评分记录 -->
    <select id="selectDraftOrNewest"
            parameterType="com.gobon.project.intelligencequality.domain.param.QualityScoreResultParam"
            resultType="com.gobon.project.intelligencequality.domain.vo.QualityScoringRecordVo">
        select cqsre.id,
        cqsre.quality_info_id,
        cqsre.quality_detail_id,
        cqsre.quality_type,
        cqsre.ref_scoring_record_id,
        cqsre.operation_type,
        cqsre.operation_desc,
        cqsre.score,
        cqsre.quality_result,
        cqsre.remark,
        cqsre.result_code
        from callcenter_quality_scoring_record cqsre
        inner join callcenter_quality_detail cqd on cqsre.quality_info_id = cqd.quality_info_id
        inner join callcenter_quality_info cqi on cqsre.quality_info_id
        and cqsre.quality_detail_id = cqd.id
        where cqsre.create_by_id = #{userId}
        and cqsre.del_flag = 0
        and cqd.status = 2
        and cqsre.create_time >= cqd.last_appeal_time
        <!-- and cqsre.operation_type in (0, 3) -->
        and cqsre.domain = #{domain}
        and cqsre.quality_info_id = #{qualityInfoId}
        and cqsre.quality_detail_id = #{qualityDetailId}
        and cqi.call_detail_id = #{callDetailId}
        <if test="sampleRuleId != null">
            and cqd.sample_rule_id = #{sampleRuleId}
        </if>
        and cqd.scoring_rule_id = #{scoringRuleId}
        order by cqsre.create_time desc
        limit 1
    </select>

    <!-- 查询草稿/最新复检评分记录 -->
    <select id="selectNewestForCurrentUser"
            parameterType="com.gobon.project.intelligencequality.domain.param.QualityScoreResultParam"
            resultType="com.gobon.project.intelligencequality.domain.vo.QualityScoringRecordVo">
        select cqsre.id,
        cqsre.quality_info_id,
        cqsre.quality_detail_id,
        cqsre.quality_type,
        cqsre.ref_scoring_record_id,
        cqsre.operation_type,
        cqsre.operation_desc,
        cqsre.score,
        cqsre.quality_result,
        cqsre.remark,
        cqsre.result_code
        from callcenter_quality_scoring_record cqsre
        inner join callcenter_quality_detail cqd on cqsre.quality_info_id = cqd.quality_info_id
        inner join callcenter_quality_info cqi on cqsre.quality_info_id
        and cqsre.quality_detail_id = cqd.id
        where cqsre.create_by_id = #{userId}
        and cqd.status = 2
        and cqsre.del_flag = 0
        and cqsre.create_time >= cqd.last_appeal_time
        and cqsre.operation_type in (0, 1, 3)
        and cqsre.domain = #{domain}
        and cqsre.quality_info_id = #{qualityInfoId}
        and cqsre.quality_detail_id = #{qualityDetailId}
        and cqi.call_detail_id = #{callDetailId}
        <if test="sampleRuleId != null">
            and cqd.sample_rule_id = #{sampleRuleId}
        </if>
        and cqd.scoring_rule_id = #{scoringRuleId}
        order by cqsre.create_time desc
        limit 1
    </select>

    <!-- 查询复检评分记录 -->
    <select id="selectNewest"
            parameterType="com.gobon.project.intelligencequality.domain.param.QualityScoreResultParam"
            resultType="com.gobon.project.intelligencequality.domain.vo.QualityScoringRecordVo">
        select cqsre.id,
        cqsre.quality_info_id,
        cqsre.quality_detail_id,
        cqsre.quality_type,
        cqsre.ref_scoring_record_id,
        cqsre.operation_type,
        cqsre.operation_desc,
        cqsre.score,
        cqsre.quality_result,
        cqsre.remark,
        cqsre.result_code,
        cqsre.accept,
        cqsre.error_flag
        from callcenter_quality_scoring_record cqsre
        inner join callcenter_quality_detail cqd on cqsre.quality_info_id = cqd.quality_info_id
        inner join callcenter_quality_info cqi on cqsre.quality_info_id
        and cqsre.quality_detail_id = cqd.id
        and cqsre.del_flag = 0
        and (cqsre.operation_type = 3 or (cqsre.operation_type = 4 and cqsre.quality_type = 0))
        and cqsre.domain = #{domain}
        and cqsre.quality_info_id = #{qualityInfoId}
        and cqsre.quality_detail_id = #{qualityDetailId}
        <if test="callDetailId != null">
            and cqi.call_detail_id = #{callDetailId}
        </if>

        <if test="sampleRuleId != null">
            and cqd.sample_rule_id = #{sampleRuleId}
        </if>

        and cqd.scoring_rule_id = #{scoringRuleId}
        order by cqsre.create_time desc
        limit 1
    </select>

    <!-- 检查 -->
    <select id="checkRecordExists"
            parameterType="com.gobon.project.intelligencequality.domain.param.QualityScoreResultParam"
            resultType="java.lang.Integer">
        select count(1)
        from callcenter_quality_scoring_record cqsre
        where cqsre.domain = #{domain} and exists(select 1
                     from callcenter_quality_scoring_record cqsr
                              inner join callcenter_quality_info cqi on cqsr.quality_info_id = cqi.id
                              inner join callcenter_quality_detail cqd on cqsr.quality_detail_id = cqd.id
                     where cqsr.del_flag = 0
                       and cqd.status = 2
                       and cqsre.id = cqsr.id
                       and cqsr.domain = #{domain}
                       and cqsr.create_time >= cqd.last_appeal_time
                       and cqsr.quality_info_id = #{qualityInfoId}
                       and cqsr.quality_detail_id = #{qualityDetailId}
                       and cqi.call_detail_id = #{callDetailId}
                       <if test="sampleRuleId != null">
                           and cqd.sample_rule_id = #{sampleRuleId}
                       </if>
                       and cqd.scoring_rule_id = #{scoringRuleId}
                       and cqsr.operation_type in (1, 3))
    </select>
</mapper>
