<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gobon.project.intelligencequality.mapper.CallcenterQualityInfoMapper">

    <resultMap type="CallcenterQualityInfo" id="CallcenterQualityInfoResult">
        <result property="id" column="id"/>
        <result property="callDetailId" column="call_detail_id"/>
        <result property="manualCount" column="manual_count"/>
        <result property="intelligentCount" column="intelligent_count"/>
        <result property="delFlag" column="del_flag"/>
        <result property="domain" column="domain"/>
    </resultMap>

    <sql id="selectCallcenterQualityInfoVo">
        select id, call_detail_id, domain, manual_count, intelligent_count, del_flag from callcenter_quality_info
    </sql>

    <select id="selectCallcenterQualityInfoList" parameterType="CallcenterQualityInfo"
            resultMap="CallcenterQualityInfoResult">
        <include refid="selectCallcenterQualityInfoVo"/>
        <where>
            <if test="callDetailId != null ">and call_detail_id = #{callDetailId}</if>
            <if test="domain != null and domain != ''">and domain = #{domain}</if>
            <if test="manualCount != null ">and manual_count = #{manualCount}</if>
            <if test="intelligentCount != null ">and intelligent_count = #{intelligentCount}</if>
        </where>
    </select>

    <select id="selectCallcenterQualityInfoById" parameterType="Long" resultMap="CallcenterQualityInfoResult">
        <include refid="selectCallcenterQualityInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertCallcenterQualityInfo" parameterType="CallcenterQualityInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into callcenter_quality_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">id,</if>
            <if test="callDetailId != null ">call_detail_id,</if>
            <if test="domain != null and domain != ''">domain,</if>
            <if test="manualCount != null ">manual_count,</if>
            <if test="intelligentCount != null ">intelligent_count,</if>
            <if test="delFlag != null ">del_flag,</if>
            <if test="netWorkMulId != null and netWorkMulId != ''">net_work_mul_id,</if>
            <if test="platForm != null and platForm != ''">plat_form,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">#{id},</if>
            <if test="callDetailId != null ">#{callDetailId},</if>
            <if test="domain != null and domain != ''">#{domain},</if>
            <if test="manualCount != null ">#{manualCount},</if>
            <if test="intelligentCount != null ">#{intelligentCount},</if>
            <if test="delFlag != null ">#{delFlag},</if>
            <if test="netWorkMulId != null and netWorkMulId != ''">#{netWorkMulId},</if>
            <if test="platForm != null and platForm != ''">#{platForm},</if>
        </trim>
    </insert>

    <update id="updateCallcenterQualityInfo" parameterType="CallcenterQualityInfo">
        update callcenter_quality_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="callDetailId != null ">call_detail_id = #{callDetailId},</if>
            <if test="domain != null and domain != ''">domain = #{domain},</if>
            <if test="manualCount != null ">manual_count = #{manualCount},</if>
            <if test="intelligentCount != null ">intelligent_count = #{intelligentCount},</if>
            <if test="delFlag != null ">del_flag = #{delFlag},</if>
            <if test="netWorkMulId != null and netWorkMulId != ''">net_work_mul_id = #{netWorkMulId},</if>
            <if test="platForm != null and platForm != ''">plat_form = #{platForm},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCallcenterQualityInfoById" parameterType="Long">
        delete from callcenter_quality_info where id = #{id}
    </delete>

    <delete id="deleteCallcenterQualityInfoByIds" parameterType="String">
        delete from callcenter_quality_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 质检列表 -->
    <select id="qualityInfoList" parameterType="com.gobon.project.intelligencequality.domain.param.QualityInfoParam"
            resultType="com.gobon.project.intelligencequality.domain.vo.QualityInfoVo">
        select crd.id,
        crd.record_id,
        crd.record_type call_type,
        crd.call_time,
        crd.start_time,
        crd.out_time end_time,
        crd.conversation_time duration,
        crd.user_name,
        crd.satisf_id satisfice_value,
        crd.statisf_name satisfice_name,
        crd.statisfied_code satisfice_code,
        crd.service_type_id,
        crd.service_type_name,
        cqi.intelligent_count,
        cqi.manual_count
        from callcenter_record_detail crd
        left join callcenter_quality_info cqi on cqi.call_detail_id = crd.id
        where (crd.state = 4 or crd.state is null)
        and crd.answer_state = 1
        and crd.type in (0, 1, 4, 5)
        <if test="quality.callType != null">
            and crd.record_type = #{quality.callType}
        </if>
        <choose>
            <when test="quality.satisficeCode != null and quality.satisficeCode == 'not'">
                and crd.statisfied_code is null
            </when>
            <when test="quality.satisficeCode != null and quality.satisficeCode != ''">
                and crd.statisfied_code = #{quality.satisficeCode}
            </when>
            <otherwise/>
        </choose>
        <choose>
            <when test="quality.intelligentCount != null and quality.intelligentCount > 0">
                and cqi.intelligent_count = #{quality.intelligentCount}
            </when>
            <when test="quality.intelligentCount != null and quality.intelligentCount == 0">
                and cqi.intelligent_count is null
            </when>
            <otherwise/>
        </choose>
        <choose>
            <when test="quality.manualCount != null and quality.manualCount > 0">
                and cqi.manual_count = #{quality.intelligentCount}
            </when>
            <when test="quality.manualCount != null and quality.manualCount == 0">
                and cqi.manual_count is null
            </when>
            <otherwise/>
        </choose>
        <choose>
            <when test="quality.durationStart != null and quality.durationEnd != null">
                and crd.conversation_time between #{quality.durationStart} and #{quality.durationEnd}
            </when>
            <when test="quality.durationStart != null">
                <![CDATA[and crd.conversation_time >= #{quality.durationStart}]]>
            </when>
            <when test="quality.durationEnd != null">
                <![CDATA[and crd.conversation_time <= #{quality.durationEnd}]]>
            </when>
        </choose>
        <choose>
            <when test="quality.startCallTime != null and quality.endCallTime != null">
                and crd.call_time between #{quality.startCallTime} and #{quality.endCallTime}
            </when>
            <when test="quality.startCallTime != null">
                <![CDATA[and crd.call_time >= #{quality.startCallTime}]]>
            </when>
            <when test="quality.endCallTime != null">
                <![CDATA[and crd.call_time <= #{quality.endCallTime}]]>
            </when>
            <otherwise/>
        </choose>
        order by crd.call_time desc
    </select>

    <select id="selectByCallDetailId" resultMap="CallcenterQualityInfoResult">
        select id, call_detail_id,net_work_mul_id, domain, manual_count, intelligent_count from callcenter_quality_info
        where call_detail_id = #{callDetailId}
        <if test="domain != null and domain != ''">
            and domain = #{domain}
        </if>
        limit 1
    </select>

    <!-- 质检列表(客服) -->
    <select id="qualityInfoListForAgent"
            parameterType="com.gobon.project.intelligencequality.domain.param.QualityInfoParam"
            resultType="com.gobon.project.intelligencequality.domain.vo.QualityInfoVo">
        select crd.id,
        crd.record_id,
        crd.record_type call_type,
        crd.call_time,
        crd.start_time,
        crd.out_time end_time,
        crd.conversation_time duration,
        crd.user_name,
        crd.satisf_id satisfice_value,
        crd.statisf_name satisfice_name,
        crd.statisfied_code satisfice_code,
        crd.service_type_id,
        crd.service_type_name,
        cqi.intelligent_count,
        cqi.manual_count
        from callcenter_record_detail crd
        inner join callcenter_quality_info cqi on cqi.call_detail_id = crd.id
        inner join callcenter_quality_detail cqd on cqd.quality_info_id = cqi.id
        where crd.state = 4
        and crd.answer_state = 1
        and crd.type in (0, 1, 2, 4, 5)
        and crd.user_id = #{quality.provider}
        <if test="quality.sampleRuleId != null">
            and cqd.sample_rule_id = #{quality.sampleRuleId}
        </if>
        <if test="quality.scoringRuleId != null">
            and cqd.scoring_rule_id = #{quality.scoringRuleId}
        </if>
        <if test="quality.callType != null">
            and crd.record_type = #{quality.callType}
        </if>
        <choose>
            <when test="quality.satisficeCode != null and quality.satisficeCode == 'not'">
                and crd.statisfied_code is null
            </when>
            <when test="quality.satisficeCode != null and quality.satisficeCode != ''">
                and crd.statisfied_code = #{quality.satisficeCode}
            </when>
            <otherwise/>
        </choose>
        <choose>
            <when test="quality.intelligentCount != null and quality.intelligentCount > 0">
                and cqi.intelligent_count = #{quality.intelligentCount}
            </when>
            <when test="quality.intelligentCount != null and quality.intelligentCount == 0">
                and cqi.intelligent_count is null
            </when>
            <otherwise/>
        </choose>
        <choose>
            <when test="quality.manualCount != null and quality.manualCount > 0">
                and cqi.manual_count = #{quality.intelligentCount}
            </when>
            <when test="quality.manualCount != null and quality.manualCount == 0">
                and cqi.manual_count is null
            </when>
            <otherwise/>
        </choose>
        <choose>
            <when test="quality.durationStart != null and quality.durationEnd != null">
                and crd.conversation_time between #{quality.durationStart} and #{quality.durationEnd}
            </when>
            <when test="quality.durationStart != null">
                <![CDATA[and crd.conversation_time >= #{quality.durationStart}]]>
            </when>
            <when test="quality.durationEnd != null">
                <![CDATA[and crd.conversation_time <= #{quality.durationEnd}]]>
            </when>
        </choose>
        <if test="quality.retestStatus != null">
            <choose>
                <when test="quality.retestStatus == 0">
                    and cqd.retest_count = 0
                </when>
                <otherwise>
                    and cqd.retest_count >= 1
                </otherwise>
            </choose>
        </if>
        <if test="quality.appealStatus != null">
            <choose>
                <when test="quality.appealStatus == 0">
                    and cqd.appeal_count = 0
                </when>
                <otherwise>
                    and cqd.appeal_count >= 1
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="quality.startCallTime != null and quality.endCallTime != null">
                and crd.call_time between #{quality.startCallTime} and #{quality.endCallTime}
            </when>
            <when test="quality.startCallTime != null">
                <![CDATA[and crd.call_time >= #{quality.startCallTime}]]>
            </when>
            <when test="quality.endCallTime != null">
                <![CDATA[and crd.call_time <= #{quality.endCallTime}]]>
            </when>
            <otherwise/>
        </choose>
        AND exists(select 1
           from callcenter_quality_scoring_record cqsr
           where cqsr.quality_info_id = cqi.id and cqsr.quality_detail_id = cqd.id)
        GROUP BY crd.id
        order by crd.call_time desc
    </select>
    
    <select id="selectById" resultMap="CallcenterQualityInfoResult">
        select id, call_detail_id, domain, manual_count, intelligent_count 
        from callcenter_quality_info where id = #{id} 
        <if test="domain != null and domain != ''">
            and domain = #{domain}
        </if>
    </select>
    <select id="selectByNetWorkMulId"
            resultType="com.gobon.project.intelligencequality.domain.CallcenterQualityInfo">
        select id, call_detail_id,net_work_mul_id, domain, manual_count, intelligent_count from callcenter_quality_info
        where net_work_mul_id = #{mulId}
        <if test="domain != null and domain != ''">
            and domain = #{domain}
        </if>
        limit 1
    </select>
    <select id="getByNetWorkMulIds" resultType="java.lang.String">
        select t.net_work_mul_id from callcenter_quality_info t where t.net_work_mul_id in
        <foreach item="item" index="index" collection="netWorkMulIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        and t.plat_form = '2'
    </select>
</mapper>
