<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gobon.project.intelligencequality.mapper.CallcenterQualitySamplingRuleMapper">

    <resultMap type="com.gobon.project.intelligencequality.domain.vo.CallcenterQualitySamplingRuleVO"
               id="CallcenterQualitySamplingRuleResult">
        <result property="id" column="id"/>
        <result property="ruleName" column="rule_name"/>
        <result property="ruleDescribe" column="rule_describe"/>
        <result property="qualityRange" column="quality_range"/>
        <result property="repeatQuality" column="repeat_quality"/>
        <result property="samplingMode" column="sampling_mode"/>
        <result property="samplingModeCode" column="sampling_mode_code"/>
        <result property="frequencyCode" column="frequency_code"/>
        <result property="frequency" column="frequency"/>
        <result property="qualityInspectionQuantity" column="quality_inspection_quantity"/>
        <result property="executionDateDescribe" column="execution_date_describe"/>
        <result property="executionDate" column="execution_date"/>
        <result property="coreExpression" column="core_expression"/>
        <result property="scoringRuleId" column="scoring_rule_id"/>
        <result property="scoringRuleName" column="scoring_rule_name"/>
        <result property="samplingRuleId" column="sampling_rule_id"/>
        <result property="executionState" column="execution_state"/>
        <result property="timedTasksId" column="timed_tasks_id"/>
        <result property="releaseState" column="release_state"/>
        <result property="releaseById" column="release_by_id"/>
        <result property="releaseTime" column="release_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createById" column="create_by_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="deleteById" column="delete_by_id"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="deleteBy" column="delete_by"/>
        <result property="recordStartTime" column="record_start_time"/>
        <result property="recordEndTime" column="record_end_time"/>
    </resultMap>

    <sql id="selectCallcenterQualitySamplingRuleVo">
        select id, rule_name, rule_describe, quality_range, repeat_quality, sampling_mode, sampling_mode_code, frequency_code, frequency, quality_inspection_quantity, execution_date_describe, execution_date, core_expression, scoring_rule_id, scoring_rule_name, sampling_rule_id, execution_state, timed_tasks_id, release_state, release_by_id, release_time, create_by, create_by_id, create_time, update_by_id, update_by, update_time, del_flag, delete_by_id, delete_time, delete_by, record_start_time, record_end_time from callcenter_quality_sampling_rule
    </sql>

    <select id="selectCallcenterQualitySamplingRuleList" parameterType="CallcenterQualitySamplingRule"
            resultMap="CallcenterQualitySamplingRuleResult">
        <include refid="selectCallcenterQualitySamplingRuleVo"/>
        <where>
            1 = 1
            <if test="platForm != null and platForm != ''">and plat_form = #{platForm}</if>
            <if test="ruleName != null  and ruleName != ''">and rule_name like concat('%', #{ruleName}, '%')</if>
            <if test="ruleDescribe != null  and ruleDescribe != ''">and rule_describe = #{ruleDescribe}</if>
            <if test="qualityRange != null ">and quality_range = #{qualityRange}</if>
            <if test="repeatQuality != null  and repeatQuality != ''">and repeat_quality = #{repeatQuality}</if>
            <if test="samplingMode != null  and samplingMode != ''">and sampling_mode = #{samplingMode}</if>
            <if test="samplingModeCode != null  and samplingModeCode != ''">and sampling_mode_code =
                #{samplingModeCode}
            </if>
            <if test="frequencyCode != null  and frequencyCode != ''">and frequency_code = #{frequencyCode}</if>
            <if test="frequency != null  and frequency != ''">and frequency = #{frequency}</if>
            <if test="executionDateDescribe != null  and executionDateDescribe != ''">and execution_date_describe =
                #{executionDateDescribe}
            </if>
            <if test="executionDate != null  and executionDate != ''">and execution_date = #{executionDate}</if>
            <if test="coreExpression != null  and coreExpression != ''">and core_expression = #{coreExpression}</if>
            <if test="scoringRuleId != null ">and scoring_rule_id = #{scoringRuleId}</if>
            <if test="scoringRuleName != null  and scoringRuleName != ''">and scoring_rule_name like concat('%',
                #{scoringRuleName}, '%')
            </if>
            <if test="samplingRuleId != null ">and sampling_rule_id = #{samplingRuleId}</if>
            <if test="executionState != null  and executionState != ''">and execution_state = #{executionState}</if>
            <if test="timedTasksId != null ">and timed_tasks_id = #{timedTasksId}</if>
            <if test="releaseState != null  and releaseState != ''">and release_state = #{releaseState}</if>
            <if test="releaseById != null ">and release_by_id = #{releaseById}</if>
            <if test="releaseTime != null ">and release_time = #{releaseTime}</if>
            <if test="createById != null ">and create_by_id = #{createById}</if>
            <if test="updateById != null ">and update_by_id = #{updateById}</if>
            <if test="deleteById != null ">and delete_by_id = #{deleteById}</if>
            <if test="deleteTime != null ">and delete_time = #{deleteTime}</if>
            <if test="deleteBy != null  and deleteBy != ''">and delete_by = #{deleteBy}</if>
            and addr_area = #{addrArea}
            and del_flag = '0'
            order by execution_state desc, create_time desc
        </where>
    </select>

    <select id="selectCallcenterQualitySamplingRuleById"
            parameterType="com.gobon.project.intelligencequality.domain.param.SamplingRuleParam"
            resultMap="CallcenterQualitySamplingRuleResult">
        <include refid="selectCallcenterQualitySamplingRuleVo"/>
        where id = #{samplingRuleId}
        and addr_area = #{addrArea}
        and del_flag = '0'
    </select>

    <insert id="insertCallcenterQualitySamplingRule" parameterType="CallcenterQualitySamplingRule"
            useGeneratedKeys="true" keyProperty="id">
        insert into callcenter_quality_sampling_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">id,</if>
            <if test="ruleName != null  and ruleName != ''">rule_name,</if>
            <if test="ruleDescribe != null  and ruleDescribe != ''">rule_describe,</if>
            <if test="qualityRange != null ">quality_range,</if>
            <if test="repeatQuality != null  and repeatQuality != ''">repeat_quality,</if>
            <if test="samplingMode != null  and samplingMode != ''">sampling_mode,</if>
            <if test="samplingModeCode != null  and samplingModeCode != ''">sampling_mode_code,</if>
            <if test="frequencyCode != null  and frequencyCode != ''">frequency_code,</if>
            <if test="frequency != null  and frequency != ''">frequency,</if>
            <if test="qualityInspectionQuantity != null  and qualityInspectionQuantity != ''">
                quality_inspection_quantity,
            </if>
            <if test="executionDateDescribe != null  and executionDateDescribe != ''">execution_date_describe,</if>
            <if test="executionDate != null  and executionDate != ''">execution_date,</if>
            <if test="coreExpression != null  and coreExpression != ''">core_expression,</if>
            <if test="scoringRuleId != null ">scoring_rule_id,</if>
            <if test="scoringRuleName != null  and scoringRuleName != ''">scoring_rule_name,</if>
            <if test="samplingRuleId != null ">sampling_rule_id,</if>
            <if test="executionState != null  and executionState != ''">execution_state,</if>
            <if test="timedTasksId != null ">timed_tasks_id,</if>
            <if test="releaseState != null  and releaseState != ''">release_state,</if>
            <if test="releaseById != null ">release_by_id,</if>
            <if test="releaseTime != null ">release_time,</if>
            <if test="createBy != null  and createBy != ''">create_by,</if>
            <if test="createById != null ">create_by_id,</if>
            <if test="createTime != null ">create_time,</if>
            <if test="updateById != null ">update_by_id,</if>
            <if test="updateBy != null  and updateBy != ''">update_by,</if>
            <if test="updateTime != null ">update_time,</if>
            <if test="delFlag != null  and delFlag != ''">del_flag,</if>
            <if test="deleteById != null ">delete_by_id,</if>
            <if test="deleteTime != null ">delete_time,</if>
            <if test="deleteBy != null  and deleteBy != ''">delete_by,</if>
            <if test="addrArea != null  and addrArea != ''">addr_area,</if>
            <if test="recordStartTime != null">record_start_time,</if>
            <if test="recordEndTime != null">record_end_time,</if>
            <if test="platForm != null and platForm != ''">plat_form,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">#{id},</if>
            <if test="ruleName != null  and ruleName != ''">#{ruleName},</if>
            <if test="ruleDescribe != null  and ruleDescribe != ''">#{ruleDescribe},</if>
            <if test="qualityRange != null ">#{qualityRange},</if>
            <if test="repeatQuality != null  and repeatQuality != ''">#{repeatQuality},</if>
            <if test="samplingMode != null  and samplingMode != ''">#{samplingMode},</if>
            <if test="samplingModeCode != null  and samplingModeCode != ''">#{samplingModeCode},</if>
            <if test="frequencyCode != null  and frequencyCode != ''">#{frequencyCode},</if>
            <if test="frequency != null  and frequency != ''">#{frequency},</if>
            <if test="qualityInspectionQuantity != null  and qualityInspectionQuantity != ''">
                #{qualityInspectionQuantity},
            </if>
            <if test="executionDateDescribe != null  and executionDateDescribe != ''">#{executionDateDescribe},</if>
            <if test="executionDate != null  and executionDate != ''">#{executionDate},</if>
            <if test="coreExpression != null  and coreExpression != ''">#{coreExpression},</if>
            <if test="scoringRuleId != null ">#{scoringRuleId},</if>
            <if test="scoringRuleName != null  and scoringRuleName != ''">#{scoringRuleName},</if>
            <if test="samplingRuleId != null ">#{samplingRuleId},</if>
            <if test="executionState != null  and executionState != ''">#{executionState},</if>
            <if test="timedTasksId != null ">#{timedTasksId},</if>
            <if test="releaseState != null  and releaseState != ''">#{releaseState},</if>
            <if test="releaseById != null ">#{releaseById},</if>
            <if test="releaseTime != null ">#{releaseTime},</if>
            <if test="createBy != null  and createBy != ''">#{createBy},</if>
            <if test="createById != null ">#{createById},</if>
            <if test="createTime != null ">#{createTime},</if>
            <if test="updateById != null ">#{updateById},</if>
            <if test="updateBy != null  and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null ">#{updateTime},</if>
            <if test="delFlag != null  and delFlag != ''">0,</if>
            <if test="deleteById != null ">#{deleteById},</if>
            <if test="deleteTime != null ">#{deleteTime},</if>
            <if test="deleteBy != null  and deleteBy != ''">#{deleteBy},</if>
            <if test="addrArea != null  and addrArea != ''">#{addrArea},</if>
            <if test="recordStartTime != null">#{recordStartTime},</if>
            <if test="recordEndTime != null">#{recordEndTime},</if>
            <if test="platForm != null and platForm != ''">#{platForm},</if>
        </trim>
    </insert>

    <update id="updateCallcenterQualitySamplingRule" parameterType="CallcenterQualitySamplingRule">
        update callcenter_quality_sampling_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleName != null  and ruleName != ''">rule_name = #{ruleName},</if>
            <if test="ruleDescribe != null  and ruleDescribe != ''">rule_describe = #{ruleDescribe},</if>
            <if test="qualityRange != null ">quality_range = #{qualityRange},</if>
            <if test="repeatQuality != null  and repeatQuality != ''">repeat_quality = #{repeatQuality},</if>
            <if test="samplingMode != null  and samplingMode != ''">sampling_mode = #{samplingMode},</if>
            <if test="samplingModeCode != null  and samplingModeCode != ''">sampling_mode_code = #{samplingModeCode},
            </if>
            <if test="frequencyCode != null  and frequencyCode != ''">frequency_code = #{frequencyCode},</if>
            <if test="frequency != null  and frequency != ''">frequency = #{frequency},</if>
            <if test="qualityInspectionQuantity != null  and qualityInspectionQuantity != ''">
                quality_inspection_quantity = #{qualityInspectionQuantity},
            </if>
            <if test="executionDateDescribe != null  and executionDateDescribe != ''">execution_date_describe =
                #{executionDateDescribe},
            </if>
            <if test="executionDate != null  and executionDate != ''">execution_date = #{executionDate},</if>
            <if test="coreExpression != null  and coreExpression != ''">core_expression = #{coreExpression},</if>
            <if test="scoringRuleId != null ">scoring_rule_id = #{scoringRuleId},</if>
            <if test="scoringRuleName != null  and scoringRuleName != ''">scoring_rule_name = #{scoringRuleName},</if>
            <if test="samplingRuleId != null ">sampling_rule_id = #{samplingRuleId},</if>
            <if test="executionState != null  and executionState != ''">execution_state = #{executionState},</if>
            <if test="timedTasksId != null ">timed_tasks_id = #{timedTasksId},</if>
            <if test="releaseState != null  and releaseState != ''">release_state = #{releaseState},</if>
            <if test="releaseById != null ">release_by_id = #{releaseById},</if>
            <if test="releaseTime != null ">release_time = #{releaseTime},</if>
            <if test="createBy != null  and createBy != ''">create_by = #{createBy},</if>
            <if test="createById != null ">create_by_id = #{createById},</if>
            <if test="createTime != null ">create_time = #{createTime},</if>
            <if test="updateById != null ">update_by_id = #{updateById},</if>
            <if test="updateBy != null  and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null ">update_time = #{updateTime},</if>
            <if test="delFlag != null  and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="deleteById != null ">delete_by_id = #{deleteById},</if>
            <if test="deleteTime != null ">delete_time = #{deleteTime},</if>
            <if test="deleteBy != null  and deleteBy != ''">delete_by = #{deleteBy},</if>
            <if test="recordStartTime != null">record_start_time = #{recordStartTime},</if>
            <if test="recordEndTime != null">record_end_time = #{recordEndTime},</if>
        </trim>
        where id = #{id}
        and addr_area = #{addrArea}
    </update>

    <delete id="deleteCallcenterQualitySamplingRuleById" parameterType="Long">
        delete from callcenter_quality_sampling_rule where id = #{id} and addr_area = #{addrArea}
    </delete>

    <delete id="deleteCallcenterQualitySamplingRuleByIds" parameterType="String">
        delete from callcenter_quality_sampling_rule where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
        and addr_area = #{addrArea}
    </delete>


    <sql id="selectCallcenterQualitySamplingRuleConfigureVo">
        select id, sampling_rule_id, configuration_item_code, configuration_item_describe, configuration_item_key, configuration_item_type, other_option_code, other_option_describe, other_option_key, sort from callcenter_quality_sampling_rule_configure
    </sql>

    <select id="selectCallcenterQualitySamplingRuleConfigureList"
            parameterType="com.gobon.project.intelligencequality.domain.param.SamplingRuleParam"
            resultType="com.gobon.project.intelligencequality.domain.vo.CallcenterQualitySamplingRuleConfigureVO">
        <include refid="selectCallcenterQualitySamplingRuleConfigureVo"/>
        <where>
            1 = 1
            <if test="samplingRuleId != null ">and sampling_rule_id = #{samplingRuleId}</if>
            and addr_area = #{addrArea}
        </where>
    </select>

    <select id="selectCallcenterQualitySamplingRuleConfigureById" parameterType="Long"
            resultType="com.gobon.project.intelligencequality.domain.vo.CallcenterQualitySamplingRuleConfigureVO">
        <include refid="selectCallcenterQualitySamplingRuleConfigureVo"/>
        where id = #{id}
        and addr_area = #{addrArea}
    </select>

    <insert id="insertCallcenterQualitySamplingRuleConfigure"
            parameterType="com.gobon.project.intelligencequality.domain.CallcenterQualitySamplingRuleConfigure">
        insert into callcenter_quality_sampling_rule_configure
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">id,</if>
            <if test="samplingRuleId != null ">sampling_rule_id,</if>
            <if test="configurationItemCode != null  and configurationItemCode != ''">configuration_item_code,</if>
            <if test="configurationItemDescribe != null  and configurationItemDescribe != ''">
                configuration_item_describe,
            </if>
            <if test="configurationItemKey != null  and configurationItemKey != ''">configuration_item_key,</if>
            <if test="configurationItemType != null  and configurationItemType != ''">configuration_item_type,</if>
            <if test="otherOptionCode != null  and otherOptionCode != ''">other_option_code,</if>
            <if test="otherOptionDescribe != null  and otherOptionDescribe != ''">other_option_describe,</if>
            <if test="otherOptionKey != null  and otherOptionKey != ''">other_option_key,</if>
            <if test="sort != null ">sort,</if>
            <if test="addrArea != null  and addrArea != ''">addr_area,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">#{id},</if>
            <if test="samplingRuleId != null ">#{samplingRuleId},</if>
            <if test="configurationItemCode != null  and configurationItemCode != ''">#{configurationItemCode},</if>
            <if test="configurationItemDescribe != null  and configurationItemDescribe != ''">
                #{configurationItemDescribe},
            </if>
            <if test="configurationItemKey != null  and configurationItemKey != ''">#{configurationItemKey},</if>
            <if test="configurationItemType != null  and configurationItemType != ''">#{configurationItemType},</if>
            <if test="otherOptionCode != null  and otherOptionCode != ''">#{otherOptionCode},</if>
            <if test="otherOptionDescribe != null  and otherOptionDescribe != ''">#{otherOptionDescribe},</if>
            <if test="otherOptionKey != null  and otherOptionKey != ''">#{otherOptionKey},</if>
            <if test="sort != null ">#{sort},</if>
            <if test="addrArea != null  and addrArea != ''">#{addrArea},</if>
        </trim>
    </insert>

    <update id="updateCallcenterQualitySamplingRuleConfigure"
            parameterType="com.gobon.project.intelligencequality.domain.CallcenterQualitySamplingRuleConfigure">
        update callcenter_quality_sampling_rule_configure
        <trim prefix="SET" suffixOverrides=",">
            <if test="samplingRuleId != null ">sampling_rule_id = #{samplingRuleId},</if>
            <if test="configurationItemCode != null  and configurationItemCode != ''">configuration_item_code =
                #{configurationItemCode},
            </if>
            <if test="configurationItemDescribe != null  and configurationItemDescribe != ''">
                configuration_item_describe = #{configurationItemDescribe},
            </if>
            <if test="configurationItemKey != null  and configurationItemKey != ''">configuration_item_key =
                #{configurationItemKey},
            </if>
            <if test="configurationItemType != null  and configurationItemType != ''">configuration_item_type =
                #{configurationItemType},
            </if>
            <if test="otherOptionCode != null  and otherOptionCode != ''">other_option_code = #{otherOptionCode},</if>
            <if test="otherOptionDescribe != null  and otherOptionDescribe != ''">other_option_describe =
                #{otherOptionDescribe},
            </if>
            <if test="otherOptionKey != null  and otherOptionKey != ''">other_option_key = #{otherOptionKey},</if>
            <if test="sort != null ">sort = #{sort},</if>
        </trim>
        where id = #{id}
        and addr_area = #{addrArea}
    </update>

    <delete id="deleteConfigure" parameterType="com.gobon.project.intelligencequality.domain.param.SamplingRuleParam">
        delete from
        callcenter_quality_sampling_rule_configure
        where
        1 = 1
        <if test="configureId != null ">and id = #{configureId}</if>
        <if test="samplingRuleId != null ">and sampling_rule_id = #{samplingRuleId}</if>
        and addr_area = #{addrArea}
    </delete>

    <select id="selectQualitySamplingRuleById"
            parameterType="com.gobon.project.intelligencequality.domain.param.ScoringRuleParam"
            resultType="com.gobon.project.intelligencequality.domain.CallcenterQualitySamplingRule">
        <include refid="selectCallcenterQualitySamplingRuleVo"/>
        where id = #{samplingRuleId}
        and addr_area = #{addrArea}
        and del_flag = '0'
    </select>
    
    <select id="selectQualitySamplingCode" resultType="String">
        select configuration_item_code
        from callcenter_quality_sampling_rule_configure
        where sampling_rule_id = #{id}
          and configuration_item_key = #{mode}
          and addr_area = #{domain}
    </select>
    
    <select id="selectAllList" parameterType="java.lang.String" 
            resultType="com.gobon.project.intelligencequality.domain.vo.QualitySamplingRuleVo">
        select cqsr.id, cqsr.rule_name
        from callcenter_quality_sampling_rule cqsr
        where cqsr.addr_area = #{domain}
          and cqsr.release_state = '1' and cqsr.del_flag = '0'
    </select>
    <select id="selectCountByScoringId" resultType="java.lang.Integer">
        select count(1) from callcenter_quality_sampling_rule t where t.scoring_rule_id = #{scoringId} and t.del_flag = '0'
    </select>

    <update id="updateQualityInfoData">
        update callcenter_quality_info
        set del_flag = 0
        WHERE del_flag = 1 and call_detail_id IN (SELECT id
                                 FROM callcenter_record_detail
                                 WHERE bill_number IN (
                                                       'IN20230813067477883975-1',
                                                       'IN20230813071159795116-1',
                                                       'IN20230813074797811771-1',
                                                       'IN20230813077898402704-1',
                                                       'IN20230813075770759054-2',
                                                       'IN20230813113393380383-1',
                                                       'IN20230813116858509180-1',
                                                       'IN20230813120028562122-1',
                                                       'IN20230813139736306251-1',
                                                       'IN20230813150773037053-1',
                                                       'IN20230813179807177537-1',
                                                       'IN20230813184000667723-1',
                                                       'IN20230813189874857410-1',
                                                       'IN20230813195328773491-1',
                                                       'IN20230813199833066637-1',
                                                       'IN20230813208440173704-1',
                                                       'IN20230813210531686708-1',
                                                       'IN20230813214808476107-1',
                                                       'IN20230813219382290390-1',
                                                       'IN20230813224492955312-1'
                                     ))
        ORDER BY RAND()
        LIMIT ${quantity}
    </update>

    <update id="updateQualityInfoDataByStatisf">
        update callcenter_quality_info
        set del_flag = 0
        WHERE del_flag = 1 and call_detail_id IN (SELECT id
        FROM callcenter_record_detail
        WHERE bill_number IN (
        'IN20230813067477883975-1',
        'IN20230813071159795116-1',
        'IN20230813074797811771-1',
        'IN20230813077898402704-1',
        'IN20230813075770759054-2',
        'IN20230813113393380383-1',
        'IN20230813116858509180-1',
        'IN20230813120028562122-1',
        'IN20230813139736306251-1',
        'IN20230813150773037053-1',
        'IN20230813179807177537-1',
        'IN20230813184000667723-1',
        'IN20230813189874857410-1',
        'IN20230813195328773491-1',
        'IN20230813199833066637-1',
        'IN20230813208440173704-1',
        'IN20230813210531686708-1',
        'IN20230813214808476107-1',
        'IN20230813219382290390-1',
        'IN20230813224492955312-1'
        )
        and statisf_name in
        <foreach collection="statisf" index="index" item="item" open=" (" separator="," close=")">
            #{item}
        </foreach>
        )
    </update>
    <update id="updateReleaseState">
        update callcenter_quality_sampling_rule set release_state = #{releaseState},update_by = #{updateBy},update_by_id = #{updateById},update_time = #{updateTime}
        where id = #{samplingRuleId}
    </update>
</mapper>
