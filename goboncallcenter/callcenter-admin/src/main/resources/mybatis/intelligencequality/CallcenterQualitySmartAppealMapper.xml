<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gobon.project.intelligencequality.mapper.CallcenterQualitySmartAppealMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gobon.project.intelligencequality.domain.CallcenterQualitySmartAppeal">
        <id column="id" property="id" />
        <result column="scoring_record_id" property="scoringRecordId" />
        <result column="user_id" property="userId" />
        <result column="appeal_content" property="appealContent" />
        <result column="appeal_reply" property="appealReply" />
        <result column="appeal_result" property="appealResult" />
        <result column="appeal_reply_score" property="appealReplyScore" />
        <result column="appeal_reply_user_id" property="appealReplyUserId" />
        <result column="appeal_reply_user_name" property="appealReplyUserName" />
        <result column="appeal_reply_time" property="appealReplyTime" />
        <result column="create_time" property="createTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>
    <select id="getAppealList"
            resultType="com.gobon.project.intelligencequality.domain.vo.CallcenterQualitySmartAppealVO">
        select * from callcenter_quality_smart_appeal where scoring_record_id = #{scoringRecordId} order by create_time
    </select>

</mapper>
