<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gobon.project.intelligencequality.mapper.CallcenterQualityBehaviorRuleMapper">

    <resultMap type="com.gobon.project.intelligencequality.domain.vo.CallcenterQualityBehaviorRuleVO" id="CallcenterQualityBehaviorRuleResult">
        <result property="id"    column="id"    />
        <result property="ruleType"    column="rule_type"    />
        <result property="sort"    column="sort"    />
        <result property="ruleDescribe"    column="rule_describe"    />
        <result property="ruleDescribeValue"    column="rule_describe_value"    />
        <result property="ruleDescribeKey"    column="rule_describe_key"    />
        <result property="ruleDetailTypeCode"    column="rule_detail_type_code"    />
        <result property="ruleDetailType"    column="rule_detail_type"    />
        <result property="enableState"    column="enable_state"    />
        <result property="createById"    column="create_by_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateById"    column="update_by_id"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectCallcenterQualityBehaviorRuleVo">
        select
            id, rule_type, sort, rule_describe,
            rule_describe_value, rule_describe_key,
            rule_detail_type_code, rule_detail_type, enable_state, create_by_id, create_by,
            create_time, update_by_id, update_by, update_time, del_flag
        from
            callcenter_quality_behavior_rule
    </sql>

    <select id="selectCallcenterQualityBehaviorRuleList" parameterType="com.gobon.project.intelligencequality.domain.CallcenterQualityBehaviorRule" resultType="com.gobon.project.intelligencequality.domain.vo.CallcenterQualityBehaviorRuleVO">
        <include refid="selectCallcenterQualityBehaviorRuleVo"/>
        <where>
            1 = 1
            <if test="ruleType != null  and ruleType != ''"> and rule_type = #{ruleType}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="ruleDescribe != null  and ruleDescribe != ''"> and rule_describe = #{ruleDescribe}</if>
            <if test="ruleDescribeValue != null  and ruleDescribeValue != ''"> and rule_describe_value = #{ruleDescribeValue}</if>
            <if test="ruleDescribeKey != null  and ruleDescribeKey != ''"> and rule_describe_key = #{ruleDescribeKey}</if>
            <if test="ruleDetailTypeCode != null  and ruleDetailTypeCode != ''"> and rule_detail_type_code = #{ruleDetailTypeCode}</if>
            <if test="ruleDetailType != null  and ruleDetailType != ''"> and rule_detail_type = #{ruleDetailType}</if>
            <if test="enableState != null  and enableState != ''"> and enable_state = #{enableState}</if>
            <if test="createById != null "> and create_by_id = #{createById}</if>
            <if test="updateById != null  and updateById != ''"> and update_by_id = #{updateById}</if>
            <if test="addrArea != null  and addrArea != ''"> and addr_area = #{addrArea}</if>
            <if test="platForm != null and platForm != ''">and plat_form = #{platForm}</if>
            and del_flag = 0
        </where>

        order by sort asc

    </select>

    <select id="getBehaviorRuleType" parameterType="com.gobon.project.intelligencequality.domain.param.BehaviorRuleParam" resultType="com.gobon.project.intelligencequality.domain.vo.BehaviorRuleTypeVO">
        select id, rule_type from callcenter_quality_behavior_rule
        where
        enable_state = 1
        and del_flag = 0
        and addr_area = #{addrArea}
    </select>
    
    <select id="selectCallcenterQualityBehaviorRuleById" parameterType="Long" resultMap="CallcenterQualityBehaviorRuleResult">
        <include refid="selectCallcenterQualityBehaviorRuleVo"/>
        where id = #{id}
        and addr_area = #{addrArea}
    </select>
        
    <insert id="insertCallcenterQualityBehaviorRule" parameterType="com.gobon.project.intelligencequality.domain.CallcenterQualityBehaviorRule">
        insert into callcenter_quality_behavior_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">id,</if>
            <if test="ruleType != null  and ruleType != ''">rule_type,</if>
            <if test="sort != null ">sort,</if>
            <if test="ruleDescribe != null  and ruleDescribe != ''">rule_describe,</if>
            <if test="ruleDescribeValue != null  and ruleDescribeValue != ''">rule_describe_value,</if>
            <if test="ruleDescribeKey != null  and ruleDescribeKey != ''">rule_describe_key,</if>
            <if test="ruleDetailTypeCode != null  and ruleDetailTypeCode != ''">rule_detail_type_code,</if>
            <if test="ruleDetailType != null  and ruleDetailType != ''">rule_detail_type,</if>
            <if test="enableState != null  and enableState != ''">enable_state,</if>
            <if test="createById != null ">create_by_id,</if>
            <if test="createBy != null  and createBy != ''">create_by,</if>
            <if test="createTime != null ">create_time,</if>
            <if test="updateById != null  and updateById != ''">update_by_id,</if>
            <if test="updateBy != null  and updateBy != ''">update_by,</if>
            <if test="updateTime != null ">update_time,</if>
            <if test="delFlag != null  and delFlag != ''">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">#{id},</if>
            <if test="ruleType != null  and ruleType != ''">#{ruleType},</if>
            <if test="sort != null ">#{sort},</if>
            <if test="ruleDescribe != null  and ruleDescribe != ''">#{ruleDescribe},</if>
            <if test="ruleDescribeValue != null  and ruleDescribeValue != ''">#{ruleDescribeValue},</if>
            <if test="ruleDescribeKey != null  and ruleDescribeKey != ''">#{ruleDescribeKey},</if>
            <if test="ruleDetailTypeCode != null  and ruleDetailTypeCode != ''">#{ruleDetailTypeCode},</if>
            <if test="ruleDetailType != null  and ruleDetailType != ''">#{ruleDetailType},</if>
            <if test="enableState != null  and enableState != ''">#{enableState},</if>
            <if test="createById != null ">#{createById},</if>
            <if test="createBy != null  and createBy != ''">#{createBy},</if>
            <if test="createTime != null ">#{createTime},</if>
            <if test="updateById != null  and updateById != ''">#{updateById},</if>
            <if test="updateBy != null  and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null ">#{updateTime},</if>
            <if test="delFlag != null  and delFlag != ''">#{delFlag},</if>
        </trim>
    </insert>

    <update id="updateCallcenterQualityBehaviorRule" parameterType="com.gobon.project.intelligencequality.domain.CallcenterQualityBehaviorRule">
        update callcenter_quality_behavior_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleType != null  and ruleType != ''">rule_type = #{ruleType},</if>
            <if test="sort != null ">sort = #{sort},</if>
            <if test="ruleDescribe != null  and ruleDescribe != ''">rule_describe = #{ruleDescribe},</if>
            <if test="ruleDescribeValue != null  and ruleDescribeValue != ''">rule_describe_value = #{ruleDescribeValue},</if>
            <if test="ruleDescribeKey != null  and ruleDescribeKey != ''">rule_describe_key = #{ruleDescribeKey},</if>
            <if test="ruleDetailTypeCode != null  and ruleDetailTypeCode != ''">rule_detail_type_code = #{ruleDetailTypeCode},</if>
            <if test="ruleDetailType != null  and ruleDetailType != ''">rule_detail_type = #{ruleDetailType},</if>
            <if test="enableState != null  and enableState != ''">enable_state = #{enableState},</if>
            <if test="createById != null ">create_by_id = #{createById},</if>
            <if test="createBy != null  and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null ">create_time = #{createTime},</if>
            <if test="updateById != null  and updateById != ''">update_by_id = #{updateById},</if>
            <if test="updateBy != null  and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null ">update_time = #{updateTime},</if>
            <if test="delFlag != null  and delFlag != ''">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
        and addr_area = #{addrArea}
    </update>






    <select id="selectCallcenterQualityBehaviorRuleDetailList" parameterType="com.gobon.project.intelligencequality.domain.param.BehaviorRuleParam" resultType="com.gobon.project.intelligencequality.domain.vo.CallcenterQualityBehaviorRuleDetailVO">
        SELECT
            id,
            behavior_rule_id,
            sort,
            enable_state,
            classify_sign,
            classify_name,
            rule_sentence,
            similitude,
            similitude_number,
            create_by_id,
            create_by,
            create_time,
            update_by,
            update_by_id,
            update_time
        FROM
            callcenter_quality_behavior_rule_detail
        where
            behavior_rule_id = #{id}
            and addr_area = #{addrArea}
    </select>

    <insert id="insertCallcenterQualityBehaviorRuleDetail" parameterType="com.gobon.project.intelligencequality.domain.CallcenterQualityBehaviorRuleDetail">
        insert into callcenter_quality_behavior_rule_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">id,</if>
            <if test="behaviorRuleId != null ">behavior_rule_id,</if>
            <if test="sort != null ">sort,</if>
            <if test="enableState != null  and enableState != ''">enable_state,</if>
            <if test="classifySign != null  and classifySign != ''">classify_sign,</if>
            <if test="classifyName != null  and classifyName != ''">classify_name,</if>
            <if test="ruleSentence != null  and ruleSentence != ''">rule_sentence,</if>
            <if test="similitude != null  and similitude != ''">similitude,</if>
            <if test="similitudeNumber != null ">similitude_number,</if>
            <if test="createById != null ">create_by_id,</if>
            <if test="createBy != null  and createBy != ''">create_by,</if>
            <if test="createTime != null ">create_time,</if>
            <if test="updateBy != null  and updateBy != ''">update_by,</if>
            <if test="updateById != null ">update_by_id,</if>
            <if test="updateTime != null ">update_time,</if>
            <if test="addrArea != null and addrArea != '' ">addr_area,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">#{id},</if>
            <if test="behaviorRuleId != null ">#{behaviorRuleId},</if>
            <if test="sort != null ">#{sort},</if>
            <if test="enableState != null  and enableState != ''">#{enableState},</if>
            <if test="classifySign != null  and classifySign != ''">#{classifySign},</if>
            <if test="classifyName != null  and classifyName != ''">#{classifyName},</if>
            <if test="ruleSentence != null  and ruleSentence != ''">#{ruleSentence},</if>
            <if test="similitude != null  and similitude != ''">#{similitude},</if>
            <if test="similitudeNumber != null ">#{similitudeNumber},</if>
            <if test="createById != null ">#{createById},</if>
            <if test="createBy != null  and createBy != ''">#{createBy},</if>
            <if test="createTime != null ">#{createTime},</if>
            <if test="updateBy != null  and updateBy != ''">#{updateBy},</if>
            <if test="updateById != null ">#{updateById},</if>
            <if test="updateTime != null ">#{updateTime},</if>
            <if test="addrArea != null and addrArea != '' ">#{addrArea},</if>
        </trim>
    </insert>

    <update id="updateCallcenterQualityBehaviorRuleDetail" parameterType="com.gobon.project.intelligencequality.domain.CallcenterQualityBehaviorRuleDetail">
        update callcenter_quality_behavior_rule_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="behaviorRuleId != null ">behavior_rule_id = #{behaviorRuleId},</if>
            <if test="sort != null ">sort = #{sort},</if>
            <if test="enableState != null  and enableState != ''">enable_state = #{enableState},</if>
            <if test="classifySign != null  and classifySign != ''">classify_sign = #{classifySign},</if>
            <if test="classifyName != null  and classifyName != ''">classify_name = #{classifyName},</if>
            <if test="ruleSentence != null  and ruleSentence != ''">rule_sentence = #{ruleSentence},</if>

            <if test="similitude != null  and similitude != ''">similitude = #{similitude},</if>
            <if test='similitude == null  or similitude == ""'>similitude = null,</if>

            <if test="similitudeNumber != null ">similitude_number = #{similitudeNumber},</if>
            <if test='similitude == null  or similitude == ""'>similitude_number = 0,</if>

            <if test="createById != null ">create_by_id = #{createById},</if>
            <if test="createBy != null  and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null ">create_time = #{createTime},</if>
            <if test="updateBy != null  and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateById != null ">update_by_id = #{updateById},</if>
            <if test="updateTime != null ">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
        and addr_area = #{addrArea}
    </update>

    <delete id="deleteCallcenterQualityBehaviorRuleDetailById"  parameterType="com.gobon.project.intelligencequality.domain.param.BehaviorRuleParam">
        delete from callcenter_quality_behavior_rule_detail
        where
        id = #{detailedId}
        and addr_area = #{addrArea}
    </delete>
    
    
    <select id="selectBehaviorRule" resultType="com.gobon.project.intelligencequality.domain.vo.QualityBehaviorRuleVo">
        select cqbr.id, cqbr.rule_describe_value rule_value
        from callcenter_quality_behavior_rule cqbr
        where cqbr.id = #{id} and cqbr.addr_area = #{domain} and cqbr.enable_state = '1'
    </select>
    
    <select id="selectBehaviorRuleItem" 
            resultType="com.gobon.project.intelligencequality.domain.vo.QualityBehaviorRuleItemVo">
        select cqbrd.id, cqbrd.behavior_rule_id, cqbrd.rule_sentence, cqbrd.similitude
        from callcenter_quality_behavior_rule_detail cqbrd
        where cqbrd.behavior_rule_id = #{id}
          and cqbrd.addr_area = #{domain}
    </select>
</mapper>
