<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gobon.project.intelligencequality.mapper.CallcenterQualityMarkDetailMapper">

    <resultMap type="CallcenterQualityMarkDetail" id="CallcenterQualityMarkDetailResult">
        <result property="id" column="id"/>
        <result property="qualityInfoId" column="quality_info_id"/>
        <result property="qualityDetailId" column="quality_detail_id"/>
        <result property="scoringRecordId" column="scoring_record_id"/>
        <result property="scoringRuleId" column="scoring_rule_id"/>
        <result property="scoringRuleName" column="scoring_rule_name"/>
        <result property="scoringRuleDetailId" column="scoring_rule_detail_id"/>
        <result property="scoringRuleDetailName" column="scoring_rule_detail_name"/>
        <result property="recordingPosition" column="recording_position"/>
        <result property="deductScore" column="deduct_score"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="createById" column="create_by_id"/>
        <result property="domain" column="domain"/>
        <result property="ruleType" column="rule_type"/>
        <result property="typeName" column="typeName"/>
        <result property="keyWord" column="key_word"/>
        <result property="callDetailId" column="call_detail_id"/>
        <result property="soundName" column="sound_name"/>
        <result property="originalCallDetailId" column="original_call_detail_id"/>
    </resultMap>

    <sql id="selectCallcenterQualityMarkDetailVo">
        select id, quality_info_id, quality_detail_id, domain, scoring_record_id, 
        scoring_rule_id, scoring_rule_name, scoring_rule_detail_id, scoring_rule_detail_name, 
        recording_position, deduct_score, remark, create_by, create_time, create_by_id, rule_type, 
        type_name, key_word, call_detail_id,sound_name,original_call_detail_id from callcenter_quality_mark_detail
    </sql>

    <select id="selectCallcenterQualityMarkDetailList" parameterType="CallcenterQualityMarkDetail"
            resultMap="CallcenterQualityMarkDetailResult">
        <include refid="selectCallcenterQualityMarkDetailVo"/>
        <where>
            <if test="qualityInfoId != null ">and quality_info_id = #{qualityInfoId}</if>
            <if test="qualityDetailId != null ">and quality_detail_id = #{qualityDetailId}</if>
            <if test="callDetailId != null ">and call_detail_id = #{callDetailId}</if>
            <if test="domain != null and domain != ''">and domain = #{domain}</if>
            <if test="scoringRecordId != null ">and scoring_record_id = #{scoringRecordId}</if>
            <if test="scoringRuleId != null ">and scoring_rule_id = #{scoringRuleId}</if>
            <if test="scoringRuleName != null  and scoringRuleName != ''">and scoring_rule_name like concat('%',
                #{scoringRuleName}, '%')
            </if>
            <if test="scoringRuleDetailId != null ">and scoring_rule_detail_id = #{scoringRuleDetailId}</if>
            <if test="scoringRuleDetailName != null  and scoringRuleDetailName != ''">and scoring_rule_detail_name like
                concat('%', #{scoringRuleDetailName}, '%')
            </if>
            <if test="recordingPosition != null  and recordingPosition != ''">and recording_position =
                #{recordingPosition}
            </if>
            <if test="deductScore != null ">and deduct_score = #{deductScore}</if>
            <if test="createById != null ">and create_by_id = #{createById}</if>
            <if test="soundName != null and soundName != ''">and sound_name = #{soundName}</if>
            <if test="originalCallDetailId != null">and original_call_detail_id = #{originalCallDetailId}</if>
        </where>
    </select>

    <select id="selectCallcenterQualityMarkDetailById" parameterType="Long"
            resultMap="CallcenterQualityMarkDetailResult">
        <include refid="selectCallcenterQualityMarkDetailVo"/>
        where id = #{id}
    </select>

    <insert id="insertCallcenterQualityMarkDetail" parameterType="CallcenterQualityMarkDetail">
        insert into callcenter_quality_mark_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">id,</if>
            <if test="qualityInfoId != null ">quality_info_id,</if>
            <if test="qualityDetailId != null ">quality_detail_id,</if>
            <if test="callDetailId != null ">call_detail_id,</if>
            <if test="domain != null and domain != ''">domain,</if>
            <if test="scoringRecordId != null ">scoring_record_id,</if>
            <if test="scoringRuleId != null ">scoring_rule_id,</if>
            <if test="scoringRuleName != null  and scoringRuleName != ''">scoring_rule_name,</if>
            <if test="scoringRuleDetailId != null ">scoring_rule_detail_id,</if>
            <if test="scoringRuleDetailName != null  and scoringRuleDetailName != ''">scoring_rule_detail_name,</if>
            <if test="recordingPosition != null  and recordingPosition != ''">recording_position,</if>
            <if test="deductScore != null ">deduct_score,</if>
            <if test="remark != null  and remark != ''">remark,</if>
            <if test="createBy != null  and createBy != ''">create_by,</if>
            <if test="createTime != null ">create_time,</if>
            <if test="createById != null ">create_by_id,</if>
            <if test="ruleType != null">rule_type,</if>
            <if test="typeName != null and typeName != ''">type_name,</if>
            <if test="keyWord != null and keyWord != ''">key_word,</if>
            <if test="soundName != null and soundName != ''">sound_name,</if>
            <if test="originalCallDetailId != null">original_call_detail_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">#{id},</if>
            <if test="qualityInfoId != null ">#{qualityInfoId},</if>
            <if test="qualityDetailId != null ">#{qualityDetailId},</if>
            <if test="callDetailId != null ">#{callDetailId},</if>
            <if test="domain != null and domain != ''">#{domain},</if>
            <if test="scoringRecordId != null ">#{scoringRecordId},</if>
            <if test="scoringRuleId != null ">#{scoringRuleId},</if>
            <if test="scoringRuleName != null  and scoringRuleName != ''">#{scoringRuleName},</if>
            <if test="scoringRuleDetailId != null ">#{scoringRuleDetailId},</if>
            <if test="scoringRuleDetailName != null  and scoringRuleDetailName != ''">#{scoringRuleDetailName},</if>
            <if test="recordingPosition != null  and recordingPosition != ''">#{recordingPosition},</if>
            <if test="deductScore != null ">#{deductScore},</if>
            <if test="remark != null  and remark != ''">#{remark},</if>
            <if test="createBy != null  and createBy != ''">#{createBy},</if>
            <if test="createTime != null ">#{createTime},</if>
            <if test="createById != null ">#{createById},</if>
            <if test="ruleType != null">#{ruleType},</if>
            <if test="typeName != null and typeName != ''">#{typeName},</if>
            <if test="keyWord != null and keyWord != ''">#{keyWord},</if>
            <if test="soundName != null and soundName != ''">#{soundName},</if>
            <if test="originalCallDetailId != null">#{originalCallDetailId},</if>
        </trim>
    </insert>

    <update id="updateCallcenterQualityMarkDetail" parameterType="CallcenterQualityMarkDetail">
        update callcenter_quality_mark_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="qualityInfoId != null ">quality_info_id = #{qualityInfoId},</if>
            <if test="qualityDetailId != null ">quality_detail_id = #{qualityDetailId},</if>
            <if test="callDetailId != null ">call_detail_id = #{callDetailId},</if>
            <if test="domain != null and domain != ''">domain = #{domain},</if>
            <if test="scoringRecordId != null ">scoring_record_id = #{scoringRecordId},</if>
            <if test="scoringRuleId != null ">scoring_rule_id = #{scoringRuleId},</if>
            <if test="scoringRuleName != null  and scoringRuleName != ''">scoring_rule_name = #{scoringRuleName},</if>
            <if test="scoringRuleDetailId != null ">scoring_rule_detail_id = #{scoringRuleDetailId},</if>
            <if test="scoringRuleDetailName != null  and scoringRuleDetailName != ''">scoring_rule_detail_name =
                #{scoringRuleDetailName},
            </if>
            <if test="recordingPosition != null  and recordingPosition != ''">recording_position =
                #{recordingPosition},
            </if>
            <if test="deductScore != null ">deduct_score = #{deductScore},</if>
            <if test="remark != null  and remark != ''">remark = #{remark},</if>
            <if test="createBy != null  and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null ">create_time = #{createTime},</if>
            <if test="createById != null ">create_by_id = #{createById},</if>
            <if test="soundName != null and soundName != ''">sound_name = #{soundName},</if>
            <if test="originalCallDetailId != null">original_call_detail_id = #{originalCallDetailId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCallcenterQualityMarkDetailById" parameterType="Long">
        delete from callcenter_quality_mark_detail where id = #{id}
    </delete>

    <delete id="deleteCallcenterQualityMarkDetailByIds" parameterType="String">
        delete from callcenter_quality_mark_detail where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsert">
        insert into callcenter_quality_mark_detail (
        id,
        quality_info_id,
        quality_detail_id,
        scoring_record_id,
        call_detail_id,
        scoring_rule_id,
        scoring_rule_name,
        rule_type,
        type_name,
        scoring_rule_detail_id,
        scoring_rule_detail_name,
        key_word,
        recording_position,
        deduct_score,
        remark,
        create_by,
        create_time,
        create_by_id,
        domain,
        sound_name,
        original_call_detail_id
        )
        values
        <foreach collection="markDetails" separator="," item="item">
            (#{item.id},#{item.qualityInfoId},#{item.qualityDetailId},#{item.scoringRecordId},#{item.callDetailId},
            #{item.scoringRuleId},#{item.scoringRuleName},#{item.ruleType},#{item.typeName},#{item.scoringRuleDetailId},
            #{item.scoringRuleDetailName},#{item.keyWord},#{item.recordingPosition},#{item.deductScore},#{item.remark},
            #{item.createBy}, #{item.createTime}, #{item.createById},#{item.domain}, #{item.soundName}, 
            #{item.originalCallDetailId})
        </foreach>
    </insert>

    <select id="selectMarkDetails"
            parameterType="com.gobon.project.intelligencequality.domain.param.QualityScoreResultParam"
            resultType="com.gobon.project.intelligencequality.domain.vo.QualityMarkDetailVo">
        select cqmd.id,
               cqmd.quality_info_id,
               cqmd.quality_detail_id,
               cqmd.call_detail_id,
               cqmd.domain,
               cqmd.scoring_record_id,
               cqmd.scoring_rule_id,
               cqmd.scoring_rule_name,
               cqmd.scoring_rule_detail_id,
               cqmd.scoring_rule_detail_name,
               cqmd.recording_position,
               cqmd.deduct_score,
               cqmd.remark,
               cqmd.rule_type,
               cqmd.type_name,
               cqmd.key_word,
               cqmd.sound_name,
               cqmd.original_call_detail_id,
               crd.type call_type,
               crd.help_flag,
               crd.multiparty_flag multiple_flag
        from callcenter_quality_mark_detail cqmd
         left join callcenter_record_detail crd on cqmd.original_call_detail_id = crd.id
        where cqmd.quality_info_id = #{qualityInfoId} and cqmd.quality_detail_id = #{qualityDetailId} 
        and cqmd.scoring_record_id = #{scoringRecordId} and cqmd.scoring_rule_id = #{scoringRuleId} 
        and cqmd.domain = #{domain} order by cqmd.original_call_detail_id, cqmd.id
    </select>
    
    <delete id="deleteByScoringRecordId">
        delete from callcenter_quality_mark_detail where scoring_record_id = #{scoringRecordId} and domain = #{domain}
    </delete>
</mapper>
