<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gobon.project.quality.mapper.CallcenterQualityTaskMapper">

    <update id="updateTaskNum">
        update callcenter_quality_task
        set check_num = (select count(1) from callcenter_quality_task_detail t1 where t1.check_status in ('1') and t1.task_id = #{taskId})
        , pass_radio = (
        ROUND(
        (select count(1) from  callcenter_quality_task_detail t1
        inner join callcenter_quality_detail t2 on t1.quality_detail_id = t2.id
        where t2.last_result_code in (1,2,3,4) and t1.task_id = #{taskId} and t1.check_status in ('1') )
        /
        (select nullif(count(1),0) from callcenter_quality_task_detail t1 where t1.check_status in ('1') and t1.task_id = #{taskId} )
        ,4) ) where id = #{taskId}
    </update>
    <sql id="BaseColumn">
        t.id,
        t.scoring_rule_id,
        t.scoring_rule_name,
        t.sample_rule_id,
        t.sample_rule_name,
        t.total_num,
        t.check_num,
        t.run_status,
        t.pass_radio,
        t.used_time,
        t.finish_time,
        t.start_time,
        t.end_time,
        t.error_log,
        t.create_time,
        t.create_by,
        t.create_by_id,
        t.del_flag,
        t.carry_person
    </sql>
    <resultMap type="com.gobon.project.quality.domain.vo.CallcenterQualityTaskVO" id="CallcenterQualityTaskMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="scoringRuleId" column="scoring_rule_id" jdbcType="INTEGER"/>
        <result property="scoringRuleName" column="scoring_rule_name" jdbcType="VARCHAR"/>
        <result property="sampleRuleId" column="sample_rule_id" jdbcType="INTEGER"/>
        <result property="sampleRuleName" column="sample_rule_name" jdbcType="VARCHAR"/>
        <result property="totalNum" column="total_num" jdbcType="INTEGER"/>
        <result property="checkNum" column="check_num" jdbcType="INTEGER"/>
        <result property="runStatus" column="run_status" jdbcType="VARCHAR"/>
        <result property="passRadio" column="pass_radio" jdbcType="DECIMAL"/>
        <result property="usedTime" column="used_time" jdbcType="INTEGER"/>
        <result property="finishTime" column="finish_time" jdbcType="TIMESTAMP"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="errorLog" column="error_log" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="findList" resultType="com.gobon.project.quality.domain.vo.CallcenterQualityTaskVO">
        select
        <include refid="BaseColumn"/>
        ,concat(round( (t.check_num / t.total_num)* 100  , 2 ),'%') as checkRadio
        ,concat(round(t.pass_radio * 100,2),'%') as passRadioStr
        from callcenter_quality_task t
        <where>
            1=1 and t.del_flag = '0'
            <if test="platForm != null and platForm != ''">
                and t.plat_form = #{platForm}
            </if>
            <if test="startDate != null">
                and DATE(t.create_time) &gt;= DATE(#{startDate})
            </if>
            <if test="endDate != null">
                and DATE(t.create_time) &lt;= DATE(#{endDate})
            </if>
            <if test="sampleRuleName != null and sampleRuleName != ''">
                and t.sample_rule_name = #{sampleRuleName}
            </if>
            <if test="runStatus != null and runStatus != ''">
                and t.run_status = #{runStatus}
            </if>
            <if test="sampleRuleId != null">
                <!-- 只查正在运行的任务 -->
                and t.sample_rule_id = #{sampleRuleId} and t.run_status = '2'
            </if>
        </where>
        order by t.create_time desc
    </select>
    <select id="findTaskDetailData"
            resultType="com.gobon.project.quality.domain.vo.CallcenterQualityTaskDetailVO">
        select
        t.id as id,t.task_id as task_id,t.quality_detail_id as qualityDetailId,t1.bill_number as callSerialNumber,
        t1.call_phone as callPhone,t1.answer_state as callStatus,t1.conversation_time as callTimes,t.check_status as checkStatus,
        t1.record_type as callType,t1.start_time as answerTime,t1.user_name as answerReason,t1.call_time as callTime,
        t3.consultation_time as consultationTime,
        t3.order_number as orderNumber,
        t3.reply_person_id as replyPersonId,
        t3.reply_person_name as replyPersonName,
        t3.phone_number as phoneNumber,
        t3.cons_order_number as consOrderNum,
        t3.grade as grade,
        t3.grade_desc as gradeDesc,t4.net_work_mul_id as netWorkMulId
        from callcenter_quality_task_detail t
        left join callcenter_record_detail t1 on t.record_detail_id = t1.id
        left join callcenter_quality_task t2 on t2.id = t.task_id
        left join callcenter_quality_detail_wl_ext t3 on t3.quality_detail_id = t.quality_detail_id
        left join callcenter_quality_detail t5 on t5.id = t.quality_detail_id
        left join callcenter_quality_info t4 on t4.id = t5.quality_info_id
        <where>
            t.task_id = #{taskId}
            <if test="platForm != null and platForm != ''">
                and t2.plat_form = #{platForm}
            </if>
            <if test="callSerialNumber != null and callSerialNumber != ''">
                and t1.bill_number = #{callSerialNumber}
            </if>
            <if test="callPhone != null and callPhone != ''">
                and t1.call_phone  = #{callPhone}
            </if>
            <if test="checkStatus != null and checkStatus != ''">
                and t.check_status = #{checkStatus}
            </if>
        </where>
        order by t.create_time desc
    </select>
</mapper>
