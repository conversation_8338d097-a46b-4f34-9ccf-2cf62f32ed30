<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gobon.project.report.mapper.PhoneMapper">
    <select id="data" resultType="com.gobon.project.report.vo.PhoneAllVo">
        select t1.abbreviation_name,
        ifnull(t1.ivrTotal, 0) ivrTotal,
        ifnull(t1.ivrGiveUpTotal, 0) ivrGiveUpTotal,
        ifnull(t1.fastHang, 0) fastHang,
        ifnull(t1.notServiceCount, 0) notServiceCount,
        ifnull(t1.inTotal, 0) inTotal,
        ifnull(t1.noAnswerTotal, 0) noAnswerTotal,
        ifnull(t1.noUserTotal, 0) noUserTotal,
        ifnull(t1.userNotAnswerTotal, 0) userNotAnswerTotal,
        ifnull(t1.userTotal, 0) userTotal,
        ifnull(concat(round(userTotal/inTotal*100, 2), '%'), '0.00%') userTotalRate,
        ifnull(t1.answerTotal, 0) answerTotal,
        ifnull(concat(round(answerTotal/userTotal*100, 2), '%'), '0.00%') userAnswerTotalRate,
        ifnull(concat(round(answerTotal/inTotal*100, 2), '%'), '0.00%') answerTotalRate,
        ifnull(t1.fiveAnswerCount, 0) fiveAnswerCount,
        ifnull(concat(round(fiveAnswerCount/inTotal*100, 2), '%'), '0.00%') fiveAnswerCountRate,
        ifnull(t1.thirtyAnswerCount, 0) thirtyAnswerCount,
        ifnull(concat(round(thirtyAnswerCount/inTotal*100, 2), '%'), '0.00%') thirtyAnswerCountRate,
        ifnull(t1.sixtyAnswerCount, 0) sixtyAnswerCount,
        ifnull(concat(round(sixtyAnswerCount/inTotal*100, 2), '%'), '0.00%') sixtyAnswerCountRate,
        ifnull(t1.ninetyAnswerCount, 0) ninetyAnswerCount,
        ifnull(concat(round(ninetyAnswerCount/inTotal*100, 2), '%'), '0.00%') ninetyAnswerCountRate
        from (
            select
            t1.abbreviation_name,
            t1.domain,
            t1.sort_num,
            sum(case when d.id is not null then 1 else 0 end) ivrTotal,
            sum(case when out_ivr_time is null and ivr_skill_group_id is null then 1 else 0 end) ivrGiveUpTotal,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state = 3 then 1 else 0 end) fastHang,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state != 3 and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>]]> '17:00' or DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<]]> '08:30') then 1 else 0 end) notServiceCount,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state != 3
            and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') then 1 else 0 end) inTotal,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state != 3 and answer_state != 1
            and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') then 1 else 0 end) noAnswerTotal,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state != 3 and d.user_name is null
            and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') then 1 else 0 end) noUserTotal,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state != 3 and answer_state != 1 and d.user_name is not null
            and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') then 1 else 0 end) userNotAnswerTotal,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state != 3 and d.user_name is not null
            and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') then 1 else 0 end) userTotal,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state != 3 and answer_state = 1
            and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') then 1 else 0 end) answerTotal,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state = 1 and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') and timestampdiff(SECOND, d.ring_start_time, d.start_time) <![CDATA[<=]]> 5 then 1 else 0 end) fiveAnswerCount,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state = 1 and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') and timestampdiff(SECOND, d.ring_start_time, d.start_time) > 5  and timestampdiff(SECOND, d.ring_start_time, d.start_time) <![CDATA[<=]]> 30 then 1 else 0 end) thirtyAnswerCount,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state = 1 and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') and timestampdiff(SECOND, d.ring_start_time, d.start_time) > 30  and timestampdiff(SECOND, d.ring_start_time, d.start_time) <![CDATA[<=]]> 60 then 1 else 0 end) sixtyAnswerCount,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state = 1 and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') and timestampdiff(SECOND, d.ring_start_time, d.start_time) > 60  and timestampdiff(SECOND, d.ring_start_time, d.start_time) <![CDATA[<=]]> 90 then 1 else 0 end) ninetyAnswerCount
            from sys_domain t1
            left join (
                select * from callcenter_record_detail d where record_type = 0 and d.type = 0
                <if test='queryStartTime !=null and queryStartTime != "" '>
                    and d.call_time  <![CDATA[>=]]> #{queryStartTime}
                </if>
                <if test='queryEndTime !=null and queryEndTime != "" '>
                    and d.call_time <![CDATA[<=]]> #{queryEndTime}
                </if>
                <if test="domain != null  and domain != ''">
                    and d.domain = #{domain}
                </if>
            ) d on d.domain = t1.domain
            LEFT JOIN sys_user su ON d.user_id = su.user_id
            left join sys_dept sd on d.dept_id = sd.dept_id
            left join sys_skill_group sg on sg.id = d.ivr_skill_group_id
            where t1.report_flag = 1
            <!-- 数据范围过滤 -->
            ${dataScope}
            GROUP BY t1.abbreviation_name
        ) t1
        <where>
            <if test="domain != null  and domain != ''">
                and t1.domain = #{domain}
            </if>
        </where>
        order by t1.sort_num
    </select>
    <select id="dataTotal" resultType="com.gobon.project.report.vo.PhoneAllVo">

        select t.abbreviation_name,
        ifnull(t.ivrTotal, 0) ivrTotal,
        ifnull(t.ivrGiveUpTotal, 0) ivrGiveUpTotal,
        ifnull(t.fastHang, 0) fastHang,
        ifnull(t.notServiceCount, 0) notServiceCount,
        ifnull(t.inTotal, 0) inTotal,
        ifnull(t.noAnswerTotal, 0) noAnswerTotal,
        ifnull(t.noUserTotal, 0) noUserTotal,
        ifnull(t.userNotAnswerTotal, 0) userNotAnswerTotal,
        ifnull(t.userTotal, 0) userTotal,
        ifnull(concat(round(userTotal/inTotal*100, 2), '%'), '0.00%') userTotalRate,
        ifnull(t.answerTotal, 0) answerTotal,
        ifnull(concat(round(answerTotal/userTotal*100, 2), '%'), '0.00%') userAnswerTotalRate,
        ifnull(concat(round(answerTotal/inTotal*100, 2), '%'), '0.00%') answerTotalRate,
        ifnull(t.fiveAnswerCount, 0) fiveAnswerCount,
        ifnull(concat(round(fiveAnswerCount/inTotal*100, 2), '%'), '0.00%') fiveAnswerCountRate,
        ifnull(t.thirtyAnswerCount, 0) thirtyAnswerCount,
        ifnull(concat(round(thirtyAnswerCount/inTotal*100, 2), '%'), '0.00%') thirtyAnswerCountRate,
        ifnull(t.sixtyAnswerCount, 0) sixtyAnswerCount,
        ifnull(concat(round(sixtyAnswerCount/inTotal*100, 2), '%'), '0.00%') sixtyAnswerCountRate,
        ifnull(t.ninetyAnswerCount, 0) ninetyAnswerCount,
        ifnull(concat(round(ninetyAnswerCount/inTotal*100, 2), '%'), '0.00%') ninetyAnswerCountRate
        from (
            select '合计' abbreviation_name,
            t1.sort_num,
            sum(case when d.id is not null then 1 else 0 end) ivrTotal,
            sum(case when out_ivr_time is null and ivr_skill_group_id is null then 1 else 0 end) ivrGiveUpTotal,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state = 3 then 1 else 0 end) fastHang,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state != 3 and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>]]> '17:00' or DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<]]> '08:30') then 1 else 0 end) notServiceCount,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state != 3
            and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') then 1 else 0 end) inTotal,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state != 3 and answer_state != 1
            and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') then 1 else 0 end) noAnswerTotal,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state != 3 and d.user_name is null
            and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') then 1 else 0 end) noUserTotal,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state != 3 and answer_state != 1 and d.user_name is not null
            and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') then 1 else 0 end) userNotAnswerTotal,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state != 3 and d.user_name is not null
            and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') then 1 else 0 end) userTotal,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state != 3 and answer_state = 1
            and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') then 1 else 0 end) answerTotal,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state = 1 and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') and timestampdiff(SECOND, d.ring_start_time, d.start_time) <![CDATA[<=]]> 5 then 1 else 0 end) fiveAnswerCount,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state = 1 and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') and timestampdiff(SECOND, d.ring_start_time, d.start_time) > 5  and timestampdiff(SECOND, d.ring_start_time, d.start_time) <![CDATA[<=]]> 30 then 1 else 0 end) thirtyAnswerCount,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state = 1 and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') and timestampdiff(SECOND, d.ring_start_time, d.start_time) > 30  and timestampdiff(SECOND, d.ring_start_time, d.start_time) <![CDATA[<=]]> 60 then 1 else 0 end) sixtyAnswerCount,
            sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state = 1 and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') and timestampdiff(SECOND, d.ring_start_time, d.start_time) > 60  and timestampdiff(SECOND, d.ring_start_time, d.start_time) <![CDATA[<=]]> 90 then 1 else 0 end) ninetyAnswerCount
            from sys_domain t1
            left join (
                select * from callcenter_record_detail d where record_type = 0 and d.type = 0
                <if test='queryStartTime !=null and queryStartTime != "" '>
                    and d.call_time  <![CDATA[>=]]> #{queryStartTime}
                </if>
                <if test='queryEndTime !=null and queryEndTime != "" '>
                    and d.call_time <![CDATA[<=]]> #{queryEndTime}
                </if>
                <if test="domain != null  and domain != ''">
                    and d.domain = #{domain}
                </if>
            ) d on d.domain = t1.domain
            LEFT JOIN sys_user su ON d.user_id = su.user_id
            left join sys_dept sd on d.dept_id = sd.dept_id
            left join sys_skill_group sg on sg.id = d.ivr_skill_group_id
            where record_type = 0 and d.type = 0 and t1.report_flag = 1
            <!-- 数据范围过滤 -->
            ${dataScope}
            <if test='queryStartTime !=null and queryStartTime != "" '>
                and d.call_time  <![CDATA[>=]]> #{queryStartTime}
            </if>
            <if test='queryEndTime !=null and queryEndTime != "" '>
                and d.call_time <![CDATA[<=]]> #{queryEndTime}
            </if>
            <if test="domain != null  and domain != ''">
                and d.domain = #{domain}
            </if>
        ) t
    </select>
    <select id="dataPrison" resultType="com.gobon.project.report.vo.PhonePrisonVo">
        SELECT t1.nick_name user_name, t1.user_number,
        ifnull(fastHang, 0) fastHang,
        ifnull(noAnswerTotal, 0) noAnswerTotal,
        ifnull(userNotAnswerTotal, 0) userNotAnswerTotal,
        ifnull(userTotal, 0) userTotal,
        ifnull(userTotalRate, '0.00%') userTotalRate,
        ifnull(answerTotal, 0) answerTotal,
        ifnull(userAnswerTotalRate, '0.00%') userAnswerTotalRate,
        ifnull(answerTotalRate, '0.00%') answerTotalRate,
        ifnull(fiveAnswerCount, 0) fiveAnswerCount,
        ifnull(fiveAnswerCountRate, '0.00%') fiveAnswerCountRate,
        ifnull(thirtyAnswerCount, 0) thirtyAnswerCount,
        ifnull(thirtyAnswerCountRate, '0.00%') thirtyAnswerCountRate,
        ifnull(sixtyAnswerCount, 0) sixtyAnswerCount,
        ifnull(sixtyAnswerCountRate, '0.00%') sixtyAnswerCountRate,
        ifnull(ninetyAnswerCount, 0) ninetyAnswerCount,
        ifnull(ninetyAnswerCountRate, '0.00%') ninetyAnswerCountRate
        FROM (
            select * from sys_user su
            where domain = 'ln.sft.jyj.com' and freeswitch_phone is not null and LENGTH(trim(freeswitch_phone))>0
        ) t1 left join (
            select t.user_name, t.user_id,
            ifnull(t.fastHang, 0) fastHang,
            ifnull(t.noAnswerTotal, 0) noAnswerTotal,
            ifnull(t.userNotAnswerTotal, 0) userNotAnswerTotal,
            ifnull(t.userTotal, 0) userTotal,
            ifnull(concat(round(userTotal/userTotal*100, 2), '%'), '0.00%') userTotalRate,
            ifnull(t.answerTotal, 0) answerTotal,
            ifnull(concat(round(answerTotal/userTotal*100, 2), '%'), '0.00%') userAnswerTotalRate,
            ifnull(concat(round(answerTotal/userTotal*100, 2), '%'), '0.00%') answerTotalRate,
            ifnull(t.fiveAnswerCount, 0) fiveAnswerCount,
            ifnull(concat(round(fiveAnswerCount/userTotal*100, 2), '%'), '0.00%') fiveAnswerCountRate,
            ifnull(t.thirtyAnswerCount, 0) thirtyAnswerCount,
            ifnull(concat(round(thirtyAnswerCount/userTotal*100, 2), '%'), '0.00%') thirtyAnswerCountRate,
            ifnull(t.sixtyAnswerCount, 0) sixtyAnswerCount,
            ifnull(concat(round(sixtyAnswerCount/userTotal*100, 2), '%'), '0.00%') sixtyAnswerCountRate,
            ifnull(t.ninetyAnswerCount, 0) ninetyAnswerCount,
            ifnull(concat(round(ninetyAnswerCount/userTotal*100, 2), '%'), '0.00%') ninetyAnswerCountRate
            from (
                select
                d.user_name,
                d.user_id,
                sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state = 3 then 1 else 0 end) fastHang,
                sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state != 3 and answer_state != 1
                and (DATE_FORMAT(d.call_time,'%H:%i') >= '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') then 1 else 0 end) noAnswerTotal,
                sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state != 3 and answer_state != 1 and d.user_name is not null
                and (DATE_FORMAT(d.call_time,'%H:%i') >= '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') then 1 else 0 end) userNotAnswerTotal,
                sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state != 3 and d.user_name is not null
                and (DATE_FORMAT(d.call_time,'%H:%i') >= '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') then 1 else 0 end) userTotal,
                sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state != 3 and answer_state = 1
                and (DATE_FORMAT(d.call_time,'%H:%i') >= '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') then 1 else 0 end) answerTotal,
                sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state = 1 and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') and timestampdiff(SECOND, d.ring_start_time, d.start_time) <![CDATA[<=]]> 5 then 1 else 0 end) fiveAnswerCount,
                sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state = 1 and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') and timestampdiff(SECOND, d.ring_start_time, d.start_time) > 5  and timestampdiff(SECOND, d.ring_start_time, d.start_time) <![CDATA[<=]]> 30 then 1 else 0 end) thirtyAnswerCount,
                sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state = 1 and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') and timestampdiff(SECOND, d.ring_start_time, d.start_time) > 30  and timestampdiff(SECOND, d.ring_start_time, d.start_time) <![CDATA[<=]]> 60 then 1 else 0 end) sixtyAnswerCount,
                sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state = 1 and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') and timestampdiff(SECOND, d.ring_start_time, d.start_time) > 60  and timestampdiff(SECOND, d.ring_start_time, d.start_time) <![CDATA[<=]]> 90 then 1 else 0 end) ninetyAnswerCount
                from callcenter_record_detail d
                LEFT JOIN sys_user su ON d.user_id = su.user_id
                left join sys_dept sd on d.dept_id = sd.dept_id
                left join sys_skill_group sg on sg.id = d.ivr_skill_group_id
                where d.domain = 'ln.sft.jyj.com' and d.user_name is not null and record_type = 0 and d.type = 0
                <!-- 数据范围过滤 -->
                ${dataScope}
                <if test='queryStartTime !=null and queryStartTime != "" '>
                    and d.call_time  <![CDATA[>=]]> #{queryStartTime}
                </if>
                <if test='queryEndTime !=null and queryEndTime != "" '>
                    and d.call_time <![CDATA[<=]]> #{queryEndTime}
                </if>
                GROUP BY d.user_name
            ) t
            order by t.userTotal
        ) t2 on t1.user_id = t2.user_id
    </select>
    <select id="dataPrisonTotal" resultType="com.gobon.project.report.vo.PhonePrisonVo">
        select t.user_name,
        ifnull(t.fastHang, 0) fastHang,
        ifnull(t.noAnswerTotal, 0) noAnswerTotal,
        ifnull(t.userNotAnswerTotal, 0) userNotAnswerTotal,
        ifnull(t.userTotal, 0) userTotal,
        ifnull(concat(round(userTotal/userTotal*100, 2), '%'), '0.00%') userTotalRate,
        ifnull(t.answerTotal, 0) answerTotal,
        ifnull(concat(round(answerTotal/userTotal*100, 2), '%'), '0.00%') userAnswerTotalRate,
        ifnull(concat(round(answerTotal/userTotal*100, 2), '%'), '0.00%') answerTotalRate,
        ifnull(t.fiveAnswerCount, 0) fiveAnswerCount,
        ifnull(concat(round(fiveAnswerCount/userTotal*100, 2), '%'), '0.00%') fiveAnswerCountRate,
        ifnull(t.thirtyAnswerCount, 0) thirtyAnswerCount,
        ifnull(concat(round(thirtyAnswerCount/userTotal*100, 2), '%'), '0.00%') thirtyAnswerCountRate,
        ifnull(t.sixtyAnswerCount, 0) sixtyAnswerCount,
        ifnull(concat(round(sixtyAnswerCount/userTotal*100, 2), '%'), '0.00%') sixtyAnswerCountRate,
        ifnull(t.ninetyAnswerCount, 0) ninetyAnswerCount,
        ifnull(concat(round(ninetyAnswerCount/userTotal*100, 2), '%'), '0.00%') ninetyAnswerCountRate
        from (
            select
                '合计' user_name,
                sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state = 3 then 1 else 0 end) fastHang,
                sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state != 3 and answer_state != 1
                and (DATE_FORMAT(d.call_time,'%H:%i') >= '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') then 1 else 0 end) noAnswerTotal,
                sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state != 3 and answer_state != 1 and d.user_name is not null
                and (DATE_FORMAT(d.call_time,'%H:%i') >= '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') then 1 else 0 end) userNotAnswerTotal,
                sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state != 3 and d.user_name is not null
                and (DATE_FORMAT(d.call_time,'%H:%i') >= '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') then 1 else 0 end) userTotal,
                sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state != 3 and answer_state = 1
                and (DATE_FORMAT(d.call_time,'%H:%i') >= '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') then 1 else 0 end) answerTotal,
                sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state = 1 and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') and timestampdiff(SECOND, d.ring_start_time, d.start_time) <![CDATA[<=]]> 5 then 1 else 0 end) fiveAnswerCount,
                sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state = 1 and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') and timestampdiff(SECOND, d.ring_start_time, d.start_time) > 5  and timestampdiff(SECOND, d.ring_start_time, d.start_time) <![CDATA[<=]]> 30 then 1 else 0 end) thirtyAnswerCount,
                sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state = 1 and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') and timestampdiff(SECOND, d.ring_start_time, d.start_time) > 30  and timestampdiff(SECOND, d.ring_start_time, d.start_time) <![CDATA[<=]]> 60 then 1 else 0 end) sixtyAnswerCount,
                sum(case when out_ivr_time is not null and ivr_skill_group_id is not null and answer_state = 1 and (DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[>=]]> '08:30' and DATE_FORMAT(d.call_time,'%H:%i') <![CDATA[<=]]> '17:00') and timestampdiff(SECOND, d.ring_start_time, d.start_time) > 60  and timestampdiff(SECOND, d.ring_start_time, d.start_time) <![CDATA[<=]]> 90 then 1 else 0 end) ninetyAnswerCount
            from callcenter_record_detail d
            LEFT JOIN sys_user su ON d.user_id = su.user_id
            left join sys_dept sd on d.dept_id = sd.dept_id
            left join sys_skill_group sg on sg.id = d.ivr_skill_group_id
            where d.domain = 'ln.sft.jyj.com' and d.user_name is not null and record_type = 0 and d.type = 0
            <if test='queryStartTime !=null and queryStartTime != "" '>
                and d.call_time  <![CDATA[>=]]> #{queryStartTime}
            </if>
            <if test='queryEndTime !=null and queryEndTime != "" '>
                and d.call_time <![CDATA[<=]]> #{queryEndTime}
            </if>
            <!-- 数据范围过滤 -->
            ${dataScope}
        ) t
    </select>


</mapper>