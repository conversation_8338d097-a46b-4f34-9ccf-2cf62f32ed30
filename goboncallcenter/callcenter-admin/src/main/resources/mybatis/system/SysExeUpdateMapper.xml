<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gobon.project.system.mapper.SysExeUpdateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gobon.project.system.domain.SysExeUpdate">
        <id column="id" property="id" />
        <result column="version" property="version" />
        <result column="download_url" property="downloadUrl" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="pub_time" property="pubTime" />
        <result column="pub_content" property="pubContent" />
    </resultMap>
    <select id="getExeLastVersion" resultType="com.gobon.project.system.domain.SysExeUpdate">
        SELECT * FROM `sys_exe_update` where NOW() > pub_time ORDER BY pub_time desc limit 1
    </select>

</mapper>
