<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gobon.project.networkquality.mapper.NetworkQualityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gobon.project.networkquality.domain.NetworkQuality">
        <result column="ID" property="id" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="CREATE_BY_ID" property="createById" />
        <result column="CREATE_BY" property="createBy" />
        <result column="RULE_ID" property="ruleId" />
        <result column="COMMENTS" property="comments" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="SCORE" property="score" />
        <result column="UPDATE_BY_ID" property="updateById" />
        <result column="CONSULT_MUL_ID" property="consultMulId" />
        <result column="CHARGE_USER_ID" property="chargeUserId" />
        <result column="RECHECK_COUNT" property="recheckCount" />
        <result column="APPEAL_COUNT" property="appealCount" />
        <result column="PASS_FLAG" property="passFlag" />
        <result column="TABOO_FLAG" property="tabooFlag" />
    </resultMap>
    <select id="getQualityList" resultType="com.gobon.project.networkquality.domain.vo.NetworkQualityVO">
        SELECT
            t6.ID,
            MU.ID MUL_ID,
            RE.CONSULTATION_TIME,
            T1.REPLY_PERSON_NAME,
            T1.ORDER_NUMBER,
            T1.PHONE_NUMBER,
            T5.FS_ITEM_DESC CONSULTATION_TYPE,
            T6.UPDATE_TIME QUALITY_TIME,
            T6.UPDATE_BY QUALITY_BY,
            T7.FS_ITEM_DESC GRADE,
            T8.FS_ITEM_DESC CONSULTATION_STATE,
            T6.SCORE,
            T6.CHARGE_USER_NAME,
            case when t6.TABOO_FLAG = '1' then '是' when t6.TABOO_FLAG = '0' then '否' else '' end TABOO_FLAG,
            case when t6.PASS_FLAG = '1' then '合格' when t6.PASS_FLAG = '0' then '不合格' else '' end PASS_FLAG
        FROM
        TB_GB_CONSULTATION_BILL T1
        LEFT JOIN TB_GB_CONSULTATION_MULTIMEDIA MU ON T1.MULTIMEDIA_ID_REF = MU.ID
        LEFT JOIN TB_GB_CONSULTATION_RECORD RE ON MU.CONSULTATION_ID = RE.ID
        LEFT JOIN TB_GB_EVALUATE tt2 ON MU.CONSULTATION_ID = TT2.SERVICE_ID_REF
        left join (select * from DICTIONARY WHERE FS_TYPE = 'consult_business_type') T5 ON RE.CONSULTATION_TYPE = T5.FS_ITEM_CODE
        left join (select * from DICTIONARY WHERE FS_TYPE = 'grade') T7 ON tt2.GRADE = T7.FS_ITEM_CODE
        left join (select * from DICTIONARY WHERE FS_TYPE = 'consultation_state') T8 ON RE.consultation_state = T8.FS_ITEM_CODE
        left join (SELECT * FROM NETWORK_QUALITY WHERE STATUS != '0') T6 ON MU.ID = T6.CONSULT_MUL_ID
        WHERE T1.CONSULTATION_MODE IN ( '20' )
        <if test="queryStartTime != null  and queryStartTime != ''">
            and RE.CONSULTATION_TIME >= TO_DATE(#{queryStartTime}, 'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="queryEndTime != null  and queryEndTime != ''">
            and RE.CONSULTATION_TIME <![CDATA[<=]]> TO_DATE(#{queryEndTime}, 'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="qualityStartTime != null  and qualityStartTime != ''">
            and T6.UPDATE_TIME >= TO_DATE(#{qualityStartTime}, 'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="qualityEndTime != null  and qualityEndTime != ''">
            and T6.UPDATE_TIME <![CDATA[<=]]> TO_DATE(#{qualityEndTime}, 'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="qualityFlag != null  and qualityFlag == '1'.toString()">
            and T6.id is not null
        </if>
        <if test="qualityFlag != null  and qualityFlag == '0'.toString()">
            and (T6.ID IS NULL OR T6.STATUS = '2')
        </if>
        <if test="grade != null  and grade != ''">
            and T7.FS_ITEM_DESC = #{grade}
        </if>
        <if test="phoneNumber != null  and phoneNumber != ''">
            and T1.PHONE_NUMBER = #{phoneNumber}
        </if>
        <if test="appealStatus != null  and appealStatus != ''">
            and T6.APPEAL_STATUS = #{appealStatus}
        </if>
        <if test="passFlag != null  and passFlag != ''">
            and T6.PASS_FLAG = #{passFlag}
        </if>
        <if test="orderNumber != null  and orderNumber != ''">
            and T1.ORDER_NUMBER = #{orderNumber}
        </if>
        <if test="agentName != null and agentName != ''">
            and  T1.REPLY_PERSON_NAME like '%' || #{agentName} || '%'
        </if>
        <if test="userNickName != null  and userNickName != ''">
            and  re.PERSON_NAME like '%' || #{userNickName} || '%'
        </if>
        <if test="chargeUserName != null  and chargeUserName != ''">
            and  T6.CHARGE_USER_NAME like '%' || #{chargeUserName} || '%'
        </if>
        <if test="userId != null">
            and T6.CHARGE_USER_ID = #{userId}
        </if>
        <if test="queryType != null">
          <if test="queryType == '1'.toString()">
              and (T6.ID IS NULL OR T6.STATUS = '2')
          </if>
          <if test="queryType == '2'.toString()">
              and T6.ID IS NOT NULL and T6.STATUS != '2'
          </if>
          <if test="queryType == '3'.toString()">
              and T6.ID IS NOT NULL AND APPEAL_COUNT > RECHECK_COUNT and T6.STATUS != '2'
          </if>
        </if>
        <if test="queryType != null">
            <if test="queryType == '1'.toString()">
                ORDER BY RE.CONSULTATION_TIME DESC
            </if>
            <if test="queryType == '2'.toString()">
                ORDER BY T6.UPDATE_TIME DESC
            </if>
            <if test="queryType == '3'.toString()">
                ORDER BY T6.UPDATE_TIME DESC
            </if>
        </if>

    </select>
    <select id="getQualityDetailById"
            resultType="com.gobon.project.networkquality.domain.vo.NetworkQualityDetailVO">
        select * from
         (
             SELECT
                 MU.ID MUL_ID,
                 T5.ID ID,
                 T5.STATUS QUALITY_STATUS,
                 MU.CONSULTATION_ID,
                 t2.CONSULTATION_TIME,
                T10.FS_ITEM_DESC CONSULTATION_STATE,
                t9.BUSINESS_TYPE,
                 MU.CALL_START,
                 MU.CALL_END,
                 CB.ORDER_NUMBER,
                t2.ORDER_NUMBER as "consOrderNumber",
                CB.REPLY_PERSON_NAME,
                 CB.PERSON_NAME,
                 CB.PERSON_IDCARD,
                 CB.PHONE_NUMBER PERSON_NUMBER,
                 CB.PHONE_NUMBER PHONE_NUMBER,
                 T3.FS_ITEM_DESC MASSES_TYPE,
                 T4.FS_ITEM_DESC INDUSTRY_TYPE,
                 CB.CONSULT_CONTENT,
                 CB.REPLY_CONTENT,
                 CB.PERSON_ACCOUNT,
                 CB.OCCURRENCE_ADDRESS,
                 CB.OCCURRENCE_DESC,
                 T7.FS_ITEM_DESC CONSULTATION_TYPE,
                 T5.UPDATE_TIME QUALITY_TIME,
                 T8.FS_ITEM_DESC GRADE
             FROM TB_GB_CONSULTATION_BILL CB
              LEFT JOIN TB_GB_CONSULTATION_MULTIMEDIA  MU ON CB.MULTIMEDIA_ID_REF = MU.ID
              LEFT JOIN FLFWZQ.TB_GB_LEGAL_CONSULT_FEEDBACK fw ON fw .CONSULT_ID = MU.CONSULTATION_ID
              left join TB_GB_CONSULTATION_RECORD t2 on MU.consultation_id = t2.id
              LEFT JOIN TB_GB_ACCOUNT_PERSONAL PERSONAL ON PERSONAL.ACCOUNT_ID_REF = CB.REPLY_PERSON_ID
              left join (select * from DICTIONARY WHERE FS_TYPE = 'personType') T3 ON CB.MASSES_TYPE = T3.FS_ITEM_CODE
              left join (select * from DICTIONARY WHERE FS_TYPE = 'industryCategory') T4 ON CB.INDUSTRY_TYPE = T4.FS_ITEM_CODE
              left join NETWORK_QUALITY T5 ON MU.ID = T5.CONSULT_MUL_ID
              LEFT JOIN TB_GB_EVALUATE T6 ON MU.CONSULTATION_ID = T6.SERVICE_ID_REF
              left join (select * from DICTIONARY WHERE FS_TYPE = 'consult_business_type') T7 ON t2.CONSULTATION_TYPE = T7.FS_ITEM_CODE
              left join (select * from DICTIONARY WHERE FS_TYPE = 'grade') T8 ON T6.GRADE = T8.FS_ITEM_CODE
              LEFT JOIN tb_gb_bill_business_type t9 on T9.consultation_id = CB.ID
        left join (select * from DICTIONARY WHERE FS_TYPE = 'consultation_state') T10 ON t2.CONSULTATION_TYPE = T2.CONSULTATION_STATE
             WHERE MU.ID = #{id}
             ORDER BY T5.CREATE_TIME DESC
         ) t where ROWNUM = 1

    </select>
    <select id="getChatList" resultType="com.gobon.project.networkquality.domain.vo.NetworkQualityChatVO">
        SELECT DISTINCT
            MSG.SEND_TIME,
            MSG.NAME,
            BODY.MSG,
            BODY.TYPE,
            (case when t4.CONTRACTOR_NAME = MSG.NAME then '1' else '2' end) AS agent
        FROM
            TG_GB_EASEMOB_MSG MSG
                LEFT JOIN TG_GB_EASEMOB_MSG_BODY BODY ON MSG.ID = BODY.MSG_ID
                LEFT JOIN TB_GB_CONSULTATION_RECORD t2 ON MSG.CONSULT_ID = t2.id
                LEFT JOIN TB_GB_IM_INFO t3 ON MSG.MSG_FROM = T3.ID
                LEFT JOIN TB_GB_CONSULTATION_MULTIMEDIA t4 ON T4.CONSULTATION_ID = MSG.CONSULT_ID
        WHERE
            BODY.TYPE != 'cmd'
	    AND MSG.CONSULT_ID = #{consultId}
	    AND t4.ID = #{mulId}
        ORDER BY
            MSG.SEND_TIME
    </select>
    <select id="getBusinessTypeDesc" resultType="java.lang.String">
        WITH T1 AS ( SELECT #{businessType} bill_business_type FROM dual )
        SELECT
            t3.name || ',' || t4.name || ',' || t5.name || ',' || t6.name
        FROM
        t1
        LEFT JOIN TB_BUSINESS_TYPE t3 ON
        CASE WHEN instr( bill_business_type, ',' ) > 0 THEN
                 REGEXP_SUBSTR( bill_business_type, '[^,]+', 1, 1 ) ELSE substr( bill_business_type, 0, 1 )
            END = t3.ID
        LEFT JOIN TB_BUSINESS_TYPE t4 ON
        CASE WHEN instr( bill_business_type, ',' ) > 0 THEN
                 REGEXP_SUBSTR( bill_business_type, '[^,]+', 2 ) ELSE substr( bill_business_type, 1 )
            END = t4.ID
        LEFT JOIN TB_BUSINESS_TYPE t5 ON REGEXP_SUBSTR( bill_business_type, '[^,]+', 1, 3 ) = t5.id
        LEFT JOIN TB_BUSINESS_TYPE t6 ON REGEXP_SUBSTR( bill_business_type, '[^,]+', 1, 4 ) = t6.id
    </select>
    <select id="getQualityRuleOption"
            resultType="com.gobon.project.networkquality.domain.vo.NetworkQualityRecordOptionVO">
        select ID, OPTION_NAME, OPTION_DESC_STATE, OPTION_DESC, SCORE TOTAL_SCORE, '0' CHECK_STATE
        from NETWORK_QUALITY_OPTION
        where RULE_id = #{ruleId} and DEL_FLAG = '0' and type = #{type}
    </select>
    <select id="getQualityRecordOption"
            resultType="com.gobon.project.networkquality.domain.vo.NetworkQualityRecordOptionVO">
        select t2.ID, OPTION_NAME, OPTION_DESC_STATE, OPTION_DESC, t1.SCORE ,t2.score TOTAL_SCORE, t1.CHECK_STATE
        from NETWORK_QUALITY_RECORD_OPTION t1
                 left join NETWORK_QUALITY_OPTION t2 on t1.OPTION_ID = t2.ID
        where QUALITY_ID = #{qualityId} and t2.type = #{type}
    </select>
    <select id="getQualityHistDetailById"
            resultType="com.gobon.project.networkquality.domain.vo.NetworkQualityDetailVO">
        SELECT
            MU.ID MUL_ID,
            T5.ID ID,
            MU.CONSULTATION_ID,
            t2.CONSULTATION_TIME,
            t9.BUSINESS_TYPE,
            MU.CALL_START,
            MU.CALL_END,
            CB.ORDER_NUMBER,
            t2.ORDER_NUMBER as "consOrderNumber",
            CB.REPLY_PERSON_NAME,
            CB.PERSON_NAME,
            CB.PERSON_IDCARD,
            CB.PHONE_NUMBER PERSON_NUMBER,
            T3.FS_ITEM_DESC MASSES_TYPE,
            T4.FS_ITEM_DESC INDUSTRY_TYPE,
            CB.CONSULT_CONTENT,
            CB.REPLY_CONTENT,
            CB.PERSON_ACCOUNT,
            CB.OCCURRENCE_ADDRESS,
            CB.OCCURRENCE_DESC,
            T7.FS_ITEM_DESC CONSULTATION_TYPE,
            T5.UPDATE_TIME QUALITY_TIME,
            T8.FS_ITEM_DESC GRADE
        FROM TB_GB_CONSULTATION_BILL CB
         INNER JOIN TB_GB_CONSULTATION_MULTIMEDIA  MU ON CB.MULTIMEDIA_ID_REF = MU.ID
         LEFT JOIN FLFWZQ.TB_GB_LEGAL_CONSULT_FEEDBACK fw ON fw .CONSULT_ID = MU.CONSULTATION_ID
         left join TB_GB_CONSULTATION_RECORD t2 on MU.consultation_id = t2.id
         LEFT JOIN TB_GB_ACCOUNT_PERSONAL PERSONAL ON PERSONAL.ACCOUNT_ID_REF = CB.REPLY_PERSON_ID
         left join (select * from DICTIONARY WHERE FS_TYPE = 'personType') T3 ON CB.MASSES_TYPE = T3.FS_ITEM_CODE
         left join (select * from DICTIONARY WHERE FS_TYPE = 'industryCategory') T4 ON CB.INDUSTRY_TYPE = T4.FS_ITEM_CODE
         left join NETWORK_QUALITY T5 ON MU.ID = T5.CONSULT_MUL_ID
         LEFT JOIN TB_GB_EVALUATE T6 ON MU.CONSULTATION_ID = T6.SERVICE_ID_REF
         left join (select * from DICTIONARY WHERE FS_TYPE = 'consult_business_type') T7 ON t2.CONSULTATION_TYPE = T7.FS_ITEM_CODE
         left join (select * from DICTIONARY WHERE FS_TYPE = 'grade') T8 ON T6.GRADE = T8.FS_ITEM_CODE
         LEFT JOIN tb_gb_bill_business_type t9 on T9.consultation_id = CB.ID
        WHERE MU.ID = #{id} and STATUS != '3'
    </select>
     <select id="locdNetworkQualityVOList"
            resultType="com.gobon.project.networkquality.domain.vo.NetworkQualityDetailVO">
        select * from (SELECT
        MU.ID MUL_ID,
        MU.CONSULTATION_ID,
        t2.CONSULTATION_TIME,
        t9.BUSINESS_TYPE,
        MU.CALL_START,
        MU.CALL_END,
        CB.ORDER_NUMBER,
        t2.ORDER_NUMBER as "consOrderNumber",
        CB.REPLY_PERSON_ID,
        CB.REPLY_PERSON_NAME,
        CB.PERSON_NAME,
        CB.PERSON_IDCARD,
        CB.PHONE_NUMBER PERSON_NUMBER,
        CB.PHONE_NUMBER phoneNumber,
        T3.FS_ITEM_DESC MASSES_TYPE,
        T4.FS_ITEM_DESC INDUSTRY_TYPE,
        CB.CONSULT_CONTENT,
        CB.REPLY_CONTENT,
        CB.PERSON_ACCOUNT,
        CB.OCCURRENCE_ADDRESS,
        CB.OCCURRENCE_DESC,
        T7.FS_ITEM_DESC CONSULTATION_TYPE,
        T8.FS_ITEM_DESC GRADE,ROWNUM rn,
        t6.GRADE as "grade",
        (case T6.GRADE WHEN '10' THEN '非常满意' WHEN '20' THEN '满意' WHEN '30' THEN '一般' WHEN '40' THEN '不满意' ELSE '' END ) AS "gradeDesc"
        FROM TB_GB_CONSULTATION_BILL CB
        INNER JOIN TB_GB_CONSULTATION_MULTIMEDIA  MU ON CB.MULTIMEDIA_ID_REF = MU.ID
        LEFT JOIN FLFWZQ.TB_GB_LEGAL_CONSULT_FEEDBACK fw ON fw .CONSULT_ID = MU.CONSULTATION_ID
        left join TB_GB_CONSULTATION_RECORD t2 on MU.consultation_id = t2.id
        LEFT JOIN TB_GB_ACCOUNT_PERSONAL PERSONAL ON PERSONAL.ACCOUNT_ID_REF = CB.REPLY_PERSON_ID
        left join (select * from DICTIONARY WHERE FS_TYPE = 'personType') T3 ON CB.MASSES_TYPE = T3.FS_ITEM_CODE
        left join (select * from DICTIONARY WHERE FS_TYPE = 'industryCategory') T4 ON CB.INDUSTRY_TYPE = T4.FS_ITEM_CODE
        LEFT JOIN TB_GB_EVALUATE T6 ON MU.CONSULTATION_ID = T6.SERVICE_ID_REF
        left join (select * from DICTIONARY WHERE FS_TYPE = 'consult_business_type') T7 ON t2.CONSULTATION_TYPE = T7.FS_ITEM_CODE
        left join (select * from DICTIONARY WHERE FS_TYPE = 'grade') T8 ON T6.GRADE = T8.FS_ITEM_CODE
        LEFT JOIN tb_gb_bill_business_type t9 on T9.consultation_id = CB.ID
        WHERE 1=1
        <choose>
            <when test="all">
                <!--查询最近一个小时内得记录-->
                AND mu.CONSULTATION_TIME >= SYSDATE - ${wlHour}/24
            </when>
            <otherwise>
                <if test="startTime != null">
                    AND mu.CONSULTATION_TIME &gt;= #{startTime}
                </if>
                <if test="endTime!= null">
                    and mu.CONSULTATION_TIME &lt;= #{endTime}
                </if>
            </otherwise>
        </choose>

         <choose>
             <when test="all">
                    order by mu.CONSULTATION_TIME desc
             </when>
             <otherwise>
                 <if test="range != null">
                     order by DBMS_RANDOM.VALUE
                 </if>
             </otherwise>
         </choose>

        ) t where 1= 1
        <if test="range != null">
            and ROWNUM &lt;= #{range}
        </if>
    </select>
    <select id="getNetWorkMulIdByOrderNumber" resultType="java.lang.String">
        SELECT MU.ID FROM TB_GB_CONSULTATION_BILL CB
        INNER JOIN TB_GB_CONSULTATION_MULTIMEDIA MU
        ON CB.MULTIMEDIA_ID_REF = MU.ID
        WHERE 1=1
        <if test="orderNumber != '' and orderNumber != null">
            and CB.ORDER_NUMBER = #{orderNumber}
        </if>
        <if test="consultationStartTime != null">
            and MU.CONSULTATION_TIME &gt;= #{consultationStartTime}
        </if>
        <if test="consultationEndTime != null">
            and MU.CONSULTATION_TIME &lt;= #{consultationEndTime}
        </if>
    </select>

</mapper>
