<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gobon.project.conversation.mapper.CallcenterRecordDetailMapper">

    <resultMap type="com.gobon.project.conversation.domain.vo.CallcenterRecordDetailVO" id="CallcenterRecordDetailResult">
        <result property="id"    column="id"    />
        <result property="billNumber"    column="bill_number"    />
        <result property="recordId"    column="record_id"    />
        <result property="type"    column="type"    />
        <result property="ivrRoute"    column="ivr_route"    />
        <result property="businessType"    column="business_type"    />
        <result property="callPhone"    column="call_phone"    />
        <result property="callTime"    column="call_time"    />
        <result property="outIvrTime"    column="out_ivr_time"    />
        <result property="startTime"    column="start_time"    />
        <result property="outTime"    column="out_time"    />
        <result property="endPerson"    column="end_person"    />
        <result property="switchFlag"    column="switch_flag"    />
        <result property="switchFlagDesc"    column="switch_flag_desc"    />
        <result property="switchTime"    column="switch_time"    />
        <result property="helpFlag"    column="help_flag"    />
        <result property="helpFlagDesc"    column="help_flag_desc"    />
        <result property="helpTime"    column="help_time"    />
        <result property="multipartyFlag"    column="multiparty_flag"    />
        <result property="multipartyFlagDesc"    column="multiparty_flag_desc"    />
        <result property="multipartyTime"    column="multiparty_time"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="userNumber"    column="user_number"    />
        <result property="deptId"    column="dept_id"    />
        <result property="userPhone"    column="user_phone"    />
        <result property="country"    column="country"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="intoCity"    column="intoCity"    />
        <result property="isp"    column="isp"    />
        <result property="customerId"    column="customer_id"    />
        <result property="sound"    column="sound"    />
        <result property="soundText"    column="sound_text"    />
        <result property="satisfId"    column="satisf_id"    />
        <result property="statisfName"    column="statisf_name"    />
        <result property="serviceState"    column="service_state"    />
        <result property="serviceTypeId"    column="service_type_id"    />
        <result property="serviceTypeName"    column="service_type_name"    />
        <result property="serviceContent"    column="service_content"    />
        <result property="freeswitchCallId"    column="freeswitch_call_id"    />
        <result property="freeswitchDetailId"    column="freeswitch_detail_id"    />
        <result property="traceId"    column="trace_id"    />
        <result property="state"    column="state"    />
        <result property="distributionTime"    column="distribution_time"    />
        <result property="customerName"    column="customer_name"    />
        <result property="answerState"    column="answer_state"    />
        <result property="conversationTime"    column="conversation_time"    />
        <result property="userNumberphone"    column="user_numberphone"    />
        <result property="handleTime"    column="handle_time"    />
        <result property="handleSecond"    column="handle_second"    />
        <result property="satisfState"    column="satisf_state"    />
        <result property="statisfiedCode"    column="statisfied_code"    />

        <result property="comeFrom"    column="come_from"    />
        <result property="recordType"    column="record_type"    />
        <result property="recordTypeDesc"    column="record_type_desc"    />
        <result property="qualityId"    column="quality_id"    />

        <result property="relationOrder"    column="relation_order"    />
        <result property="minStartTime"    column="min_start_time"    />
        <result property="maxOutTime"    column="max_out_time"    />
        <result property="minCallTime"    column="min_call_time"    />
        <result property="typeDesc"    column="type_desc"    />
        <result property="customerPhone"    column="customer_phone"    />
        <result property="totalConversationTime"    column="total_conversation_time"    />
        <result property="answerStateDesc"    column="answer_state_desc"    />

        <result property="consultLanguageCode"    column="consult_language_code"    />
        <result property="consultLanguage"    column="consult_language"    />
        <result property="helpTypeCode"    column="help_type_code"    />
        <result property="helpType"    column="help_type"    />
        <result property="nature"    column="nature"    />
        <result property="functionalDepartment"    column="functional_department"    />


        <result property="switchObject"    column="switch_object"    />
        <result property="helpObject"    column="help_object"    />
        <result property="multipartyObject"    column="multiparty_object"    />
        <result property="customerPhoneB"    column="customer_phone_b"    />
        <result property="customerNationality"    column="customer_nationality"    />
        <result property="customerLanguage"    column="customer_language"    />
        <result property="allHelpTime"    column="all_help_time"    />
        <result property="targetId"    column="target_id"    />
        <result property="handleResult"    column="handle_result"    />



        <!--<collection property="sonVO"
                    ofType="com.gobon.project.conversation.domain.vo.SonVO"
                    select="getSonVO"
                    column="id" />-->
    </resultMap>


    <select id="selectCallcenterRecordByFreeswitchCallId" resultType="com.gobon.project.conversation.domain.CallcenterRecordDetail">
        SELECT * FROM callcenter_record_detail
        <where>
            freeswitch_call_id = #{callId}
            <if test="@com.gobon.common.utils.StringUtils@isNotEmpty(callPhone)">
                AND call_phone = #{callPhone}
            </if>
            <if test="@com.gobon.common.utils.StringUtils@isNotEmpty(userPhone)">
                AND user_phone = #{userPhone}
            </if>
        </where>
    </select>

    <resultMap id="RM_CallcenterRecordDetail" type="com.gobon.project.conversation.domain.CallcenterRecordDetail">
        <result property="id"    column="id"    />
        <result property="billNumber"    column="bill_number"    />
        <result property="recordId"    column="record_id"    />
        <result property="freeswitchDetailId"    column="freeswitch_detail_id"    />
        <result property="traceId"    column="trace_id"    />
        <result property="type"    column="type"    />
        <result property="ivrRoute"    column="ivr_route"    />
        <result property="businessType"    column="business_type"    />
        <result property="callPhone"    column="call_phone"    />
        <result property="callTime"    column="call_time"    />
        <result property="outIvrTime"    column="out_ivr_time"    />
        <result property="startTime"    column="start_time"    />
        <result property="outTime"    column="out_time"    />
        <result property="switchFlag"    column="switch_flag"    />
        <result property="switchTime"    column="switch_time"    />
        <result property="helpFlag"    column="help_flag"    />
        <result property="helpTime"    column="help_time"    />
        <result property="multipartyFlag"    column="multiparty_flag"    />
        <result property="multipartyTime"    column="multiparty_time"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="userNumber"    column="user_number"    />
        <result property="deptId"    column="dept_id"    />
        <result property="userPhone"    column="user_phone"    />
        <result property="country"    column="country"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="isp"    column="isp"    />
        <result property="customerId"    column="customer_id"    />
        <result property="monitorFlag"    column="monitor_flag"    />
        <result property="monitorStartTime"    column="monitor_start_time"    />
        <result property="monitorEndTime"    column="monitor_end_time"    />
        <result property="monitorUserId"    column="monitor_user_id"    />
        <result property="monitorUserName"    column="monitor_user_name"    />
        <result property="forcedFlag"    column="forced_flag"    />
        <result property="forcedTime"    column="forced_time"    />
        <result property="forcedUserId"    column="forced_user_id"    />
        <result property="forcedUserName"    column="forced_user_name"    />
        <result property="interceptFlag"    column="intercept_flag"    />
        <result property="interceptTime"    column="intercept_time"    />
        <result property="interceptUserId"    column="intercept_user_id"    />
        <result property="interceptUserName"    column="intercept_user_name"    />
        <result property="sound"    column="sound"    />
        <result property="soundText"    column="sound_text"    />
        <result property="satisfId"    column="satisf_id"    />
        <result property="statisfName"    column="statisf_name"    />
        <result property="serviceState"    column="service_state"    />
        <result property="serviceTypeId"    column="service_type_id"    />
        <result property="serviceTypeName"    column="service_type_name"    />
        <result property="serviceContent"    column="service_content"    />
        <result property="freeswitchCallId"    column="freeswitch_call_id"    />
        <result property="state"    column="state"    />
        <result property="distributionTime"    column="distribution_time"    />
        <result property="customerName"    column="customer_name"    />
        <result property="answerState"    column="answer_state"    />
        <result property="conversationTime"    column="conversation_time"    />
        <result property="userNumberphone"    column="user_numberphone"    />
        <result property="handleTime"    column="handle_time"    />
        <result property="handleSecond"    column="handle_second"    />
        <result property="satisfState"    column="satisf_state"    />
        <result property="statisfiedCode"    column="statisfied_code"    />
        <result property="endReason"    column="end_reason"    />
        <result property="transferId"    column="transfer_id"    />
        <result property="recordDetailId"    column="record_detail_id"    />

        <result property="consultLanguageCode"    column="consult_language_code"    />
        <result property="consultLanguage"    column="consult_language"    />
        <result property="helpTypeCode"    column="help_type_code"    />
        <result property="helpType"    column="help_type"    />
        <result property="nature"    column="nature"    />
        <result property="functionalDepartment"    column="functional_department"    />
        <result property="domain"    column="domain"    />
        <result property="targetId"    column="target_id"    />
        <result property="leaveId"    column="leave_id"    />
    </resultMap>


    <select id="selectCallcenterRecordByTraceId" resultMap="RM_CallcenterRecordDetail">
        SELECT * FROM callcenter_record_detail
        <where>
            trace_id = #{traceId}
        </where>
        ORDER BY id ASC
        LIMIT 1
    </select>

    <select id="selectCallcenterRecordLastByTraceId" resultMap="RM_CallcenterRecordDetail">
        SELECT * FROM callcenter_record_detail
        <where>
            trace_id = #{traceId}
        </where>
        ORDER BY call_time DESC
        LIMIT 1
    </select>

    <select id="selectCallcenterRecordLastMonitorByTraceId" resultMap="RM_CallcenterRecordDetail">
        SELECT * FROM callcenter_record_detail
        <where>
                trace_id = #{traceId}
            AND monitor_flag = 1
        </where>
        ORDER BY id DESC
        LIMIT 1
    </select>

    <select id="selectCallcenterRecordByFreeswitchDetailId" resultMap="RM_CallcenterRecordDetail">
        SELECT * FROM callcenter_record_detail
        <where>
            freeswitch_detail_id = #{freeswitchDetailId}
        </where>
        ORDER BY id DESC
        LIMIT 1
    </select>

    <select id="selectCallcenterRecordById" resultMap="RM_CallcenterRecordDetail">
        SELECT * FROM callcenter_record_detail
        <where>
            id = #{id}
        </where>
    </select>

    <sql id="selectCallcenterRecordDetailVo">
        SELECT
            d.id,
            d.bill_number,
            d.record_id,
            d.type,
            CASE
            d.type
            WHEN '0' THEN
            '一对一'
            WHEN '1' THEN
            '转接'
            WHEN '2' THEN
            '求助'
            WHEN '3' THEN
            '多方'
            WHEN '4' THEN
            '拦截'
            WHEN '5' THEN
            '强拆'
            END 'type_desc',
            d.ivr_route,
            d.business_type,
            d.call_time,
            d.out_ivr_time,
            d.start_time,
            d.out_time,

            d.switch_flag,
            CASE
            d.switch_flag
            WHEN '0' THEN
            '否'
            WHEN '1' THEN
            '是'
            END 'switch_flag_desc',
            d.switch_time,
            d.help_flag,
            CASE
            d.help_flag
            WHEN '0' THEN
            '否'
            WHEN '1' THEN
            '是'
            END 'help_flag_desc',
            d.help_time,

            d.multiparty_flag,
            CASE
            d.multiparty_flag
            WHEN '0' THEN
            '否'
            WHEN '1' THEN
            '是'
            END 'multiparty_flag_desc',
            d.multiparty_time,

            d.user_id,
            d.user_name,
            d.user_number,
            d.dept_id,
            d.country,
            d.province,
            d.city,
            concat( d.province, '-', d.city ) come_from,
            d.customer_id,
            d.monitor_flag,
            d.monitor_start_time,
            d.monitor_end_time,
            d.monitor_user_id,
            d.monitor_user_name,
            d.forced_flag,
            d.forced_time,
            d.forced_user_id,
            d.forced_user_name,
            d.intercept_flag,
            d.intercept_time,
            d.intercept_user_id,
            d.intercept_user_name,
            d.sound,
            d.sound_text,
            d.satisf_id,
            ifnull(d.statisf_name, '未评价') statisf_name,

            d.consult_language_code,
            d.consult_language,
            d.help_type_code,
            d.help_type,
            d.nature,
            d.functional_department,

            d.service_type_id,
            d.service_state,
            d.service_type_name,
            d.service_content,
            d.freeswitch_call_id,
            d.freeswitch_detail_id,
            d.trace_id,
            d.state,
            d.distribution_time,
            d.customer_name,
            d.answer_state,
            CASE
            d.answer_state
            WHEN '-1' THEN
            '进入队列'
            WHEN '0' THEN
            '客服未接听'
            WHEN '1' THEN
            '接通'
            WHEN '2' THEN
            '无人应答'
            WHEN '3' THEN
            '群众快速挂机'
            WHEN '4' THEN
            '无法接通'
            WHEN '6' THEN
            '群众未接听'
            END 'answer_state_desc',
            timestampdiff( SECOND, d.start_time, d.out_time ) conversation_time,
            d.user_numberphone,
            d.handle_time,
            d.handle_second,
            d.satisf_state,
            d.statisfied_code,
            d.record_type,
        IF
            ( d.record_type = 0, '呼入', '呼出' ) record_type_desc,
            d.call_phone,
            d.user_phone,
            d.end_person,
            d.record_detail_id,
        IF
            ( d.record_type = 0, d.call_phone, d.user_phone ) customer_phone,
            d.multiparty_id,
            d.target_id,
            (case when left(original_call_telephone,3) = '020' then '广东-广州' else city.city end) intoCity
        FROM
            callcenter_record_detail d
        left join city_prefix city on LEFT(d.original_call_telephone,4) = city.prefix_num
    </sql>

    <select id="selectManagelList" parameterType="com.gobon.project.conversation.domain.param.CallcenterRecordDetailListParam" resultMap="CallcenterRecordDetailResult">
        SELECT
            t.*,
            if(t.customer_id is not null, (select customer_name from callcenter_customer where id = t.customer_id and person_id = t.user_id), null) customer_name,
            if(t.customer_id is not null, (select nationality from callcenter_customer where id = t.customer_id and person_id = t.user_id), null) customer_nationality,
            if(t.customer_id is not null, (select language from callcenter_customer where id = t.customer_id and person_id = t.user_id), null) customer_language,
            if(t.customer_id is not null, (select phone from callcenter_customer where id = t.customer_id and person_id = t.user_id), null) customer_phone_b,

            timestampdiff( SECOND, c.min_start_time, c.max_out_time ) total_conversation_time,
            c.type_desc,
            c.min_call_time,
            c.min_start_time,
            c.max_out_time,
            s.whole_answer_state,
            s.lineCount
        FROM
            (
                select * from
                (
                    SELECT
                    d.record_id,
                    sd.dept_name,
                    GROUP_CONCAT( concat( d.answer_state ) SEPARATOR ',' ) whole_answer_state,
                    timestampdiff( SECOND, d.out_ivr_time, d.distribution_time ) lineCount
                    FROM
                    callcenter_record_detail d
                    INNER JOIN callcenter_record c ON d.record_id = c.id
                    LEFT JOIN callcenter_customer cc ON d.customer_id = cc.id
                    LEFT JOIN sys_user su ON su.user_id = d.user_id
                    left join callcenter_satisfied_info sa on d.satisf_id = sa.id
                    left join sys_dept sd on su.dept_id = sd.dept_id
                    left join ( select * from sys_dict_data where dict_type = 'statisfied_code') dict on sa.statisfied_code = dict.dict_value
                    <where>
                        1 = 1
                        AND d.record_id IS NOT NULL
                        and ( ( (d.type = 2 or d.type = 3) and d.answer_state = 1 ) or (d.type != 2 and d.type != 3 ) )

                        <if test="type != null and type.length > 0"> and d.type in
                            <foreach collection="type" index="index" item="item" open="(" close=")" separator=",">
                                #{item,jdbcType=VARCHAR}
                            </foreach>
                        </if>
                        <if test="businessType != null  and businessType != ''"> and d.business_type = #{businessType}</if>
                        <if test="recordType != null  and recordType != ''"> and c.record_type = #{recordType}</if>
                        <if test="endPerson != null  and endPerson != ''"> and c.end_person = #{endPerson}</if>
                        <if test="serviceState != null  and serviceState != ''"> and d.service_state = #{serviceState}</if>
                        <if test="customerPhone != null  and customerPhone != ''"> and ( c.call_phone = #{customerPhone} or c.user_phone = #{customerPhone} )</if>
                        <if test="customerName != null  and customerName != ''"> and d.customer_name like  concat('%', #{customerName}, '%')</if>

                        <!--<if test="satisfId != null  and satisfId != ''">
                            <choose>
                                <when test='satisfId != "0"'>
                                    and d.satisf_id = #{satisfId}
                                </when>
                                <otherwise>
                                    and d.satisf_id is null
                                </otherwise>
                            </choose>
                        </if>-->
                        <choose>
                            <when test='satisfId !=null and satisfId != "" and satisfId == "not".toString()'>
                                and ( d.satisf_id = 0 or d.satisf_id is null )
                            </when>
                            <when test='satisfId !=null and satisfId != "" and satisfId != "not".toString()'>
                                and dict.dict_value = #{satisfId}
                            </when>
                        </choose>

                        <if test='conversationStartTime !=null and conversationStartTime != "" '>
                            and d.start_time  <![CDATA[>=]]> str_to_date(#{conversationStartTime},'%Y-%m-%d %H:%i:%s')
                        </if>
                        <if test='conversationEndTime !=null and conversationEndTime != "" '>
                            and d.start_time <![CDATA[<=]]> str_to_date(#{conversationEndTime},'%Y-%m-%d %H:%i:%s')
                        </if>

                        <if test="conversationStartSeconds != null  and conversationStartSeconds != ''"> and TIMESTAMPDIFF(second, d.start_time, d.out_time) <![CDATA[>=]]> #{conversationStartSeconds}</if>
                        <if test="conversationEndSeconds != null  and conversationEndSeconds != ''"> and TIMESTAMPDIFF(second, d.start_time, d.out_time) <![CDATA[<=]]> #{conversationEndSeconds}</if>

                        <if test='serviceTypeIds !=null and serviceTypeIds.length > 0 '>
                            <foreach collection="serviceTypeIds" index="index" item="item" open=" and (" separator="or" close=")">
                                d.service_type_id like concat('%',#{item},'%')
                            </foreach>
                        </if>

                        <if test="grade != null  and grade != ''"> and cc.grade = #{grade}</if>
                        <if test='labelIds !=null and labelIds.length > 0 '>
                            <foreach collection="labelIds" index="index" item="item" open=" and (" separator="or" close=")">
                                FIND_IN_SET(#{item},cc.label_ids)
                            </foreach>
                        </if>

                        <!--<if test="deptIds != null  and deptIds.length > 0">
                            <foreach collection="deptIds" index="index" item="item" open=" and (" separator="or" close=")">
                                sd.dept_id = #{item}
                            </foreach>
                        </if>-->
                        <if test="skillIds != null  and skillIds.length > 0">
                            <foreach collection="skillIds" index="index" item="item" open=" and (" separator="or" close=")">
                                FIND_IN_SET(#{item}, su.skill_group_id)
                            </foreach>
                        </if>

                        <if test="userId != null  and userId != ''"> and d.user_id = #{userId}</if>

                        <if test='pageSource != null and pageSource != "" and pageSource == "1"'>
                            <!-- 数据范围过滤 -->
                            ${dataScope}
                        </if>

                        GROUP BY d.record_id
                        order by d.call_time desc
                    </where>
                ) t
                where 1 = 1
                    <if test="answerState != null  and answerState != ''">
                        <choose>
                            <when test='answerState == "1"'>
                                and FIND_IN_SET(#{answerState}, t.whole_answer_state)
                            </when>
                            <otherwise>
                                and !FIND_IN_SET('1', t.whole_answer_state) and FIND_IN_SET(#{answerState}, t.whole_answer_state)
                            </otherwise>
                        </choose>
                    </if>
            ) s
            left JOIN
            (
            SELECT
                d.id,
                c.bill_number,
                d.record_id,
                d.type,
                d.call_phone,
                d.call_time,
                d.start_time,
                d.out_time,

                d.switch_flag,
                CASE
                d.switch_flag
                WHEN '0' THEN
                '否'
                WHEN '1' THEN
                '是'
                END 'switch_flag_desc',
                d.switch_time,

                d.help_flag,
                CASE
                d.help_flag
                WHEN '0' THEN
                '否'
                WHEN '1' THEN
                '是'
                END 'help_flag_desc',
                d.help_time,

                d.multiparty_flag,

                if(ifnull(d.multiparty_time, m.multiparty_time) is not null, '是', '否') multiparty_flag_desc,
                ifnull(d.multiparty_time, m.multiparty_time) multiparty_time,

                d.user_name,
                sd.dept_name,
                p.group_name skill_name,
                d.user_number,
                d.user_phone,
                d.country,
                d.province,
                d.city,
                concat( d.province, '-', d.city ) come_from,
                d.isp,

                d.satisf_id,
                ifnull(d.statisf_name, '未评价') statisf_name,

                d.consult_language_code,

                d.help_type_code,
                d.service_type_id,
                d.service_state,
                if(c.record_type = 0, c.call_phone, c.user_phone) customer_phone,

                <choose>
                    <when test="exportSign !=null and exportSign != ''">
                        if(d.switch_flag = 1, (select user_name from callcenter_record_detail where record_detail_id = d.id and type = 1 order by call_time limit 1), null) switch_object,
                        if(d.switch_flag = 1, (select user_id from callcenter_record_detail where record_detail_id = d.id and type = 1 order by call_time limit 1), d.user_id) user_id,
                        if(d.switch_flag = 1, (select customer_id from callcenter_record_detail where record_detail_id = d.id and type = 1 order by call_time limit 1), d.customer_id) customer_id,
                        if(d.switch_flag = 1, (select consult_language from callcenter_record_detail where record_detail_id = d.id and type = 1 order by call_time limit 1), d.consult_language) consult_language,
                        if(d.switch_flag = 1, (select help_type from callcenter_record_detail where record_detail_id = d.id and type = 1 order by call_time limit 1), d.help_type) help_type,
                        if(d.switch_flag = 1, (select nature from callcenter_record_detail where record_detail_id = d.id and type = 1 order by call_time limit 1), d.nature) nature,
                        if(d.switch_flag = 1, (select functional_department from callcenter_record_detail where record_detail_id = d.id and type = 1 order by call_time limit 1), d.functional_department) functional_department,
                        if(d.switch_flag = 1, (select service_type_name from callcenter_record_detail where record_detail_id = d.id and type = 1 order by call_time limit 1), d.service_type_name) service_type_name,
                        if(d.switch_flag = 1, (select service_content from callcenter_record_detail where record_detail_id = d.id and type = 1 order by call_time limit 1), d.service_content) service_content,
                    </when>
                    <otherwise>
                        d.user_id,
                        d.customer_id,
                        d.consult_language,
                        d.help_type,
                        d.nature,
                        d.functional_department,
                        d.service_type_name,
                        d.service_content,
                    </otherwise>
                </choose>

                d.state,
                d.distribution_time,

                d.answer_state,
                CASE
                <choose>
                    <when test='answerState != null  and answerState !="" and answerState == "1"'>
                        #{answerState}
                    </when>
                    <otherwise>
                        d.answer_state
                    </otherwise>
                </choose>
                WHEN '-1' THEN
                '进入队列'
                WHEN '0' THEN
                '客服未接听'
                WHEN '1' THEN
                '接通'
                WHEN '2' THEN
                '无人应答'
                WHEN '3' THEN
                '群众快速挂机'
                WHEN '4' THEN
                '无法接通'
                WHEN '6' THEN
                '群众未接听'
                END 'answer_state_desc' ,

                d.user_numberphone,
                d.handle_time,
                d.handle_second,
                d.satisf_state,
                d.statisfied_code,
                timestampdiff( SECOND, d.start_time, d.out_time ) conversation_time,
                c.record_type,
                IF( c.record_type = 0, '呼入', '呼出' ) record_type_desc,
                c.end_person,
                d.freeswitch_call_id,
                d.record_detail_id,
                m.help_object,
                m.multiparty_object,
                d.original_call_telephone,
                m.all_help_time
            FROM
                callcenter_record_detail d
                INNER JOIN callcenter_record c ON d.record_id = c.id
                left join sys_user su on su.user_id = d.user_id
                left join sys_skill_group p on c.ivr_skill_group_id = p.id
                left join sys_dept sd on su.dept_id = sd.dept_id
                LEFT JOIN callcenter_customer cc ON d.customer_id = cc.id
                left join (
                    SELECT
                        record_detail_id ,
                        type,
                        MAX(CASE type WHEN '2' THEN name ELSE null END ) help_object,
                        MAX(CASE type WHEN '3' THEN name ELSE null END ) multiparty_object,
                        MAX(CASE type WHEN '3' THEN multiparty_time ELSE null END ) multiparty_time,
                        all_help_time
                    FROM
                    (
                        SELECT
                            m.record_detail_id,
                            group_concat( if(m.type = 2, m.no, '') , m.nick_name SEPARATOR '，') name,
                            group_concat( IF ( m.type = 2, m.NO, '' ), m.create_time SEPARATOR '，' ) all_help_time,
                            m.multiparty_time,
                            m.type
                        from
                        (
                            SELECT
                                @r := CASE WHEN @rank = m.record_detail_id THEN concat( @r + 1, '.' ) ELSE concat( 1, '.' ) END AS no,
                                @rank := m.record_detail_id record_detail_id,
                                m.nick_name,
                                m.create_time,
                                m.multiparty_time,
                                m.type
                            FROM
                            (
                                SELECT
                                    record_detail_id,
                                    type,
                                    create_time,
                                    multiparty_id,
                                    multiparty_time,
                                    group_concat( nick_name  , if(nick_name is not null, "(" , '')  , ifnull(user_number, '') ,if(nick_name is not null, ")" , '') SEPARATOR '、' ) nick_name
                                from
                                (
                                    SELECT
                                        DISTINCT
                                        if(d.record_detail_id is not null, d.record_detail_id, d.id) record_detail_id,
                                        m.type,
                                        if(m.type = 2, m.create_time,null) create_time,
                                        if(m.type = 3, d.multiparty_time,null) multiparty_time,
                                        md.multiparty_id,
                                        su.nick_name,
                                        su.user_number
                                    FROM
                                    callcenter_multiparty m
                                    LEFT JOIN callcenter_record_detail d on m.record_detail_id = d.id
                                    LEFT JOIN callcenter_multiparty_detail md ON m.id = md.multiparty_id
                                    LEFT JOIN sys_user su ON md.user_id = su.user_id and m.create_user_id != su.user_id
                                    WHERE
                                    m.type != 1
                                ) x GROUP BY multiparty_id
                            ) m

                        ) m
                        group by m.type	,m.record_detail_id
                    ) t
                    GROUP BY record_detail_id
                ) m on m.record_detail_id = d.id
            WHERE
                1 = 1
                AND d.type = '0'
            ORDER BY
                d.call_time DESC

            ) t
            on s.record_id = t.record_id
            LEFT JOIN (
            SELECT
                d.record_id,
                MIN( d.call_time ) min_call_time,
                MIN( d.start_time ) min_start_time,
                MAX( d.out_time ) max_out_time,
                group_concat( DISTINCT ( d.type_desc ) SEPARATOR ',' ) type_desc
            FROM
                (
                SELECT
                    d.id,
                    d.record_id,
                    d.call_time,
                    d.start_time,
                    d.out_time,
                CASE
                    d.type
                    WHEN '0' THEN
                    '一对一'
                    WHEN '1' THEN
                    '转接'
                    WHEN '2' THEN
                    '求助'
                    WHEN '3' THEN
                    '多方'
                    WHEN '4' THEN
                    '拦截'
                    WHEN '5' THEN
                    '强拆'
                END 'type_desc'
        FROM
            callcenter_record_detail d
        where  ( ( ( d.type = 2 OR d.type = 3 ) AND d.answer_state = 1 )  OR ( d.type != 2 AND d.type != 3 )  )
        ) d
        GROUP BY
            d.record_id
        ) c ON t.record_id = c.record_id
    </select>

    <select id="selectSeatsList" parameterType="com.gobon.project.conversation.domain.param.CallcenterRecordDetailListParam" resultType="com.gobon.project.conversation.domain.vo.CallcenterRecordDetailSimpleVO">
        select
        d.*, d.conversation_time_1 conversation_time, '广东-广州' localCity,
        if(d.record_type = '1', '0', if(d.line_counts is null, '0', d.line_counts) ) line_count
        from (
            select d.id,
            d.bill_number,
            d.trace_id,
            d.record_id,
            d.record_type,
            IF( d.record_type = 0, '呼入', '呼出' ) record_type_desc,
            d.start_time,
            d.out_time,
            d.call_time,
            d.call_time min_call_time,
            d.call_phone,
            d.user_phone,
            ifnull(td.area_phone, d.original_call_telephone) original_call_telephone,
            concat( d.province, '-', d.city ) come_from,
            d.answer_state,
            CASE
                d.answer_state
                WHEN '-1' THEN
                '进入队列'
                WHEN '0' THEN
                '客服未接听'
                WHEN '1' THEN
                '接通'
                WHEN '2' THEN
                '无人应答'
                WHEN '3' THEN
                '群众快速挂机'
                WHEN '4' THEN
                '无法接通'
                WHEN '6' THEN
                '群众未接听'
            END 'answer_state_desc' ,
            d.type,
            CASE
                    d.type
                    WHEN '0' THEN
                    '一对一'
                    WHEN '1' THEN
                    '转接'
                    WHEN '2' THEN
                    '求助'
                    WHEN '3' THEN
                    '多方'
                    WHEN '4' THEN
                    '拦截'
                    WHEN '5' THEN
                    '强拆'
            END 'type_desc' ,
            ifnull(timestampdiff( SECOND, d.start_time, d.out_time ), 0) conversation_time_1,
            d.out_ivr_time,
            d.distribution_time,
            sd.dept_name,
            if(d.answer_state in (1, 3), bsb.status, null) as service_state,
            timestampdiff(SECOND, d.out_ivr_time, ifnull(d.distribution_time, IFNULL(start_time, out_time))) line_counts,
            su.nick_name user_name, d.user_number, su.belong_city,
            replace(ifnull(d.statisf_name, '未评价'), '一般', '基本满意') statisf_name,
            if(d.record_type = '1', '', sg.group_name ) group_name,
            (case when d2.transfer_id is not null then '是' else '否' end) transferFlag,
            (case when d2.transfer_id is not null then '1' else '0' end) transferFlagCode,
            (case when d2.transfer_id is not null then d2.user_name else '' end) transferUser,
            if(TIMESTAMPDIFF(MINUTE, bsb.update_time, now()) <![CDATA[<=]]> 1440, '1', '0') canFill,
            d.switch_time transfer_time,
            ifnull(d.service_type_id, ifnull(td2.desc, ifnull(td.desc, if(original_call_telephone = '12348', '广东-广州', city.city)))) intoCity,
            ifnull(d.service_type_id, ifnull(td2.desc, ifnull(td.desc, ifnull((case when original_call_telephone = '12348' then '广东-广州' else city.city end), concat( d.province, '-', d.city ))))) concatCity,
            d.ivr_skill_group_name skill_name,
            <choose>
                <when test="integrationSign == '1'.toString()">
                    if(ifnull(real_call_state, end_person) = '1', '坐席', if(ifnull(real_call_state, end_person) = '0', '群众', if(ifnull(real_call_state, end_person) = '3', '系统异常挂机', '系统正常挂机'))) end_person,
                </when>
                <otherwise>
                    if(end_person = '1', '坐席', if(end_person = '0', '群众', if(end_person = '3', '系统异常挂机', '系统正常挂机'))) end_person,
                </otherwise>
            </choose>
            if(su.user_type = '1', '律师', if(su.user_type = '2', '客服', null)) role_name,
            bsb.handle_result,
            bsb.consult_content
            from callcenter_record_detail d
            LEFT JOIN (select transfer_id, ifnull(user_name, user_phone) user_name from callcenter_record_detail where type = 1
            <if test='conversationStartTime !=null and conversationStartTime != "" '>
                and call_time  <![CDATA[>=]]> str_to_date(#{conversationStartTime},'%Y-%m-%d %H:%i:%s')
            </if>
            <if test='conversationEndTime !=null and conversationEndTime != "" '>
                and call_time <![CDATA[<=]]> str_to_date(#{conversationEndTime},'%Y-%m-%d %H:%i:%s')
            </if>
            group by transfer_id
            ) d2 on d.id = d2.transfer_id
            left join sys_user su on d.user_id = su.user_id
            left join sys_skill_group sg on d.ivr_skill_group_id = sg.id
            left join sys_dept sd on su.dept_id = sd.dept_id
            left join city_prefix city on d.original_call_telephone = concat(prefix_num, '12348')
            left join t_third_phone td on d.original_call_telephone = td.real_phone
            left join t_third_phone td2 on d.call_phone = td2.real_phone
            left join bus_standing_book bsb on d.id = bsb.business_id
            <where>
                <if test="userId == null">
                    and ((call_time >= '2024-08-01 00:00:00' and (original_call_telephone is null or original_call_telephone not in (select phone from t_original_phone where use_flag = '0'))) or call_time <![CDATA[<]]> '2024-08-01 00:00:00')
                </if>
                <if test="type != null and type.length > 0">
                    and d.type in
                    <foreach collection="type" index="index" item="item" open="(" close=")" separator=",">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </if>

                <if test="integrationSign != null  and integrationSign != ''"> and d.type = '0' </if>

                <if test="answerState != null and answerState != ''"> and d.answer_state = #{answerState}</if>
                <if test="userNumber != null and userNumber != ''">
                 and (d.user_number = #{userNumber}
                    or su.nick_name like concat('%', #{userNumber}, '%')
                    or su.user_name like concat('%', #{userNumber}, '%')
                 )
                </if>
                <if test="businessType != null  and businessType != ''"> and d.business_type = #{businessType}</if>
                <if test="recordType != null  and recordType != ''"> and d.record_type = #{recordType}</if>
                <if test="serviceState != null  and serviceState == '3'.toString()"> and (bsb.status is null or d.answer_state = 0)</if>
                <if test="serviceState != null  and serviceState == '0'.toString()"> and (bsb.status = '0' and d.answer_state in (1, 3))</if>
                <if test="serviceState != null  and serviceState == '1'.toString()"> and (bsb.status = '1' and d.answer_state in (1, 3))</if>
                <if test="customerPhone != null  and customerPhone != ''"> and ( d.call_phone = #{customerPhone} AND d.answer_state = 1)</if>
                <if test="customerName != null  and customerName != ''"> and d.customer_name like  concat('%', #{customerName}, '%')</if>
                <if test="endPerson != null and endPerson != ''">
                    <choose>
                        <when test="integrationSign == '1'.toString()">
                            <choose>
                                <when test="endPerson == '3'.toString()">
                                    and d.id in (
                                    select id from callcenter_record_detail d
                                    where end_person = '3'
                                    <if test="recordType != null  and recordType != ''"> and d.record_type = #{recordType}</if>
                                    <if test='conversationStartTime !=null and conversationStartTime != "" '>
                                        and call_time  <![CDATA[>=]]> str_to_date(#{conversationStartTime},'%Y-%m-%d %H:%i:%s')
                                    </if>
                                    <if test='conversationEndTime !=null and conversationEndTime != "" '>
                                        and call_time <![CDATA[<=]]> str_to_date(#{conversationEndTime},'%Y-%m-%d %H:%i:%s')
                                    </if>
                                    )
                                </when>
                                <otherwise>
                                    and ifnull(real_call_state, end_person) = #{endPerson}
                                </otherwise>
                            </choose>
                        </when>
                        <otherwise>
                            and end_person = #{endPerson}
                        </otherwise>
                    </choose>
                </if>
                <choose>
                    <when test='satisfId !=null and satisfId != "" and satisfId == "not".toString()'>
                        and ( d.satisf_id = 0 or d.satisf_id is null )
                    </when>
                    <when test='satisfId !=null and satisfId != "" and satisfId != "not".toString()'>
                        and d.statisf_name = #{satisfId}
                    </when>
                </choose>

                <if test='conversationStartTime !=null and conversationStartTime != "" '>
                    and d.call_time  <![CDATA[>=]]> str_to_date(#{conversationStartTime},'%Y-%m-%d %H:%i:%s')
                </if>
                <if test='conversationEndTime !=null and conversationEndTime != "" '>
                    and d.call_time <![CDATA[<=]]> str_to_date(#{conversationEndTime},'%Y-%m-%d %H:%i:%s')
                </if>
                <if test="conversationStartSeconds != null  and conversationStartSeconds != ''"> and TIMESTAMPDIFF(second, d.start_time, d.out_time) <![CDATA[>=]]> #{conversationStartSeconds}</if>
                <if test="conversationEndSeconds != null  and conversationEndSeconds != ''"> and TIMESTAMPDIFF(second, d.start_time, d.out_time) <![CDATA[<=]]> #{conversationEndSeconds}</if>

                <if test='serviceTypeIds !=null and serviceTypeIds.length > 0 '>
                    <foreach collection="serviceTypeIds" index="index" item="item" open=" and (" separator="or" close=")">
                        d.service_type_id like concat('%',#{item},'%')
                    </foreach>
                </if>
                <if test='statisfName !=null and statisfName.length > 0 '>
                    AND d.statisf_name in
                    <foreach collection="statisfName" index="index" item="item" open=" (" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="grade != null  and grade != ''"> and cc.grade = #{grade}</if>
                <if test='labelIds !=null and labelIds.length > 0 '>
                    <foreach collection="labelIds" index="index" item="item" open=" and (" separator="or" close=")">
                        FIND_IN_SET(#{item},cc.label_ids)
                    </foreach>
                </if>
                <if test="skillIds != null  and skillIds.length > 0">
                    and d.ivr_skill_group_id in
                    <foreach collection="skillIds" index="index" item="item" open=" (" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

                <if test="userId != null  and userId != ''"> and d.user_id = #{userId}</if>

                <if test="userPhone != null  and userPhone != ''">
                 and d.user_phone like concat('%', #{userPhone}, '%')
                 </if>
                <if test="callPhone != null  and callPhone != ''">
                 and ( d.call_phone like concat('%', #{callPhone}, '%') or d.user_phone like concat('%', #{callPhone}, '%') or d.original_call_telephone like concat('%', #{callPhone}, '%') )
                 </if>
                <if test="originalCallTelephone != null  and originalCallTelephone != ''">
                 and d.original_call_telephone like concat('%', #{originalCallTelephone}, '%')
                 </if>

                <if test="billNumber != null  and billNumber != ''">
                    and d.bill_number like concat('%', #{billNumber}, '%')
                </if>
            </where>
        ) d

        <where>
            <if test="transferFlagCode != null and transferFlagCode != ''">
                and d.transferFlagCode = #{transferFlagCode}
            </if>

            <if test="comeFrom != null and comeFrom != ''">
                and d.come_from like concat('%', #{comeFrom}, '%')
            </if>
        </where>
        <choose>
            <when test="userNumber != null and userNumber != ''">
                order by d.id desc
            </when>
            <otherwise>
                order by d.record_id desc, call_time asc
            </otherwise>
        </choose>

    </select>
    <select id="selectSeatsList_COUNT" parameterType="com.gobon.project.conversation.domain.param.CallcenterRecordDetailListParam" resultType="Long">
        select count(1)
        from callcenter_record_detail d
        left join sys_user su on d.user_id = su.user_id
        left join sys_skill_group sg on d.ivr_skill_group_id = sg.id
        left join sys_dept sd on su.dept_id = sd.dept_id
        left join bus_standing_book bsb on d.id = bsb.business_id
        <where>
            <if test="userId == null">
               and ((call_time >= '2024-08-01 00:00:00' and (original_call_telephone is null or original_call_telephone not in (select phone from t_original_phone where use_flag = '0'))) or call_time <![CDATA[<]]> '2024-08-01 00:00:00')
            </if>
            <if test="type != null and type.length > 0">
                and d.type in
                <foreach collection="type" index="index" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>

            <if test="serviceState != null  and serviceState == '3'.toString()"> and (bsb.status is null or d.answer_state = 0)</if>
            <if test="serviceState != null  and serviceState == '0'.toString()"> and (bsb.status = '0' and d.answer_state in (1, 3))</if>
            <if test="serviceState != null  and serviceState == '1'.toString()"> and (bsb.status = '1' and d.answer_state in (1, 3))</if>
            <if test="integrationSign != null  and integrationSign != ''"> and d.type = '0' </if>

            <if test="answerState != null and answerState != ''"> and d.answer_state = #{answerState}</if>
            <if test="userNumber != null and userNumber != ''">
             and (d.user_number = #{userNumber}
                or su.nick_name like concat('%', #{userNumber}, '%')
                or su.user_name like concat('%', #{userNumber}, '%')
             )
            </if>
            <if test="businessType != null  and businessType != ''"> and d.business_type = #{businessType}</if>
            <if test="recordType != null  and recordType != ''"> and d.record_type = #{recordType}</if>
            <if test="customerPhone != null  and customerPhone != ''"> and ( d.call_phone = #{customerPhone} or d.user_phone = #{customerPhone} )</if>
            <if test="customerName != null  and customerName != ''"> and d.customer_name like  concat('%', #{customerName}, '%')</if>
            <if test="endPerson != null and endPerson != ''">
                <choose>
                    <when test="integrationSign == '1'.toString()">
                        <choose>
                            <when test="endPerson == '3'.toString()">
                                and d.id in (
                                select id from callcenter_record_detail d
                                where end_person = '3'
                                    <if test="recordType != null  and recordType != ''"> and d.record_type = #{recordType}</if>
                                    <if test='conversationStartTime !=null and conversationStartTime != "" '>
                                        and call_time  <![CDATA[>=]]> str_to_date(#{conversationStartTime},'%Y-%m-%d %H:%i:%s')
                                    </if>
                                    <if test='conversationEndTime !=null and conversationEndTime != "" '>
                                        and call_time <![CDATA[<=]]> str_to_date(#{conversationEndTime},'%Y-%m-%d %H:%i:%s')
                                    </if>
                                )
                            </when>
                            <otherwise>
                                and ifnull(real_call_state, end_person) = #{endPerson}
                            </otherwise>
                        </choose>
                    </when>
                    <otherwise>
                        and end_person = #{endPerson}
                    </otherwise>
                </choose>
            </if>
            <if test='statisfName !=null and statisfName.length > 0 '>
                AND d.statisf_name in
                <foreach collection="statisfName" index="index" item="item" open=" (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <choose>
                <when test='satisfId !=null and satisfId != "" and satisfId == "not".toString()'>
                    and ( d.satisf_id = 0 or d.satisf_id is null )
                </when>
                <when test='satisfId !=null and satisfId != "" and satisfId != "not".toString()'>
                    and d.statisf_name = #{satisfId}
                </when>
            </choose>

            <if test='conversationStartTime !=null and conversationStartTime != "" '>
                and d.call_time  <![CDATA[>=]]> str_to_date(#{conversationStartTime},'%Y-%m-%d %H:%i:%s')
            </if>
            <if test='conversationEndTime !=null and conversationEndTime != "" '>
                and d.call_time <![CDATA[<=]]> str_to_date(#{conversationEndTime},'%Y-%m-%d %H:%i:%s')
            </if>
            <if test="conversationStartSeconds != null  and conversationStartSeconds != ''"> and TIMESTAMPDIFF(second, d.start_time, d.out_time) <![CDATA[>=]]> #{conversationStartSeconds}</if>
            <if test="conversationEndSeconds != null  and conversationEndSeconds != ''"> and TIMESTAMPDIFF(second, d.start_time, d.out_time) <![CDATA[<=]]> #{conversationEndSeconds}</if>


            <if test="skillIds != null  and skillIds.length > 0">
                and d.ivr_skill_group_id in
                <foreach collection="skillIds" index="index" item="item" open=" (" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="userId != null  and userId != ''"> and d.user_id = #{userId}</if>

            <if test="userPhone != null  and userPhone != ''">
             and d.user_phone like concat('%', #{userPhone}, '%')
             </if>
            <if test="callPhone != null  and callPhone != ''">
             and ( d.call_phone like concat('%', #{callPhone}, '%') or d.user_phone like concat('%', #{callPhone}, '%') or d.original_call_telephone like concat('%', #{callPhone}, '%') )
             </if>
            <if test="originalCallTelephone != null  and originalCallTelephone != ''">
             and d.original_call_telephone like concat('%', #{originalCallTelephone}, '%')
             </if>

            <if test="billNumber != null  and billNumber != ''">
                and d.bill_number like concat('%', #{billNumber}, '%')
            </if>
        </where>
    </select>

    <select id="getMonitorList" parameterType="com.gobon.project.conversation.domain.param.MonitorParam" resultType="com.gobon.project.conversation.domain.vo.MonitorConversationVO">
        select t.*,
        ifnull(c.incoming_call, 0) incoming_call,
        ifnull(c.dial, 0) dial,
        ifnull(f.online_time,0) on_line_time
        from
        (
            SELECT su.user_id, su.user_number, su.user_name, su.dept_id
            , if(sd.dept_service_state = '1', sd.dept_name, null) dept_name
            FROM  sys_user su
            left join sys_dept sd on su.dept_id = sd.dept_id
            where
            1 = 1
            and su.user_number in
            <foreach collection="userNumbers" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
            <!-- 数据范围过滤 -->
            ${dataScope}
        ) t
        left join
        (
            select
            t.user_id,
            count(if(t.record_type='0',true,null )) incoming_call,
            count(if(t.record_type='1',true,null )) dial
            from callcenter_record_detail t
            where
            1 = 1
            and t.user_number in
            <foreach collection="userNumbers" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>

             and TO_DAYS(t.call_time) = TO_DAYS(now()) group by t.user_id
           ) c
        on t.user_id = c.user_id
        left join
            (
            select t.user_id,t.online_time from user_statistics t where  t.option_state_code = '1' and TO_DAYS(option_state_time) = TO_DAYS(NOW())
            ) f
        on t.user_id = f.user_id

    </select>

    <select id="selectCallcenterRecordDetailById" parameterType="Long" resultMap="CallcenterRecordDetailResult">
        <include refid="selectCallcenterRecordDetailVo"/>
        where d.id = #{id}
    </select>

    <select id="selectManagelById" parameterType="Long" resultMap="CallcenterRecordDetailResult">
        SELECT
        t.*,
        timestampdiff( SECOND, c.min_start_time, c.max_out_time ) total_conversation_time,
        c.type_desc,
        c.min_call_time,
        c.min_start_time,
        c.max_out_time
        FROM
        (
        SELECT
        d.id,
        d.record_id,
        d.type,
        d.call_phone,
        d.call_time,
        d.start_time,
        d.out_time,
        d.sound,
        d.sound_text,
        d.multiparty_flag,
        d.multiparty_time,
        d.user_id,
        d.user_name,
        d.user_number,
        d.user_phone,
        d.country,
        d.province,
        d.city,
        concat( d.province, '-', d.city ) come_from,
        d.isp,
        d.customer_id,
        d.satisf_id,
        d.statisf_name,
        d.consult_language_code,
        d.consult_language,
        d.help_type_code,
        d.help_type,
        d.nature,
        d.functional_department,
        d.service_type_id,
        d.service_state,
        d.service_type_name,
        d.service_content,
        d.state,
        d.distribution_time,
        d.customer_name,
        d.answer_state,
        d.user_numberphone,
        d.handle_time,
        d.handle_second,
        d.satisf_state,
        d.statisfied_code,
        timestampdiff( SECOND, d.start_time, d.out_time ) conversation_time,
        c.record_type,
        IF
        ( c.record_type = 0, '呼入', '呼出' ) record_type_desc,
        c.end_person,
        d.freeswitch_call_id,
        d.record_detail_id,
        if(c.record_type = 0, c.call_phone, c.user_phone) customer_phone,
        d.multiparty_id
        FROM
        callcenter_record_detail d
        INNER JOIN callcenter_record c ON d.record_id = c.id
        LEFT JOIN callcenter_customer cc ON d.customer_id = cc.id
        LEFT JOIN sys_user su ON su.user_id = d.user_id
        LEFT JOIN sys_dept sd ON sd.dept_id = su.dept_id

        <where>
            1 = 1
            and d.id =  #{id}
        </where>
        ) t
        LEFT JOIN (
        SELECT
        d.record_id,
        MIN( d.call_time ) min_call_time,
        MIN( d.start_time ) min_start_time,
        MAX( d.out_time ) max_out_time,
        group_concat( DISTINCT ( d.type_desc ) SEPARATOR ',' ) type_desc
        FROM
        (
        SELECT
        d.id,
        d.record_id,
        d.call_time,
        d.out_time,
        d.start_time,
        CASE
        d.type
        WHEN '0' THEN
        '一对一'
        WHEN '1' THEN
        '转接'
        WHEN '2' THEN
        '求助'
        WHEN '3' THEN
        '多方'
        WHEN '4' THEN
        '拦截'
        WHEN '5' THEN
        '强拆'
        END 'type_desc'
        FROM
        callcenter_record_detail d
        ) d
        GROUP BY
        d.record_id
        ) c ON t.record_id = c.record_id
    </select>

    <select id="selectCallcenterListByPhone" parameterType="com.gobon.project.conversation.domain.param.CallcenterRecordDetailListParam" resultMap="CallcenterRecordDetailResult">
        select t.* from
        (
            SELECT
            d.id,
            d.record_id,
            d.bill_number,
            d.call_time,
            d.call_phone,
            d.out_time,
            d.start_time,
            CASE
            d.type
            WHEN '0' THEN
            '一对一'
            WHEN '1' THEN
            '转接'
            WHEN '2' THEN
            '求助'
            WHEN '3' THEN
            '多方'
            WHEN '4' THEN
            '拦截'
            WHEN '5' THEN
            '强拆'
            END 'type_desc',
            d.statisfied_code,
            d.user_name,
            d.user_number,
            d.user_phone,
            bsb.consult_content as service_content,
            bsb.handle_result,
            d.sound,
            timestampdiff( SECOND, d.start_time, d.out_time ) conversation_time,
            d.record_type
            FROM
            callcenter_record_detail d
            left join `bus_standing_book` bsb on d.id = bsb.business_id
            where (d.call_phone = #{customerPhone} or d.user_phone = #{customerPhone}) and d.answer_state = 1
            and d.user_id is not null
            <if test="id != null">
                and d.id != #{id}
            </if>
        ) t
        where 1 = 1
        <if test="vagueContent != null  and vagueContent != ''">
         and t.type_desc like concat('%', #{vagueContent}, '%')
         or t.service_content like concat('%', #{vagueContent}, '%')
         </if>
        order by t.call_time  desc
    </select>

    <select id="getStayList" parameterType="com.gobon.project.conversation.domain.param.CallcenterRecordDetailListParam" resultType="com.gobon.project.conversation.domain.vo.StaySubmitListVO">
        select * from
        (
        select d.id,  d.customer_name , if(d.record_type = '0', d.call_phone, d.user_phone) phone, d.call_time, d.start_time,
        if(d.record_type = 0, '呼入', '呼出') record_type_desc,
        bsb.status service_state
        from callcenter_record_detail d
        left join `bus_standing_book` bsb  on d.id = bsb.business_id
        where d.user_id = #{userId} and d.answer_state in (1, 3) and bsb.`status` = '0'  order by d.id desc limit 15

        ) t
        left join
        (
        SELECT
            count( 1 ) total
        FROM
            callcenter_record_detail d
            left join `bus_standing_book` bsb  on d.id = bsb.business_id
        WHERE
            d.user_id = #{userId} and bsb.`status` = '0' and d.answer_state in (1, 3) and call_time > DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 60 day), '%Y-%m-%d %H:%i:%s')
        ) c on 1 = 1
    </select>

    <select id="getCallcenterMultipartyVO" parameterType="com.gobon.project.conversation.domain.param.CallcenterMultipartyParam" resultType="com.gobon.project.conversation.domain.vo.CallcenterMultipartyVO">
        SELECT
            t.*,
            ifnull(c.type_all, 1) type_all
        FROM
            (
        SELECT
            d.id,
            d.type,
            d.call_phone,
            d.call_time,
            d.start_time,
            d.out_time,
            d.user_id,
            if(d.third_party = 1, d.user_phone, CONCAT( su.nick_name, "(", d.user_number, ")" )) user_name,
            d.user_phone, d.sound, d.freeswitch_call_id,
            d.user_number,
            c.record_type,
            d.answer_state,
            IF(d.multiparty_id is null, d.id, d.multiparty_id ) multiparty_id,
            d.third_party,
            que.queue_name
        FROM
            callcenter_record_detail d
            left join callcenter_shortcut_queue que on d.hot_line_id = que.id
            left join
            callcenter_record c on d.record_id = c.id
            left join
            sys_user su on su.user_id = d.user_id
        WHERE
            1 = 1
            and d.id != #{id}
            AND d.record_id = #{recordId}
            and ( ( (d.type = 2 or d.type = 3) and d.answer_state = 1 ) or (d.type != 2 and d.type != 3 ) )
            <if test='sqlSign == null  or sqlSign == ""'>
                and d.record_detail_id = #{id}
                and d.type not in
                <foreach collection="types" index="index" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>

            ) t
            LEFT JOIN (
                select
                    d.type,
                    count( d.type ) type_all,
                    d.multiparty_id
                from
                (
                    SELECT
                        d.type,
                        IF(d.multiparty_id is null, d.id, d.multiparty_id ) multiparty_id
                    FROM
                        callcenter_record_detail d,
                        callcenter_record c
                    WHERE
                        d.record_id = c.id
                        and d.id != #{id}
                        AND d.record_id = #{recordId}
                        and ( ( (d.type = 2 or d.type = 3) and d.answer_state = 1 ) or (d.type != 2 and d.type != 3 ) )
                        <if test='sqlSign == null  or sqlSign == ""'>
                            and d.record_detail_id = #{id}
                            and d.type not in
                            <foreach collection="types" index="index" item="item" open="(" close=")" separator=",">
                                #{item,jdbcType=BIGINT}
                            </foreach>
                        </if>
                )  d GROUP BY  d.multiparty_id
            ) c ON t.multiparty_id = c.multiparty_id
        ORDER BY
	        t.call_time ASC
    </select>


    <select id="getCallcenterMultipartyVOByMultipartyId" parameterType="com.gobon.project.conversation.domain.param.CallcenterMultipartyParam" resultType="com.gobon.project.conversation.domain.vo.CallcenterMultipartyVO">
        SELECT
            t.*,
            c.type_all
        FROM
            (
            SELECT
                d.id, d.type, d.call_phone, d.call_time, d.start_time, d.out_time,
                d.user_id,
                if(d.third_party = 1, d.user_phone, CONCAT( su.nick_name, "(", d.user_number, ")" )) user_name,
                d.user_phone, d.sound, d.freeswitch_call_id, d.multiparty_id,
                d.user_number,
                d.sound_text,
                d.third_party,
                que.queue_name
            FROM
                callcenter_record_detail d
            left join callcenter_shortcut_queue que on d.hot_line_id = que.id
            left join
                sys_user su on su.user_id = d.user_id
            WHERE
                d.multiparty_id = #{multipartyId}
                and d.answer_state = 1
            order by d.start_time ASC
            ) t
        LEFT JOIN (
            SELECT
                count( d.type ) type_all ,
                d.multiparty_id
            FROM
                callcenter_record_detail d
            WHERE
                d.multiparty_id = #{multipartyId}
                and d.answer_state = 1
            GROUP BY d.multiparty_id
        ) c ON t.multiparty_id = c.multiparty_id
    </select>

    <select id="getSoundVO" parameterType="long" resultType="com.gobon.project.conversation.domain.vo.SoundVO">
        SELECT
            d.id original_call_detail_id,
            d.type,
            d.multiparty_id,
            d.sound,
            d.sound_text
        FROM
            callcenter_record_detail d,
            callcenter_record c
        WHERE
            d.record_id = c.id
            and d.id != #{id}
            AND d.record_id = #{recordId}
            and d.record_detail_id = #{id}
            and ( ( (d.type = 2 or d.type = 3) and d.answer_state = 1 ) or (d.type != 2 and d.type != 3 ) )
            and d.type not in
            <foreach collection="types" index="index" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>

        GROUP BY
            d.multiparty_id  order by	d.start_time ASC
    </select>

    <select id="getRelationVO" parameterType="com.gobon.project.conversation.domain.param.CallcenterMultipartyParam" resultType="com.gobon.project.conversation.domain.vo.RelationVO">
        SELECT
            c.type son_type,
            c.userName son_user_name,
            IF(c.userName is not null,t.passiveUserName, null) son_passive_user_name,
            s.*
        FROM
            ( SELECT d.id, d.user_name passiveUserName FROM callcenter_record_detail d WHERE d.id = #{id} ) t
            LEFT JOIN ( SELECT d.record_detail_id, d.type, d.user_name userName FROM callcenter_record_detail d WHERE d.record_detail_id = #{id} and d.type in (1, 4, 5) order by d.call_time asc limit 1 ) c ON t.id = c.record_detail_id
            LEFT JOIN (
                SELECT
                    d.call_phone parent_call_phone,
                    d.user_phone parent_user_phone,
                    d.call_time parent_call_time,
                    d.start_time parent_start_time,

                    timestampdiff( SECOND, d.start_time, d.out_time ) parent_conversation_time,

                    d.out_time parent_out_time,
                    concat( d.province, '-', d.city ) come_from,
                    d.user_name parent_user_name,
                    d.statisf_name parent_statisf_name,
                    d.handle_second parent_handle_second,
                    CASE
                    IF( d.start_time IS NULL AND d.out_time IS NULL, '0', IF( (d.type = '2' or d.type = '3') and (d.start_time IS NULL AND d.out_time IS not NULL), '0', d.answer_state))
                    WHEN '-1' THEN
                    '进入队列'
                    WHEN '0' THEN
                    '客服未接听'
                    WHEN '1' THEN
                    '接通'
                    WHEN '2' THEN
                    '无人应答'
                    WHEN '3' THEN
                    '群众快速挂机'
                    WHEN '4' THEN
                    '无法接通'
                    WHEN '6' THEN
                    '群众未接听'
                    END 'parent_answer_state_desc'
                FROM
                    callcenter_record_detail d
                 WHERE d.id = #{recordDetailId}
            ) s ON 1 = 1
    </select>

    <insert id="insertCallcenterRecordDetail" parameterType="com.gobon.project.conversation.domain.CallcenterRecordDetail" useGeneratedKeys="true" keyProperty="id">
        insert into callcenter_record_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">id,</if>
            <if test="billNumber != null  and billNumber != ''">bill_number,</if>
            <if test="recordId != null ">record_id,</if>
            <if test="type != null  and type != ''">type,</if>
            <if test="ivrRoute != null  and ivrRoute != ''">ivr_route,</if>
            <if test="businessType != null  and businessType != ''">business_type,</if>
            <if test="callPhone != null  and callPhone != ''">call_phone,</if>
            <if test="callTime != null ">call_time,</if>
            <if test="outIvrTime != null ">out_ivr_time,</if>
            <if test="startTime != null ">start_time,</if>
            <if test="outTime != null ">out_time,</if>
            <if test="switchFlag != null  and switchFlag != ''">switch_flag,</if>
            <if test="switchTime != null ">switch_time,</if>
            <if test="helpFlag != null  and helpFlag != ''">help_flag,</if>
            <if test="helpTime != null ">help_time,</if>
            <if test="multipartyFlag != null  and multipartyFlag != ''">multiparty_flag,</if>
            <if test="multipartyTime != null ">multiparty_time,</if>
            <if test="userId != null ">user_id,</if>
            <if test="userName != null  and userName != ''">user_name,</if>
            <if test="userNumber != null  and userNumber != ''">user_number,</if>
            <if test="deptId != null  and deptId != ''">dept_id,</if>
            <if test="userPhone != null  and userPhone != ''">user_phone,</if>
            <if test="country != null  and country != ''">country,</if>
            <if test="province != null  and province != ''">province,</if>
            <if test="city != null  and city != ''">city,</if>
            <if test="isp != null  and isp != ''">isp,</if>
            <if test="customerId != null ">customer_id,</if>
            <if test="monitorFlag != null  and monitorFlag != ''">monitor_flag,</if>
            <if test="monitorStartTime != null ">monitor_start_time,</if>
            <if test="monitorEndTime != null ">monitor_end_time,</if>
            <if test="monitorUserId != null ">monitor_user_id,</if>
            <if test="monitorUserName != null  and monitorUserName != ''">monitor_user_name,</if>
            <if test="forcedFlag != null  and forcedFlag != ''">forced_flag,</if>
            <if test="forcedTime != null ">forced_time,</if>
            <if test="forcedUserId != null ">forced_user_id,</if>
            <if test="forcedUserName != null  and forcedUserName != ''">forced_user_name,</if>
            <if test="interceptFlag != null  and interceptFlag != ''">intercept_flag,</if>
            <if test="interceptTime != null ">intercept_time,</if>
            <if test="interceptUserId != null ">intercept_user_id,</if>
            <if test="interceptUserName != null  and interceptUserName != ''">intercept_user_name,</if>
            <if test="sound != null  and sound != ''">sound,</if>
            <if test="satisfId != null ">satisf_id,</if>
            <if test="statisfName != null  and statisfName != ''">statisf_name,</if>
            <if test="serviceState != null and serviceState != ''">service_state,</if>
            <if test="serviceTypeId != null and serviceTypeId != ''">service_type_id,</if>
            <if test="serviceTypeName != null  and serviceTypeName != ''">service_type_name,</if>
            <if test="serviceContent != null  and serviceContent != ''">service_content,</if>
            <if test="freeswitchCallId != null  and freeswitchCallId != ''">freeswitch_call_id,</if>
            <if test="freeswitchDetailId != null  and freeswitchDetailId != ''">freeswitch_detail_id,</if>
            <if test="traceId != null  and traceId != ''">trace_id,</if>
            <if test="state != null  and state != ''">state,</if>
            <if test="distributionTime != null ">distribution_time,</if>
            <if test="customerName != null  and customerName != ''">customer_name,</if>
            <if test="answerState != null  and answerState != ''">answer_state,</if>
            <if test="conversationTime != null  and conversationTime != ''">conversation_time,</if>
            <if test="userNumberphone != null  and userNumberphone != ''">user_numberphone,</if>
            <if test="handleTime != null ">handle_time,</if>
            <if test="handleSecond != null ">handle_second,</if>
            <if test="satisfState != null ">satisf_state,</if>
            <if test="statisfiedCode != null  and statisfiedCode != ''">statisfied_code,</if>
            <if test="transferId != null">transfer_id,</if>
            <if test="recordDetailId != null">record_detail_id,</if>
            <if test="helpTypeCode != null  and helpTypeCode != ''">help_type_code,</if>
            <if test="helpType != null  and helpType != ''">help_type,</if>
            <if test="nature != null  and nature != ''">nature,</if>
            <if test="functionalDepartment != null  and functionalDepartment != ''">functional_department,</if>
            <if test="multipartyId != null">multiparty_id,</if>
            <if test="consultLanguageCode != null  and consultLanguageCode != ''">consult_language_code,</if>
            <if test="consultLanguage != null  and consultLanguage != ''">consult_language,</if>
            <if test="thirdParty != null">third_party,</if>
            <if test="hotLineId != null">hot_line_id,</if>
            <if test="cityCode != null  and cityCode != ''">city_code,</if>
            <if test="recordType != null ">record_type,</if>
            <if test="ivrSkillGroupId != null ">ivr_skill_group_id,</if>
            <if test="endPerson != null">end_person,</if>
            <if test="callState != null">call_state,</if>
            <if test="ivrSkillGroupName != null and ivrSkillGroupName != ''">ivr_skill_group_name,</if>
            <if test="originalCallTelephone != null and originalCallTelephone != ''">original_call_telephone,</if>
            <if test="domain != null and domain != ''">domain,</if>
            <if test="targetId != null">target_id,</if>
            <if test="noanswerId != null">noanswer_id,</if>
            <if test="leaveId != null">leave_id,</if>
            <if test="policeId != null and policeId != ''">police_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">#{id},</if>
            <if test="billNumber != null  and billNumber != ''">#{billNumber},</if>
            <if test="recordId != null ">#{recordId},</if>
            <if test="type != null  and type != ''">#{type},</if>
            <if test="ivrRoute != null  and ivrRoute != ''">#{ivrRoute},</if>
            <if test="businessType != null  and businessType != ''">#{businessType},</if>
            <if test="callPhone != null  and callPhone != ''">#{callPhone},</if>
            <if test="callTime != null ">#{callTime},</if>
            <if test="outIvrTime != null ">#{outIvrTime},</if>
            <if test="startTime != null ">#{startTime},</if>
            <if test="outTime != null ">#{outTime},</if>
            <if test="switchFlag != null  and switchFlag != ''">#{switchFlag},</if>
            <if test="switchTime != null ">#{switchTime},</if>
            <if test="helpFlag != null  and helpFlag != ''">#{helpFlag},</if>
            <if test="helpTime != null ">#{helpTime},</if>
            <if test="multipartyFlag != null  and multipartyFlag != ''">#{multipartyFlag},</if>
            <if test="multipartyTime != null ">#{multipartyTime},</if>
            <if test="userId != null ">#{userId},</if>
            <if test="userName != null  and userName != ''">#{userName},</if>
            <if test="userNumber != null  and userNumber != ''">#{userNumber},</if>
            <if test="deptId != null  and deptId != ''">#{deptId},</if>
            <if test="userPhone != null  and userPhone != ''">#{userPhone},</if>
            <if test="country != null  and country != ''">#{country},</if>
            <if test="province != null  and province != ''">#{province},</if>
            <if test="city != null  and city != ''">#{city},</if>
            <if test="isp != null  and isp != ''">#{isp},</if>
            <if test="customerId != null ">#{customerId},</if>
            <if test="monitorFlag != null  and monitorFlag != ''">#{monitorFlag},</if>
            <if test="monitorStartTime != null ">#{monitorStartTime},</if>
            <if test="monitorEndTime != null ">#{monitorEndTime},</if>
            <if test="monitorUserId != null ">#{monitorUserId},</if>
            <if test="monitorUserName != null  and monitorUserName != ''">#{monitorUserName},</if>
            <if test="forcedFlag != null  and forcedFlag != ''">#{forcedFlag},</if>
            <if test="forcedTime != null ">#{forcedTime},</if>
            <if test="forcedUserId != null ">#{forcedUserId},</if>
            <if test="forcedUserName != null  and forcedUserName != ''">#{forcedUserName},</if>
            <if test="interceptFlag != null  and interceptFlag != ''">#{interceptFlag},</if>
            <if test="interceptTime != null ">#{interceptTime},</if>
            <if test="interceptUserId != null ">#{interceptUserId},</if>
            <if test="interceptUserName != null  and interceptUserName != ''">#{interceptUserName},</if>
            <if test="sound != null  and sound != ''">#{sound},</if>
            <if test="satisfId != null ">#{satisfId},</if>
            <if test="statisfName != null  and statisfName != ''">#{statisfName},</if>
            <if test="serviceState != null  and serviceState != ''">#{serviceState},</if>
            <if test="serviceTypeId != null ">#{serviceTypeId},</if>
            <if test="serviceTypeName != null  and serviceTypeName != ''">#{serviceTypeName},</if>
            <if test="serviceContent != null  and serviceContent != ''">#{serviceContent},</if>
            <if test="freeswitchCallId != null  and freeswitchCallId != ''">#{freeswitchCallId},</if>
            <if test="freeswitchDetailId != null  and freeswitchDetailId != ''">#{freeswitchDetailId},</if>
            <if test="traceId != null  and traceId != ''">#{traceId},</if>
            <if test="state != null  and state != ''">#{state},</if>
            <if test="distributionTime != null ">#{distributionTime},</if>
            <if test="customerName != null  and customerName != ''">#{customerName},</if>
            <if test="answerState != null  and answerState != ''">#{answerState},</if>
            <if test="conversationTime != null  and conversationTime != ''">#{conversationTime},</if>
            <if test="userNumberphone != null  and userNumberphone != ''">#{userNumberphone},</if>
            <if test="handleTime != null ">#{handleTime},</if>
            <if test="handleSecond != null ">#{handleSecond},</if>
            <if test="satisfState != null ">#{satisfState},</if>
            <if test="statisfiedCode != null  and statisfiedCode != ''">#{statisfiedCode},</if>
            <if test="transferId != null">#{transferId},</if>
            <if test="recordDetailId != null">#{recordDetailId},</if>
            <if test="helpTypeCode != null  and helpTypeCode != ''">#{helpTypeCode},</if>
            <if test="helpType != null  and helpType != ''">#{helpType},</if>
            <if test="nature != null  and nature != ''">#{nature},</if>
            <if test="functionalDepartment != null  and functionalDepartment != ''">#{functionalDepartment},</if>
            <if test="multipartyId != null">#{multipartyId},</if>
            <if test="consultLanguageCode != null  and consultLanguageCode != ''">#{consultLanguageCode},</if>
            <if test="consultLanguage != null  and consultLanguage != ''">#{consultLanguage},</if>
            <if test="thirdParty != null">#{thirdParty},</if>
            <if test="hotLineId != null">#{hotLineId},</if>
            <if test="cityCode != null  and cityCode != ''">#{cityCode},</if>
            <if test="recordType != null ">#{recordType},</if>
            <if test="ivrSkillGroupId != null ">#{ivrSkillGroupId},</if>
            <if test="endPerson != null">#{endPerson},</if>
            <if test="callState != null">#{callState},</if>
            <if test="ivrSkillGroupName != null and ivrSkillGroupName != ''">#{ivrSkillGroupName},</if>
            <if test="originalCallTelephone != null and originalCallTelephone != ''">#{originalCallTelephone},</if>
            <if test="domain != null and domain != ''">#{domain},</if>
            <if test="targetId != null">#{targetId},</if>
            <if test="noanswerId != null">#{noanswerId},</if>
            <if test="leaveId != null">#{leaveId},</if>
            <if test="policeId != null and policeId != ''">#{policeId},</if>
        </trim>
    </insert>

    <update id="updateCallcenterRecordDetailMonitor" parameterType="com.gobon.project.conversation.domain.CallcenterRecordDetail">
        update callcenter_record_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="monitorFlag != null  and monitorFlag != ''">monitor_flag = #{monitorFlag},</if>
            <if test="monitorStartTime != null ">monitor_start_time = #{monitorStartTime},</if>
            monitor_end_time = #{monitorEndTime},
            <if test="monitorUserId != null ">monitor_user_id = #{monitorUserId},</if>
            <if test="monitorUserName != null  and monitorUserName != ''">monitor_user_name = #{monitorUserName},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateCallcenterRecordDetail" parameterType="com.gobon.project.conversation.domain.CallcenterRecordDetail">
        update callcenter_record_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="billNumber != null  and billNumber != ''">bill_number = #{billNumber},</if>
            <if test="recordId != null ">record_id = #{recordId},</if>
            <if test="type != null  and type != ''">type = #{type},</if>
            <if test="ivrRoute != null  and ivrRoute != ''">ivr_route = #{ivrRoute},</if>
            <if test="businessType != null  and businessType != ''">business_type = #{businessType},</if>
            <if test="callPhone != null  and callPhone != ''">call_phone = #{callPhone},</if>
            <if test="callTime != null ">call_time = #{callTime},</if>
            <if test="outIvrTime != null ">out_ivr_time = #{outIvrTime},</if>
            <if test="startTime != null ">start_time = #{startTime},</if>
            <if test="outTime != null ">out_time = #{outTime},</if>
            <if test="switchFlag != null  and switchFlag != ''">switch_flag = #{switchFlag},</if>
            <if test="switchTime != null ">switch_time = #{switchTime},</if>
            <if test="helpFlag != null  and helpFlag != ''">help_flag = #{helpFlag},</if>
            <if test="helpTime != null ">help_time = #{helpTime},</if>
            <if test="multipartyFlag != null  and multipartyFlag != ''">multiparty_flag = #{multipartyFlag},</if>
            <if test="multipartyTime != null ">multiparty_time = #{multipartyTime},</if>
            <if test="userId != null ">user_id = #{userId},</if>
            <if test="userName != null  and userName != ''">user_name = #{userName},</if>
            <if test="userNumber != null  and userNumber != ''">user_number = #{userNumber},</if>
            <if test="deptId != null  and deptId != ''">dept_id = #{deptId},</if>
            <if test="userPhone != null  and userPhone != ''">user_phone = #{userPhone},</if>
            <if test="country != null  and country != ''">country = #{country},</if>
            <if test="province != null  and province != ''">province = #{province},</if>
            <if test="city != null  and city != ''">city = #{city},</if>
            <if test="isp != null  and isp != ''">isp = #{isp},</if>
            <if test="customerId != null ">customer_id = #{customerId},</if>
            <if test="monitorFlag != null  and monitorFlag != ''">monitor_flag = #{monitorFlag},</if>
            <if test="monitorStartTime != null ">monitor_start_time = #{monitorStartTime},</if>
            <if test="monitorEndTime != null ">monitor_end_time = #{monitorEndTime},</if>
            <if test="monitorUserId != null ">monitor_user_id = #{monitorUserId},</if>
            <if test="monitorUserName != null  and monitorUserName != ''">monitor_user_name = #{monitorUserName},</if>
            <if test="forcedFlag != null  and forcedFlag != ''">forced_flag = #{forcedFlag},</if>
            <if test="forcedTime != null ">forced_time = #{forcedTime},</if>
            <if test="forcedUserId != null ">forced_user_id = #{forcedUserId},</if>
            <if test="forcedUserName != null  and forcedUserName != ''">forced_user_name = #{forcedUserName},</if>
            <if test="interceptFlag != null  and interceptFlag != ''">intercept_flag = #{interceptFlag},</if>
            <if test="interceptTime != null ">intercept_time = #{interceptTime},</if>
            <if test="interceptUserId != null ">intercept_user_id = #{interceptUserId},</if>
            <if test="interceptUserName != null  and interceptUserName != ''">intercept_user_name = #{interceptUserName},</if>
            <if test="sound != null  and sound != ''">sound = #{sound},</if>
            <if test="satisfId != null ">satisf_id = #{satisfId},</if>
            <if test="statisfName != null  and statisfName != ''">statisf_name = #{statisfName},</if>
            <if test="serviceState != null and serviceState != ''">service_state = #{serviceState},</if>
            <if test="serviceTypeId != null and serviceTypeId != ''">service_type_id = #{serviceTypeId},</if>
            <if test="serviceTypeName != null  and serviceTypeName != ''">service_type_name = #{serviceTypeName},</if>
            <if test="serviceContent != null  and serviceContent != ''">service_content = #{serviceContent},</if>
            <if test="freeswitchCallId != null  and freeswitchCallId != ''">freeswitch_call_id = #{freeswitchCallId},</if>
            <if test="freeswitchDetailId != null  and freeswitchDetailId != ''">freeswitch_detail_id = #{freeswitchDetailId},</if>
            <if test="traceId != null  and traceId != ''">trace_id = #{traceId},</if>
            <if test="state != null  and state != ''">state = #{state},</if>
            <if test="distributionTime != null ">distribution_time = #{distributionTime},</if>
            <if test="customerName != null  and customerName != ''">customer_name = #{customerName},</if>
            <if test="answerState != null  and answerState != ''">answer_state = #{answerState},</if>
            <if test="conversationTime != null  and conversationTime != '' and conversationTime != '0'">conversation_time = #{conversationTime},</if>
            <if test="userNumberphone != null  and userNumberphone != ''">user_numberphone = #{userNumberphone},</if>
            <if test="handleTime != null ">handle_time = #{handleTime},</if>
            <if test="handleSecond != null ">handle_second = #{handleSecond},</if>
            <if test="satisfState != null ">satisf_state = #{satisfState},</if>
            <if test="statisfiedCode != null ">statisfied_code = #{statisfiedCode},</if>
            <if test="transferId != null ">transfer_id = #{transferId},</if>
            <if test="recordDetailId != null ">record_detail_id = #{recordDetailId},</if>
            <if test="helpTypeCode != null  and helpTypeCode != ''">help_type_code = #{helpTypeCode},</if>
            <if test="helpType != null  and helpType != ''">help_type = #{helpType},</if>
            <if test="nature != null  and nature != ''">nature = #{nature},</if>
            <if test="functionalDepartment != null  and functionalDepartment != ''">functional_department = #{functionalDepartment},</if>
            <if test="consultLanguageCode != null  and consultLanguageCode != ''">consult_language_code = #{consultLanguageCode},</if>
            <if test="consultLanguage != null  and consultLanguage != ''">consult_language = #{consultLanguage},</if>
            <if test="cityCode != null  and cityCode != ''">city_code = #{cityCode},</if>
            <if test="endReason != null">end_reason = #{endReason},</if>
            <if test="divisionMark != null and divisionMark != ''">division_mark = #{divisionMark},</if>
            <if test="soundText != null and soundText != ''">sound_text = #{soundText},</if>
            <if test="soundTextState != null and soundTextState != ''">sound_text_state = #{soundTextState},</if>
            <if test="ringStartTime != null">ring_start_time = #{ringStartTime},</if>
            <if test="ringEndTime != null">ring_end_time = #{ringEndTime},</if>
            <if test="ivrSkillGroupId != null">ivr_skill_group_id = #{ivrSkillGroupId},</if>
            <if test="endPerson != null">end_person = #{endPerson},</if>
            <if test="callState != null">call_state = #{callState},</if>
            <if test="originalCallTelephone != null and originalCallTelephone != ''">original_call_telephone = #{originalCallTelephone},</if>
            <if test="ivrSkillGroupName != null and ivrSkillGroupName != ''">ivr_skill_group_name = #{ivrSkillGroupName},</if>
            <if test="domain != null and domain != ''">`domain` = #{domain},</if>
            <if test="targetId != null">target_id = #{targetId},</if>
            <if test="noanswerId != null">noanswer_id = #{noanswerId},</if>
            <if test="leaveId != null">leave_id = #{leaveId},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="addCustomer" parameterType="com.gobon.project.conversation.domain.param.CustomerParam">
        update callcenter_record_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null ">customer_id = #{customerId},</if>
            <if test="customerName != null  and customerName != ''">customer_name = #{customerName}</if>
        </trim>
        where user_id = #{userId}
        and (call_phone = #{phone} or user_phone = #{phone} or id = #{recordDetailId} )
    </update>

    <update id="updateCustomer" parameterType="com.gobon.project.conversation.domain.param.CustomerParam">
        update callcenter_record_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerName != null  and customerName != ''">customer_name = #{customerName}</if>
        </trim>
        where customer_id = #{customerId} and user_id = #{userId}
    </update>

    <update id="updateSoundText">
        update callcenter_record_detail set sound_text = #{text},sound_text_state = 1 where id = #{id}
    </update>
    <update id="updateStatisfiedByRecordId">
        update callcenter_record_detail set satisf_id = #{satisfId}, statisf_name = #{statisfName}, satisf_state = '1',
        statisfied_code = #{statisfCode}, end_person = '1'
        where record_id = #{recordId} and user_id is not null
    </update>

    <update id="updateSatisfStateByTraceId">
        update
            callcenter_record_detail
        set
            satisf_state = 1
        where trace_id = #{traceId}
    </update>
    <update id="updateSatisfStateByRecordId">
        update
            callcenter_record_detail
        set
            satisf_state = 1
        where record_id = #{recordId}
    </update>
    <update id="updateSatisfStateById">
        update
            callcenter_record_detail
        set
            satisf_state = 1
        where id = #{id}
    </update>

    <select id="getPersonalStatistics" parameterType="com.gobon.project.conversation.domain.param.StatisticsParam" resultType="com.gobon.project.conversation.domain.vo.ConversationStatisticsVO">
        select
            t.*,
            ifnull(truncate( t.incoming_call_time / t.incoming_connect_call, 2),0) average_incoming_call_time,
            ifnull(truncate( t.dial_time / t.connect_dial , 2),0) average_dial_time,
            ifnull(truncate( (t.incoming_call_time + t.dial_time) / t.conversation_connect_count, 0),0) average_talk_time,
            TRUNCATE ( t.participate / t.invitation * 100, 2 ) participate_rate,
            ifnull(TRUNCATE( t.invitation / t.main_conversation_count * 100, 2 ),0)  invitation_rate
        from
        (
        SELECT  count(1) conversation_count,
                count(if( t.answer_state = 1, TRUE , NULL)) conversation_connect_count,
                count(if(d.record_type='0',true,null )) incoming_call,
                count(if(d.record_type='0' and t.answer_state = 1 ,true,null )) incoming_connect_call,
                count(if(d.record_type='1',true,null )) dial ,
                count(if(d.record_type='1' and t.answer_state = 1 ,true,null )) connect_dial ,

                IFNULL(sum(case when t.answer_state = 1 then TIMESTAMPDIFF(second,t.start_time, t.out_time) else 0 end), 0) talk_time,
                IFNULL(sum(case when d.record_type = '0' and t.answer_state = 1 then TIMESTAMPDIFF(second,t.start_time, t.out_time) else 0 end), 0) incoming_call_time,
                IFNULL(sum(case when d.record_type = '1' and t.answer_state = 1 then TIMESTAMPDIFF(second,t.start_time, t.out_time) else 0 end), 0) dial_time,

                ifnull(truncate(count(IF ( d.record_type = '1' and t.answer_state = 1, TRUE , NULL ))/count(IF ( d.record_type = '1', TRUE, NULL )) * 100, 2), 0) connect_rate,
                ifnull(truncate(count(IF ( d.record_type = '0' and t.answer_state = 1, TRUE , NULL ))/count(IF ( d.record_type = '0', TRUE, NULL )) * 100, 2), 0) incoming_rate,

                ifnull(TRUNCATE ( count( IF ( t.statisfied_code in ('very_good','good','common') or (t.satisf_id is null), TRUE, NULL ) ) / count( IF ( 1 = 1, TRUE, NULL ) ) * 100, 2 ),0) satisfied,
                count( IF ( t.satisf_id IS NOT NULL and t.satisf_state = 1 and t.type in (0, 1, 4, 5), TRUE, NULL ) ) participate , -- 参评数
                count( IF ( t.start_time IS NOT NULL, TRUE, NULL ) ) connect,
                count(IF (  t.satisf_state = 1 and t.type in (0, 1, 4, 5), true, NULL ) ) invitation, -- 邀评数
                count( IF ( t.type in (0, 1, 4, 5) and  t.answer_state = 1, TRUE, NULL ) ) main_conversation_count ,


                (SELECT sum( t.state_second ) + c.state_second state_second FROM

                callcenter_freeswitch_option t,
                    (
                    SELECT
                        s.*,
                        IFNULL( c.state_second, 0 ) state_second
                    FROM
                    ( SELECT 1 AS a ) s
                    LEFT JOIN
                        (
                            SELECT
                            timestampdiff(SECOND ,t.option_time, now()) state_second
                            FROM
                            callcenter_freeswitch_option t
                            WHERE
                            1 = 1
                            <if test="userId != null  and userId != ''"> and t.option_user_id = #{userId}</if>

                            <if test='beginTime !=null and beginTime != "" '>
                                and t.option_time  <![CDATA[>=]]> str_to_date(#{beginTime},'%Y-%m-%d %H:%i:%s')
                            </if>
                            <if test='endTime !=null and endTime != "" '>
                                and t.option_time <![CDATA[<=]]> str_to_date(#{endTime},'%Y-%m-%d %H:%i:%s')
                            </if>
                            AND t.option_type_code != '4'
                            AND t.option_type_code != '7'
                            and t.state_second = '0'
                            and t.nextstate_time is null
                            order by t.option_time desc limit 1
                            ) c ON 1 = 1
                    ) c

                where 1 = 1
                <if test="userId != null  and userId != ''"> and t.option_user_id = #{userId}</if>

                <if test='beginTime !=null and beginTime != "" '>
                    and t.option_time  <![CDATA[>=]]> str_to_date(#{beginTime},'%Y-%m-%d %H:%i:%s')
                </if>
                <if test='endTime !=null and endTime != "" '>
                    and t.option_time <![CDATA[<=]]> str_to_date(#{endTime},'%Y-%m-%d %H:%i:%s')
                </if>

                AND t.option_type_code != '4'
                AND t.option_type_code != '7'

                ) on_line_time

                FROM
                callcenter_record_detail t
                INNER join
                callcenter_record d on t.record_id = d.id
                left join sys_user su on su.user_id = t.user_id

                where 1 = 1
                AND su.del_flag = '0'
                <if test="userId != null  and userId != ''">and t.user_id = #{userId}</if>
                <if test='beginTime !=null and beginTime != "" '>
                    and t.call_time  <![CDATA[>=]]> str_to_date(#{beginTime},'%Y-%m-%d %H:%i:%s')
                </if>
                <if test='endTime !=null and endTime != "" '>
                    and t.call_time <![CDATA[<=]]> str_to_date(#{endTime},'%Y-%m-%d %H:%i:%s')
                </if>

                <if test='pageSource != null and pageSource != "" and pageSource == "1"'>
                    <!-- 数据范围过滤 -->
                    ${dataScope}
                </if>
        ) t
    </select>

    <select id="getManageStatistics" parameterType="com.gobon.project.conversation.domain.param.StatisticsParam" resultType="com.gobon.project.conversation.domain.vo.ConversationStatisticsVO">
        select
        t.*,
        ifnull(truncate( t.incoming_call_time / t.whole_incoming_call_answer, 0),0) average_incoming_call_time,
        ifnull(truncate( t.dial_time / t.whole_dial_answer , 0),0) average_dial_time,
        ifnull(truncate( t.talk_time / (t.whole_dial_answer + t.whole_incoming_call_answer), 0),0) average_talk_time,
        ifnull(truncate( t.dial_answer / t.dial * 100, 2), 0) connect_rate,
        ifnull(truncate( t.incoming_call_answer / t.incoming_call * 100, 2), 0) incoming_rate,
        ifnull(TRUNCATE( t.good / t.conversation_count * 100, 2 ),0) satisfied,
        ifnull(TRUNCATE( t.participate / t.invitation * 100, 2 ),0)  participate_rate,
        ifnull(TRUNCATE( t.invitation / (t.dial_answer + t.incoming_call_answer) * 100, 2 ),0)  invitation_rate
        from
        (
            select
            d.*,
            count(c.id) conversation_count,
            count(if(c.record_type= 1 ,true,null )) dial -- 呼出量

            from
            (
                select
                distinct d.record_id
                from
                callcenter_record_detail d
                left join callcenter_record c  on d.record_id = c.id
                left join
                sys_user su on d.user_id = su.user_id
                where 1 = 1
                <if test='beginTime !=null and beginTime != "" '>
                    and d.call_time  <![CDATA[>=]]> str_to_date(#{beginTime},'%Y-%m-%d %H:%i:%s')
                </if>
                <if test='endTime !=null and endTime != "" '>
                    and d.call_time <![CDATA[<=]]> str_to_date(#{endTime},'%Y-%m-%d %H:%i:%s')
                </if>

                <if test='pageSource != null and pageSource != "" and pageSource == "1"'>
                    <!-- 数据范围过滤 -->
                    ${dataScope}
                </if>
            ) t
            left join
            callcenter_record c on t.record_id = c.id
            left join
            (

                select

                sum( CASE WHEN d.answer_state = 1 THEN TIMESTAMPDIFF( SECOND, d.start_time, d.out_time ) ELSE 0 END ) talk_time, -- 通话时长
                IFNULL(sum( case when c.record_type= 0 and d.answer_state = 1 then TIMESTAMPDIFF( SECOND, d.start_time, d.out_time ) else 0 end), 0) incoming_call_time, -- 呼入通话时长
                IFNULL(sum( case when c.record_type= 1 and d.answer_state = 1 then TIMESTAMPDIFF( SECOND, d.start_time, d.out_time ) else 0 end), 0) dial_time, -- 呼出通话时长

                count(distinct(if( c.record_type = 0 and d.answer_state != -1 ,c.id,null ))) incoming_call, -- 呼入量
                count(distinct(if( c.record_type = 0 and d.answer_state = 1 ,c.id,null ))) incoming_call_answer, -- 算一通呼入接听量
                count(distinct(if( c.record_type = 0 and d.answer_state = 1 ,d.id,null ))) whole_incoming_call_answer, -- 所有呼入接听量
                count(distinct(if( c.record_type = 1 and d.answer_state = 1 ,c.id,null ))) dial_answer, -- 算一通呼出接听量
                count(distinct(if( c.record_type = 1 and d.answer_state = 1 ,d.id,null ))) whole_dial_answer, -- 所有呼出接听量`

                count( IF ( d.type = 0 and d.statisfied_code in ('very_good','good','common') or (d.type = 0 and d.satisf_id is null), TRUE, NULL ) ) good, -- 好评数
                count( IF ( d.satisf_id IS NOT NULL and d.type = 0 , TRUE, NULL ) ) participate , -- 参评数
                count( IF ( d.satisf_state = 1 , true, NULL ) ) invitation -- 邀评数
                from
                callcenter_record_detail d
                left join callcenter_record c  on d.record_id = c.id
                left join
                sys_user su on d.user_id = su.user_id
                where 1 = 1
                <if test='beginTime !=null and beginTime != "" '>
                    and d.call_time  <![CDATA[>=]]> str_to_date(#{beginTime},'%Y-%m-%d %H:%i:%s')
                </if>
                <if test='endTime !=null and endTime != "" '>
                    and d.call_time <![CDATA[<=]]> str_to_date(#{endTime},'%Y-%m-%d %H:%i:%s')
                </if>

                <if test='pageSource != null and pageSource != "" and pageSource == "1"'>
                    <!-- 数据范围过滤 -->
                    ${dataScope}
                </if>

            ) d on 1 = 1

        ) t
    </select>

    <select id="getConsultLanguageStatistics" parameterType="com.gobon.project.conversation.domain.param.StatisticsParam" resultType="com.gobon.project.conversation.domain.vo.ConsultLanguageStatisticsVO">
        select s.dict_label name, ifnull(t.value, 0) value FROM
        (
            select s.dict_label, s.dict_value from sys_dict_data s where s.status = '0' and dict_type = 'consult_language_level'
        ) s
        left join
        (
            SELECT
                d.consult_language_code,
                count(1) value
            FROM
            callcenter_record_detail d
            left join sys_user su on su.user_id = d.user_id
            where
                1 = 1
                and su.del_flag = '0'
                and d.consult_language_code is not null
                and d.type = 0
                <!-- 数据范围过滤 -->
                ${dataScope}
            group by d.consult_language_code
        ) t on t.consult_language_code = s.dict_value

    </select>

    <select id="selectNewCallcenterRecordDetailByRecordIdAndMultipartyId"
            resultType="com.gobon.project.conversation.domain.CallcenterRecordDetail">
        select
            *
        from callcenter_record_detail
        where
            record_id = #{recordId}
            and
            id in (
                select tmp.id from (
                    select
                        substring_index(group_concat(crd.id order by crd.`call_time` desc),',',1) id
                    from callcenter_record_detail crd
                    where crd.record_id = #{recordId}
                    and crd.multiparty_id = #{multipartyId}
                    group by crd.user_phone
                ) tmp
	        )
    </select>
    <select id="getByDetailId" resultType="com.gobon.project.conversation.domain.CallcenterRecord">
        select t1.* FROM callcenter_record t1
        left join callcenter_record_detail t2 on t1.id = t2.record_id
        where t2.id = #{detailId} limit 1
    </select>
    <select id="selectDetailByTraceId"
            resultType="com.gobon.project.conversation.domain.CallcenterRecordDetail">
        select * FROM callcenter_record_detail
        where trace_id = #{traceId} and type not in (2,3)  order by start_time desc limit 1
    </select>

    <select id="selectCallcenterRecordDetailByRecordIdAndType"
            resultType="com.gobon.project.conversation.domain.CallcenterRecordDetail">
        select
            *
        from callcenter_record_detail
        where record_id = #{recordId} and `type` = #{type}
    </select>

    <select id="selectCallcenterRecordDetailListByTraceId" resultType="com.gobon.project.conversation.domain.CallcenterRecordDetail">
        select *
        from callcenter_record_detail
        where trace_id = #{traceId} order by id desc
    </select>


    <select id="getVisitor" resultType="String">
        SELECT
            count( t.phone )
        FROM
            (
        SELECT DISTINCT
        IF
            ( c.record_type = '0', c.call_phone, null ) phone
        FROM
            callcenter_record_detail d
            LEFT JOIN callcenter_record c ON c.id = d.record_id
            left join sys_user su on su.user_id = d.user_id
        WHERE
            d.type = 0

            <if test='beginTime !=null and beginTime != "" '>
                and d.call_time  <![CDATA[>=]]> str_to_date(#{beginTime},'%Y-%m-%d %H:%i:%s')
            </if>
            <if test='endTime !=null and endTime != "" '>
                and d.call_time <![CDATA[<=]]> str_to_date(#{endTime},'%Y-%m-%d %H:%i:%s')
            </if>

            <!-- 数据范围过滤 -->
            ${dataScope}
            ) t

    </select>

    <select id="selectCallcenterRecordDetailListByRecordIdOrderById"
            resultType="com.gobon.project.conversation.domain.CallcenterRecordDetail">
        select
            *
        from callcenter_record_detail
        where record_id = #{recordId}
        order by id desc limit 1
    </select>
    <select id="selectCallcenterRecordLastNoPartyByTraceId"
            resultType="com.gobon.project.conversation.domain.CallcenterRecordDetail">
         select
            *
        from callcenter_record_detail
        where trace_id = #{traceId} and type not in (2,3)  and answer_state = 1
        order by start_time desc limit 1
    </select>

    <select id="apiSelectCallcenterRecordDetailById" resultType="com.gobon.project.conversation.domain.vo.CallRecordDetailslApiVO" parameterType="Long">
         select * from callcenter_record_detail  where id = #{id}
    </select>


    <insert id="insertCallcenterRecordDetailSound" parameterType="com.gobon.project.conversation.domain.CallcenterRecordDetailSound" useGeneratedKeys="true" keyProperty="id">
        insert into callcenter_record_detail_sound
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">id,</if>
            <if test="callRecordDetailId != null">call_record_detail_id,</if>
            <if test="sound != null and sound !='' ">sound,</if>
            <if test="soundText != null and soundText !='' ">sound_text,</if>
            <if test="conversationRole != null and conversationRole !='' ">conversation_role,</if>
            <if test="userId != null ">user_id,</if>
            <if test="startSecond != null and startSecond !='' ">start_second,</if>
            <if test="endSecond != null and endSecond !='' ">end_second,</if>
            <if test="createTime != null ">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">#{id},</if>
            <if test="callRecordDetailId != null ">#{callRecordDetailId},</if>
            <if test="sound != null and sound !='' ">#{sound},</if>
            <if test="soundText != null and soundText !='' ">#{soundText},</if>
            <if test="conversationRole != null and conversationRole !='' ">#{conversationRole},</if>
            <if test="userId != null ">#{userId},</if>
            <if test="startSecond != null and startSecond !='' ">#{startSecond},</if>
            <if test="endSecond != null and endSecond !='' ">#{endSecond},</if>
            <if test="createTime != null ">#{createTime},</if>
        </trim>
    </insert>
    <insert id="insertAbnormalCallcenterRecordDetail">
        insert into callcenter_record_detail_abnormal
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">id,</if>
            <if test="billNumber != null  and billNumber != ''">bill_number,</if>
            <if test="recordId != null ">record_id,</if>
            <if test="type != null  and type != ''">type,</if>
            <if test="ivrRoute != null  and ivrRoute != ''">ivr_route,</if>
            <if test="businessType != null  and businessType != ''">business_type,</if>
            <if test="callPhone != null  and callPhone != ''">call_phone,</if>
            <if test="callTime != null ">call_time,</if>
            <if test="outIvrTime != null ">out_ivr_time,</if>
            <if test="startTime != null ">start_time,</if>
            <if test="outTime != null ">out_time,</if>
            <if test="switchFlag != null  and switchFlag != ''">switch_flag,</if>
            <if test="switchTime != null ">switch_time,</if>
            <if test="helpFlag != null  and helpFlag != ''">help_flag,</if>
            <if test="helpTime != null ">help_time,</if>
            <if test="multipartyFlag != null  and multipartyFlag != ''">multiparty_flag,</if>
            <if test="multipartyTime != null ">multiparty_time,</if>
            <if test="userId != null ">user_id,</if>
            <if test="userName != null  and userName != ''">user_name,</if>
            <if test="userNumber != null  and userNumber != ''">user_number,</if>
            <if test="deptId != null  and deptId != ''">dept_id,</if>
            <if test="userPhone != null  and userPhone != ''">user_phone,</if>
            <if test="country != null  and country != ''">country,</if>
            <if test="province != null  and province != ''">province,</if>
            <if test="city != null  and city != ''">city,</if>
            <if test="isp != null  and isp != ''">isp,</if>
            <if test="customerId != null ">customer_id,</if>
            <if test="monitorFlag != null  and monitorFlag != ''">monitor_flag,</if>
            <if test="monitorStartTime != null ">monitor_start_time,</if>
            <if test="monitorEndTime != null ">monitor_end_time,</if>
            <if test="monitorUserId != null ">monitor_user_id,</if>
            <if test="monitorUserName != null  and monitorUserName != ''">monitor_user_name,</if>
            <if test="forcedFlag != null  and forcedFlag != ''">forced_flag,</if>
            <if test="forcedTime != null ">forced_time,</if>
            <if test="forcedUserId != null ">forced_user_id,</if>
            <if test="forcedUserName != null  and forcedUserName != ''">forced_user_name,</if>
            <if test="interceptFlag != null  and interceptFlag != ''">intercept_flag,</if>
            <if test="interceptTime != null ">intercept_time,</if>
            <if test="interceptUserId != null ">intercept_user_id,</if>
            <if test="interceptUserName != null  and interceptUserName != ''">intercept_user_name,</if>
            <if test="sound != null  and sound != ''">sound,</if>
            <if test="satisfId != null ">satisf_id,</if>
            <if test="statisfName != null  and statisfName != ''">statisf_name,</if>
            <if test="serviceState != null and serviceState != ''">service_state,</if>
            <if test="serviceTypeId != null and serviceTypeId != ''">service_type_id,</if>
            <if test="serviceTypeName != null  and serviceTypeName != ''">service_type_name,</if>
            <if test="serviceContent != null  and serviceContent != ''">service_content,</if>
            <if test="freeswitchCallId != null  and freeswitchCallId != ''">freeswitch_call_id,</if>
            <if test="freeswitchDetailId != null  and freeswitchDetailId != ''">freeswitch_detail_id,</if>
            <if test="traceId != null  and traceId != ''">trace_id,</if>
            <if test="state != null  and state != ''">state,</if>
            <if test="distributionTime != null ">distribution_time,</if>
            <if test="customerName != null  and customerName != ''">customer_name,</if>
            <if test="answerState != null  and answerState != ''">answer_state,</if>
            <if test="conversationTime != null  and conversationTime != ''">conversation_time,</if>
            <if test="userNumberphone != null  and userNumberphone != ''">user_numberphone,</if>
            <if test="handleTime != null ">handle_time,</if>
            <if test="handleSecond != null ">handle_second,</if>
            <if test="satisfState != null ">satisf_state,</if>
            <if test="statisfiedCode != null  and statisfiedCode != ''">statisfied_code,</if>
            <if test="transferId != null">transfer_id,</if>
            <if test="recordDetailId != null">record_detail_id,</if>
            <if test="helpTypeCode != null  and helpTypeCode != ''">help_type_code,</if>
            <if test="helpType != null  and helpType != ''">help_type,</if>
            <if test="nature != null  and nature != ''">nature,</if>
            <if test="functionalDepartment != null  and functionalDepartment != ''">functional_department,</if>
            <if test="multipartyId != null">multiparty_id,</if>
            <if test="consultLanguageCode != null  and consultLanguageCode != ''">consult_language_code,</if>
            <if test="consultLanguage != null  and consultLanguage != ''">consult_language,</if>
            <if test="thirdParty != null">third_party,</if>
            <if test="hotLineId != null">hot_line_id,</if>
            <if test="cityCode != null  and cityCode != ''">city_code,</if>
            <if test="recordType != null ">record_type,</if>
            <if test="ivrSkillGroupId != null ">ivr_skill_group_id,</if>
            <if test="endPerson != null">end_person,</if>
            <if test="callState != null">call_state,</if>
            <if test="realCallState != null">real_call_state,</if>
            <if test="ivrSkillGroupName != null and ivrSkillGroupName != ''">ivr_skill_group_name,</if>
            <if test="originalCallTelephone != null and originalCallTelephone != ''">original_call_telephone,</if>
            <if test="domain != null and domain != ''">domain,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">#{id},</if>
            <if test="billNumber != null  and billNumber != ''">#{billNumber},</if>
            <if test="recordId != null ">#{recordId},</if>
            <if test="type != null  and type != ''">#{type},</if>
            <if test="ivrRoute != null  and ivrRoute != ''">#{ivrRoute},</if>
            <if test="businessType != null  and businessType != ''">#{businessType},</if>
            <if test="callPhone != null  and callPhone != ''">#{callPhone},</if>
            <if test="callTime != null ">#{callTime},</if>
            <if test="outIvrTime != null ">#{outIvrTime},</if>
            <if test="startTime != null ">#{startTime},</if>
            <if test="outTime != null ">#{outTime},</if>
            <if test="endPerson != null  and endPerson != ''">#{endPerson},</if>
            <if test="switchFlag != null  and switchFlag != ''">#{switchFlag},</if>
            <if test="switchTime != null ">#{switchTime},</if>
            <if test="helpFlag != null  and helpFlag != ''">#{helpFlag},</if>
            <if test="helpTime != null ">#{helpTime},</if>
            <if test="multipartyFlag != null  and multipartyFlag != ''">#{multipartyFlag},</if>
            <if test="multipartyTime != null ">#{multipartyTime},</if>
            <if test="userId != null ">#{userId},</if>
            <if test="userName != null  and userName != ''">#{userName},</if>
            <if test="userNumber != null  and userNumber != ''">#{userNumber},</if>
            <if test="deptId != null  and deptId != ''">#{deptId},</if>
            <if test="userPhone != null  and userPhone != ''">#{userPhone},</if>
            <if test="country != null  and country != ''">#{country},</if>
            <if test="province != null  and province != ''">#{province},</if>
            <if test="city != null  and city != ''">#{city},</if>
            <if test="isp != null  and isp != ''">#{isp},</if>
            <if test="customerId != null ">#{customerId},</if>
            <if test="monitorFlag != null  and monitorFlag != ''">#{monitorFlag},</if>
            <if test="monitorStartTime != null ">#{monitorStartTime},</if>
            <if test="monitorEndTime != null ">#{monitorEndTime},</if>
            <if test="monitorUserId != null ">#{monitorUserId},</if>
            <if test="monitorUserName != null  and monitorUserName != ''">#{monitorUserName},</if>
            <if test="forcedFlag != null  and forcedFlag != ''">#{forcedFlag},</if>
            <if test="forcedTime != null ">#{forcedTime},</if>
            <if test="forcedUserId != null ">#{forcedUserId},</if>
            <if test="forcedUserName != null  and forcedUserName != ''">#{forcedUserName},</if>
            <if test="interceptFlag != null  and interceptFlag != ''">#{interceptFlag},</if>
            <if test="interceptTime != null ">#{interceptTime},</if>
            <if test="interceptUserId != null ">#{interceptUserId},</if>
            <if test="interceptUserName != null  and interceptUserName != ''">#{interceptUserName},</if>
            <if test="sound != null  and sound != ''">#{sound},</if>
            <if test="satisfId != null ">#{satisfId},</if>
            <if test="statisfName != null  and statisfName != ''">#{statisfName},</if>
            <if test="serviceState != null  and serviceState != ''">#{serviceState},</if>
            <if test="serviceTypeId != null ">#{serviceTypeId},</if>
            <if test="serviceTypeName != null  and serviceTypeName != ''">#{serviceTypeName},</if>
            <if test="serviceContent != null  and serviceContent != ''">#{serviceContent},</if>
            <if test="freeswitchCallId != null  and freeswitchCallId != ''">#{freeswitchCallId},</if>
            <if test="freeswitchDetailId != null  and freeswitchDetailId != ''">#{freeswitchDetailId},</if>
            <if test="traceId != null  and traceId != ''">#{traceId},</if>
            <if test="state != null  and state != ''">#{state},</if>
            <if test="distributionTime != null ">#{distributionTime},</if>
            <if test="customerName != null  and customerName != ''">#{customerName},</if>
            <if test="answerState != null  and answerState != ''">#{answerState},</if>
            <if test="conversationTime != null  and conversationTime != ''">#{conversationTime},</if>
            <if test="userNumberphone != null  and userNumberphone != ''">#{userNumberphone},</if>
            <if test="handleTime != null ">#{handleTime},</if>
            <if test="handleSecond != null ">#{handleSecond},</if>
            <if test="satisfState != null ">#{satisfState},</if>
            <if test="statisfiedCode != null  and statisfiedCode != ''">#{statisfiedCode},</if>
            <if test="transferId != null">#{transferId},</if>
            <if test="recordDetailId != null">#{recordDetailId},</if>
            <if test="helpTypeCode != null  and helpTypeCode != ''">#{helpTypeCode},</if>
            <if test="helpType != null  and helpType != ''">#{helpType},</if>
            <if test="nature != null  and nature != ''">#{nature},</if>
            <if test="functionalDepartment != null  and functionalDepartment != ''">#{functionalDepartment},</if>
            <if test="multipartyId != null">#{multipartyId},</if>
            <if test="consultLanguageCode != null  and consultLanguageCode != ''">#{consultLanguageCode},</if>
            <if test="consultLanguage != null  and consultLanguage != ''">#{consultLanguage},</if>
            <if test="thirdParty != null">#{thirdParty},</if>
            <if test="hotLineId != null">#{hotLineId},</if>
            <if test="cityCode != null  and cityCode != ''">#{cityCode},</if>
            <if test="recordType != null ">#{recordType},</if>
            <if test="ivrSkillGroupId != null ">#{ivrSkillGroupId},</if>
            <if test="endPerson != null">#{endPerson},</if>
            <if test="callState != null">#{callState},</if>
            <if test="realCallState != null">#{realCallState},</if>
            <if test="ivrSkillGroupName != null and ivrSkillGroupName != ''">#{ivrSkillGroupName},</if>
            <if test="originalCallTelephone != null and originalCallTelephone != ''">#{originalCallTelephone},</if>
            <if test="domain != null and domain != ''">#{domain},</if>
        </trim>
    </insert>
    <insert id="insertCallcenterRecordDetailSoundAsrResult" parameterType="com.gobon.project.conversation.domain.CallcenterRecordDetailSoundAsrresultEntity" useGeneratedKeys="true" keyProperty="id">
        insert into callcenter_record_detail_sound_asrresult(call_record_detail_id,result_json,create_time) values (#{callRecordDetailId},#{resultJson},#{createTime})
    </insert>

    <update id="updateCallcenterRecordDetailSound" parameterType="com.gobon.project.conversation.domain.CallcenterRecordDetailSound">
        update callcenter_record_detail_sound
        <trim prefix="SET" suffixOverrides=",">
            <if test="soundText != null  and soundText != ''">sound_text = #{soundText},</if>
            <if test="conversationRole != null  and conversationRole != ''">conversation_role = #{conversationRole},</if>
            <if test="userId != null "> user_id = #{userId},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateSountTextState">
        update callcenter_record_detail set sound_text_state = #{state} where id = #{id}
    </update>
    <update id="recordAbnormalCauses">
        update callcenter_record_detail set abnormal_causes = #{abnormalCauses} where id = #{recordId}
    </update>
    <update id="updateDefaultPraise">
        update callcenter_record_detail set statisf_name = '满意', satisf_id = 11, default_praise = 1
        where answer_state = 1 and out_time is not null and type in (0,1)
        and DATE_SUB(CURDATE(), INTERVAL 5 DAY) >= date(out_time) and statisf_name is null and satisf_id is null
    </update>
    <update id="updateDetailRealHangup">
        update callcenter_record_detail set real_call_state = #{endPerson} where trace_id = #{traceId} and type = 0
    </update>

    <select id="selectCallcenterRecordDetailSoundByCallRecordDetailId" resultType="com.gobon.project.conversation.domain.CallcenterRecordDetailSound" parameterType="Long">
         select * from callcenter_record_detail_sound t  where call_record_detail_id = #{callRecordDetailId}
    </select>

    <select id="selectCallcenterRecordDetailListByRecordId"
            resultType="com.gobon.project.conversation.domain.CallcenterRecordDetail">
         select
            *
        from callcenter_record_detail
        where
            record_id = #{recordId}
            order by id asc
    </select>

    <select id="selectCallcenterRecordDetailByRecordId"
            resultType="com.gobon.project.conversation.domain.CallcenterRecordDetail">
         select
            *
        from callcenter_record_detail
        where
            record_id = #{recordId}
            order by id desc
            limit 1
    </select>

    <select id="selectNeededData"  parameterType="com.gobon.project.conversation.domain.param.PyParam" resultType="com.gobon.project.conversation.domain.vo.AudioHandleVO">
        SELECT
            c.id AS sesrviceId,
            c.type,
            c.user_id,
            u.nick_name,
            u.user_number,
            u.user_audio
        FROM
        callcenter_record_detail c
        LEFT JOIN sys_user u ON c.user_id = u.user_id
        where
        1 = 1
        and c.answer_state = 1
        <choose>
            <when test='type != null and type != "" and (type == "2" or type == "3")'>
                and c.id = #{callRecordDetailId}
                or c.multiparty_id = #{multipartyId}
            </when>
            <otherwise>
                and c.id = #{callRecordDetailId}
            </otherwise>
        </choose>

    </select>

    <!-- 质检筛选通话数据 -->
    <!-- and (length(su.user_audio) - length(replace(su.user_audio,',',''))) >= 4 -->
    <select id="selectQualityCallInfos"
            parameterType="com.gobon.project.intelligencequality.domain.param.QualityScanParam"
            resultType="com.gobon.project.intelligencequality.domain.vo.QualityCallInfoVo">
        select crd.id,
            crd.record_id,
            crd.conversation_time duration,
            crd.end_person hangup,crd.call_time as callTime
            from callcenter_record_detail crd
            inner join sys_user su on su.user_id = crd.user_id
            where su.addr_area = #{param.domain}
            and out_time is not null
            and crd.type in (0, 1, 4, 5)
            and ivr_skill_group_name like '普%'
            <!--and crd.id in (56864)-->
            and conversation_time >= 60
            <if test="param.skillString != null and param.skillString != ''">
                and INTE_ARRAY(su.skill_group_id, #{param.skillString})
            </if>
            and crd.call_time <![CDATA[>=]]> #{param.startTime}
            and crd.call_time <![CDATA[<]]> #{param.endTime}
            and division_mark = 1
            <if test="param.recordType != null">
                and crd.record_type = #{param.recordType}
            </if>
            <if test="param.agents != null">
                and crd.user_id in
                <foreach item="item" collection="param.agents" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="param.satisfactionItem != null">
                and (crd.statisfied_code in
                <foreach item="item" collection="param.satisfactionItem" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                <if test="param.includeNotEvaluate != null and param.includeNotEvaluate == 1">
                    or crd.statisfied_code is null
                </if>
                )
            </if>
            <!--<if test="param.dialogueNumber != null">-->
            <!--    and (select count(1)-->
            <!--    from callcenter_record_detail_sound crds-->
            <!--    where crds.call_record_detail_id = crd.id) <![CDATA[>=]]> #{param.dialogueNumber}-->
            <!--</if>-->
            and not exists(select 1
                from callcenter_quality_info cqi
                    inner join callcenter_quality_detail cqde on cqi.id = cqde.quality_info_id
                 where cqde.del_flag = 0
                   and crd.id = cqi.call_detail_id
                   and cqi.domain = #{param.domain}
                   and cqde.quality_type = #{param.qualityType}
                   and cqde.scoring_rule_id = #{param.scoringRuleId} and cqde.sample_rule_id = #{param.sampleRuleId}
                   and exists(select 1 from callcenter_quality_scoring_record cqsre where cqsre.quality_info_id = cqi.id and cqsre.quality_detail_id = cqde.id))
                   <if test="param.range != null and param.range >= 1">
                       order by rand() limit #{param.range}
                   </if>
        <!--order by crd.call_time desc limit 200-->
    </select>

    <select id="selectQualityCallSound" parameterType="Long"
            resultType="com.gobon.project.intelligencequality.domain.vo.QualityCallSoundVo">
        select id,
               call_record_detail_id          call_detail_id,
               user_id,
               sound,
               sound_text,
               create_time,
               start_second,
               end_second,
               conversation_role `role`,
               cast(start_second as decimal(20, 6)) start_time,
               cast(end_second as decimal(20, 6)) end_time
        from callcenter_record_detail_sound crds
        where crds.call_record_detail_id = #{callDetailId}
    </select>

    <select id="selectList" parameterType="com.gobon.project.conversation.domain.param.CallcenterRecordDetailListParam"
            resultType="com.gobon.project.conversation.domain.vo.CallcenterRecordDetailVO">
        select * from
            callcenter_record_detail
        where
        1 = 1
            <if test="soundTextState != null and soundTextState != ''"> and sound_text_state != #{soundTextState}</if>
            <if test="divisionMark != null and divisionMark != ''"> and division_mark = #{divisionMark}</if>
            <if test="answerState != null and answerState != ''"> and answer_state = #{answerState}</if>
            <if test="userId != null and userId != ''"> and user_id = #{userId}</if>
        limit 200
    </select>


    <select id="selectNoConversationRoleList" resultType="com.gobon.project.conversation.domain.vo.CallcenterRecordDetailVO">
        select d.* from callcenter_record_detail d
        where d.id in
        (
        select s.call_record_detail_id from callcenter_record_detail_sound s
        where s.conversation_role is null  group by s.call_record_detail_id
        )
        and d.sound_text_state != 3
    </select>
    <select id="selectRecordListByPhone"
            resultType="com.gobon.project.conversation.domain.vo.CallcenterRecordDetailVO">
        select id, call_time, record_type, service_type_name from callcenter_record_detail
        where type in (0,1) and call_phone = #{phoneNumber} or user_phone = #{phoneNumber}
        order by call_time desc
    </select>

    <select id="selectDetailListByRecordId" parameterType="com.gobon.project.conversation.domain.param.CallcenterRecordDetailListParam" resultType="com.gobon.project.conversation.domain.vo.CallRecordDetailslSimpleVO">
        select
        timestampdiff( SECOND, d.start_time, d.out_time ) conversation_time,
        d.*,
        (case when d.type = 0 and d.out_ivr_time is not null
			and (timestampdiff(SECOND, d.out_ivr_time, d.distribution_time) > 2 or d.distribution_time is null) then timestampdiff(SECOND, d.out_ivr_time, d.distribution_time) else 0 end) line_up_time, -- 排队时长
        concat( d.province, '-', d.city ) come_from,
        CASE
        d.type
        WHEN '0' THEN
        '一对一'
        WHEN '1' THEN
        '转接'
        WHEN '2' THEN
        '求助'
        WHEN '3' THEN
        '多方'
        WHEN '4' THEN
        '拦截'
        WHEN '5' THEN
        '强拆'
        END 'type_desc',
        CASE
        d.answer_state
        WHEN '-1' THEN
        '进入队列'
        WHEN '0' THEN
        '客服未接听'
        WHEN '1' THEN
        '接通'
        WHEN '2' THEN
        '无人应答'
        WHEN '3' THEN
        '群众快速挂机'
        WHEN '4' THEN
        '无法接通'
        WHEN '6' THEN
        '群众未接听'
        END 'answer_state_desc',
        d.target_id,
        d.customer_id,
        d.ivr_skill_group_name skillName,
        if(t3.user_type = '1', '律师', if(t3.user_type = '2', '客服', null)) role_name,
        t4.dept_name,
        d.statisf_name,
        ifnull(d.service_type_id, ifnull(td2.desc, ifnull(td.desc, if(original_call_telephone = '12348', '广东-广州', city.city)))) intoCity<!--,
        crde.summary as consultContent-->
        from callcenter_record_detail d
        left join sys_user t3 on d.user_id = t3.user_id
        left join sys_dept t4 on d.dept_id = t4.dept_id
        left join city_prefix city on LEFT(d.original_call_telephone,4) = city.prefix_num
        left join t_third_phone td on d.original_call_telephone = td.real_phone
        left join t_third_phone td2 on d.call_phone = td2.real_phone
        <!--left join (
            select t.* from (select ROW_NUMBER() OVER (PARTITION BY record_detail_id ORDER BY t.create_time DESC ) as unikey,
            t.record_detail_id,t.summary
            from callcenter_record_detail_ext t group by t.record_detail_id) t where t.unikey = 1) crde on crde.record_detail_id = d.id-->
        where d.record_id = #{recordId}
        order by call_time desc
    </select>

    <select id="getCustomerStatistics" parameterType="String" resultType="com.gobon.project.conversation.domain.vo.CustomerStatisticsVO">
        SELECT
            sum(CASE WHEN d.record_type = 0 and d.type = 0 THEN 1 ELSE 0 END) consultation_volume,
            sum(CASE WHEN d.statisf_name in ('满意', '非常满意') THEN 1 ELSE 0 END) praise_total
        FROM
            callcenter_record_detail d
        WHERE
            d.call_phone = #{phone}
    </select>
    <select id="getDefaultPraiseList"
            resultType="com.gobon.project.conversation.domain.vo.CallcenterRecordDetailVO">
        select * from callcenter_record_detail
        where answer_state = 1 and out_time is not null and type in (0,1)
        and DATE_SUB(CURDATE(), INTERVAL 5 DAY) >= date(out_time) and statisf_name is null and satisf_id is null
    </select>
    <select id="getLastRecord" resultType="com.gobon.project.conversation.domain.vo.CallcenterRecordDetailVO">
    SELECT * FROM `callcenter_record_detail`
    where trace_id = #{traceId} and type in (0, 1) ORDER BY call_time desc limit 1
    </select>
    <select id="selectCallcenterRecordByFreeswitchTraceId"
            resultType="com.gobon.project.conversation.domain.CallcenterRecordDetail">
        SELECT * FROM callcenter_record_detail
        where type = 0 and trace_id = #{traceId} and record_type = 0
        LIMIT 1
    </select>
    <select id="selectSeatsListHs"
            resultType="com.gobon.project.conversation.domain.vo.CallcenterRecordDetailHsVO">
        select bill_number, call_time, out_time, call_phone, ivr_skill_group_name
        from callcenter_record_detail
        where call_time >= #{conversationStartTime} and call_time <![CDATA[<=]]> #{conversationEndTime}
        and record_type = 0 and type = 0 and ivr_skill_group_id is not null and answer_state = 4 and (
        (call_time >= '2023-10-01 00:00:00' and call_time <![CDATA[<=]]> '2023-10-14 23:59:59' and timestampdiff(SECOND, out_ivr_time, ifnull(distribution_time, IFNULL(start_time, out_time))) > 120) or
        (call_time >= '2023-10-15 00:00:00' and call_time <![CDATA[<=]]> '2023-10-31 23:59:59' and timestampdiff(SECOND, out_ivr_time, ifnull(distribution_time, IFNULL(start_time, out_time))) > 90) or
        (call_time >= '2023-11-01 00:00:00' and call_time <![CDATA[<=]]> '2023-11-30 23:59:59' and timestampdiff(SECOND, out_ivr_time, ifnull(distribution_time, IFNULL(start_time, out_time))) > 90) or
        (call_time >= '2023-12-01 00:00:00' and call_time <![CDATA[<=]]> '2025-05-31 23:59:59' and timestampdiff(SECOND, out_ivr_time, ifnull(distribution_time, IFNULL(start_time, out_time))) > 30) or
        (call_time >= '2025-06-01 00:00:00') or
        (call_time <![CDATA[<=]]> '2023-10-01 00:00:00' and timestampdiff(SECOND, out_ivr_time, ifnull(distribution_time, IFNULL(start_time, out_time))) > 120))
    </select>
    <select id="selectSeatsListNotGood"
            resultType="com.gobon.project.conversation.domain.vo.CallcenterRecordDetailNotGoodVO">
        SELECT bill_number ,
               call_phone ,
               call_time ,
               user_name ,
               user_number ,
               ifnull(td2.desc, ifnull(td.desc, ifnull((case when original_call_telephone = '12348' then '广东-广州' else city.city end), concat( d.province, '-', d.city )))) fromCity,
               d.statisf_name , d.ivr_skill_group_name
        FROM callcenter_record_detail d
         left join city_prefix city on d.original_call_telephone = concat(prefix_num, '12348')
         left join t_third_phone td on d.original_call_telephone = td.real_phone
         left join t_third_phone td2 on d.call_phone = td2.real_phone
        where record_type = 0 and type = 0 and statisf_name in ('不满意', '非常不满意', '基本满意')
          and call_time >= #{conversationStartTime} and call_time <![CDATA[<=]]> #{conversationEndTime}
    </select>
    <select id="getRecordDetailById"
            resultType="com.gobon.project.conversation.domain.vo.CallRecordDetailslSimpleVO">
        select
            timestampdiff( SECOND, d.start_time, d.out_time ) conversation_time,
            d.*,
            (case when d.type = 0 and d.out_ivr_time is not null
                and (timestampdiff(SECOND, d.out_ivr_time, d.distribution_time) > 2 or d.distribution_time is null) then timestampdiff(SECOND, d.out_ivr_time, d.distribution_time) else 0 end) line_up_time, -- 排队时长
            concat( d.province, '-', d.city ) come_from,
            CASE
                d.type
                WHEN '0' THEN
                    '一对一'
                WHEN '1' THEN
                    '转接'
                WHEN '2' THEN
                    '求助'
                WHEN '3' THEN
                    '多方'
                WHEN '4' THEN
                    '拦截'
                WHEN '5' THEN
                    '强拆'
                END 'type_desc',
                CASE
                    d.answer_state
                    WHEN '-1' THEN
                        '进入队列'
                    WHEN '0' THEN
                        '客服未接听'
                    WHEN '1' THEN
                        '接通'
                    WHEN '2' THEN
                        '无人应答'
                    WHEN '3' THEN
                        '群众快速挂机'
                    WHEN '4' THEN
                        '无法接通'
                    WHEN '6' THEN
                        '群众未接听'
                    END 'answer_state_desc',
                d.target_id,
            d.customer_id,
            d.ivr_skill_group_name skillName,
            if(t3.user_type = '1', '律师', if(t3.user_type = '2', '客服', null)) role_name,
            t4.dept_name,
            d.statisf_name
        from callcenter_record_detail d
         left join sys_user t3 on d.user_id = t3.user_id
         left join sys_dept t4 on d.dept_id = t4.dept_id
        where d.id = #{id}
    </select>

    <delete id="deleteSoundsByDetailId">
        delete from callcenter_record_detail_sound where call_record_detail_id =#{detailId}
    </delete>
    <delete id="deleteSoundAsrResultByDetailId">
        delete from callcenter_record_detail_sound_asrresult where call_record_detail_id  =#{detailId}
    </delete>

    <select id="getNewAsrDetailList" resultType="com.gobon.project.conversation.domain.CallcenterRecordDetailEntity">
        SELECT *
          FROM callcenter_record_detail
         WHERE call_time >= #{startDate}
           AND call_time <![CDATA[<=]]> #{endDate}
           AND conversation_time > 15
           AND division_mark = '0'
           AND answer_state = 1
           AND out_time is not null
         ORDER BY call_time DESC
         LIMIT 100
    </select>

    <select id="getNoMarkDetailList" resultType="com.gobon.project.conversation.domain.CallcenterRecordDetailEntity">
        SELECT *
        FROM callcenter_record_detail
        WHERE out_time >= #{startDate}
        AND out_time <![CDATA[<=]]> #{endDate}
        AND division_mark = '0'
        and user_id is not null and record_type = '0'
        AND answer_state = 1
        AND out_time is not null
        ORDER BY out_time DESC
        LIMIT 50
    </select>

    <select id="selectAllCallcenterRecordDetailByDetailId"
            resultType="com.gobon.project.conversation.domain.CallcenterRecordDetail">
        select t.id as id,t.sound as sound,t.bill_number as billNumber from callcenter_record_detail t where t.record_id in
        (select t.record_id from callcenter_record_detail t where t.id = #{detailId})
    </select>
    <select id="selectRecordDetailData"
            resultType="com.gobon.project.conversation.domain.CallcenterRecordDetailEntity">
        select t.id as id,t.bill_number as billNumber,t.call_time as callTime from callcenter_record_detail t
        <where>
            1=1 and exists(select 1 from callcenter_record_detail_sound t1 where t1.call_record_detail_id = t.id)
            and not exists(select 1 from callcenter_record_detail_ext t2 where t2.record_detail_id = t.id)
            <if test="startDate != null">
                and t.call_time &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                and t.call_time &lt;= (#{endDate} + INTERVAL 1 DAY)
            </if>
        </where>
        limit ${limit}
    </select>
</mapper>
