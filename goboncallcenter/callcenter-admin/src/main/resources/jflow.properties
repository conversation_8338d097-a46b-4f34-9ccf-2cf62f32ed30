mysql\u6570\u636e\u5e93\u914d\u7f6e\u793a\u4f8b
AppCenterDBType=mysql
AppCenterDBDatabase=contactcentermaster
AppCenterDSN=jdbc:mysql://*************:50002/contactcentermaster?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
JflowUser=root
JflowPassword=secret
JflowTestSql=SELECT 'AA'
driverClassName=com.mysql.jdbc.Driver
#\u670d\u52a1\u5668\u7684URL,\u7528\u4e8ecs\u6d41\u7a0b\u670d\u52a1\u4e2d\u83b7\u53d6url\u4fe1\u606f\uff0c\u6765\u6267\u884c\u6d41\u7a0b\u4e8b\u4ef6
HostURL=http://localhost:8080/jflow-web
#\u9644\u4ef6\u4fdd\u5b58\u4f4d\u7f6e
SavaPath=Temp/App/
#SDK\u8868\u5355\u4e0a\u670d\u52a1\u5668\u5730\u5740,\u5e94\u7528\u5230\u4f7f\u7528ccflow\u7684\u65f6\u5019\u4f7f\u7528\u7684\u662fsdk\u8868\u5355,\u8be5\u8868\u5355\u4f1a\u5b58\u50a8\u5728\u5176\u4ed6\u7684\u670d\u52a1\u5668\u4e0a.
SDKFromServHost=127.0.0.1
#\u7ec4\u7ec7\u7ed3\u6784\u6570\u636e\u6a21\u5f0f, 0=OneOne\u6a21\u5f0f(\u4e00\u4e2a\u7528\u6237\u4e00\u4e2a\u90e8\u95e8\u591a\u5c97\u4f4d\uff09 1=OneMore\u6a21\u5f0f. \u4e00\u4e2a\u7528\u6237\u591a\u4e2a\u90e8\u95e8\u591a\u5c97\u4f4d.
OSModel=1
#\u7ec4\u7ec7\u7ed3\u6784\u6570\u636e\u6765\u6e90. 0=\u901a\u8fc7\u6570\u636e\u5e93,1=\u901a\u8fc7websercices,2=AD
OSDBSrc=0
#\u662f\u5426\u5728\u5df2\u5b8c\u6210\u4e0e\u5728\u9014\u4e2d\u663e\u793a\u6284\u9001
IsAddCC=0
#********   BP\u6846\u67b6\u7684\u901a\u7528\u914d\u7f6e\u90e8\u5206 ************************************************************
# \u662f\u5426:debug\u72b6\u6001. 0 \u8868\u793a\u4e0d\u662f, 1 \u662f\uff0c\u5982\u679c\u7cfb\u7edf\u53d1\u5e03\u540e\uff0c\u8bf7\u5c06\u6b64\u4fee\u6539\u62100\uff0c\u4ee5\u63d0\u9ad8\u6267\u884c\u6548\u7387\u3002
IsDebug=0
# \u5728\u6d41\u7a0b\u8fd0\u884c\u7ed3\u675f\u540e\uff0c\u662f\u5426\u8981\u5220\u9664\u6d41\u7a0b\u6ce8\u518c\u8868\u7684\u6570\u636e
#\u8bf4\u660e:\u5bf9\u4e8e\u6570\u636e\u91cf\u8f83\u5927\u7684\u7528\u6237\u5c31\u5220\u9664\uff0c\u5df2\u7ecf\u5b8c\u6210\u7684\u6d41\u7a0b\u4ece\u6d41\u7a0b\u6570\u636e\u8868\u6216\u8005V_FlowData\u8bbf\u95ee\uff0c\u4e0d\u5927\u7684\u7528\u6237\u5c31\u4e0d\u8981\u5220\u9664.
IsDeleteGenerWorkFlow=0
#\u662f\u5426\u662f\u96c6\u56e2\u4f7f\u7528
IsUnit=0
#\u5bcc\u6587\u672c\u6587\u4ef6\u4e0a\u4f20\u4f4d\u7f6e
RichTextBoxAttached=\\DataUser\\RichTextBoxAttached\\
ChartImageHandler=storage=file;tiemout=20;dir=~/DataUser/TempImageFiles/  
#********   CCFlow\u5e94\u7528\u914d\u7f6e\u90e8\u5206 ************************************************************
#\u8f85\u52a9\u7ba1\u7406\u5458
adminers=admin,system,
#\u7528\u6237\u4fe1\u606f\u663e\u793a\u683c\u5f0f @0=UserID,UserName@1=UserNo,@2=UserName
UserInfoShowModel=0
#\u662f\u5426\u4e3a\u6d41\u7a0b\u589e\u52a0\u4e00\u4e2a\u4f18\u5148\u7ea7
IsEnablePRI=0
#\u662f\u5426\u542f\u7528\u5171\u4eab\u4efb\u52a1\u6c60
IsEnableTaskPool=1
#\u662f\u5426\u542f\u7528\u8349\u7a3f
IsEnableDraft=1
#\u662f\u4e0d\u662fBS\u7cfb\u7edf\u7ed3\u6784(1\u4ee3\u8868\u662f 0\u4ee3\u8868\u4e0d\u662f)
IsBSsystem=1
#\u662f\u5426\u68c0\u67e5\u6811\u5f62\u8868\u5355\u4e3a\u7a7a\u7684\u903b\u8f91\uff08\u4e3a\u6d4b\u8bd5\u6240\u4f7f\u7528,\u6a21\u5f0f\u4e3a1\uff09
IsEnableCheckFrmTreeIsNull=0
# \u662f\u5426\u542f\u7528\u5236\u5ea6
IsEnableZhiDu=0
#\u662f\u5426\u542f\u7528\u6570\u503c\u7c7b\u578b\u4e3a\u7a7a\u503c\uff0c\u5728\u4e00\u4e9b\u5de5\u4f5c\u73af\u5883\u4e0b\uff0c
#\u6570\u503c\u7c7b\u578b\u7684\u6570\u636e\u5982\u679c\u7528\u6237\u4e0d\u8f93\u5165\u5c31\u9ed8\u8ba4\u4e3anull\uff0c\u663e\u793a\u65f6\u4e5f\u8981\u663e\u793a""\uff0c
#\u800c\u975e\u9ed8\u8ba4\u4e3a0\u3002\u5982\u679c\u8981\u542f\u7528\u8fd9\u4e2a\u73af\u5883\u4e0b\u7684\u5e94\u7528\u5c31\u9700\u8981\u8bbe\u7f6e0\uff0c\u9ed8\u8ba4\u4e3a0.
IsEnableNull=1
#\u53ef\u4ee5\u6253\u5f00*\u5c4f\u853d\u7684\u5173\u952e\u5b57*\u5904\u7406\u7684\u7c7b\u578b,\u7528\u4e8e\u591a\u9644\u4ef6\u5904\u7406*\u5c4f\u853d\u7684\u5173\u952e\u5b57*\u7684\u6253\u5f00\u63a7\u5236: doc,docx,pdf\uff0c \u6ce8\u610f\u8981\u5c0f\u5199.
OpenTypes=doc,docx,pdf,xls,xlsx
#\u5206\u9875\u6570\u636e\u5927\u5c0f,\u6bcf\u9875\u663e\u793a\u7684\u6570\u91cf.(\u4e0d\u8981\u4f4e\u4e8e5)
PageSize=15
#\u4e3addl\u6807\u7b7e\u7684\u5143\u7d20\u6700\u5927\u663e\u793a\u884c\u6570\uff0c\u8d85\u8fc7\u5219\u663e\u793a"..."\u6309\u94ae
MaxDDLNum=12
#\u662f\u5426\u542f\u7528\u6d88\u606f\u673a\u5236
IsEnableSysMessage=1
#\u662f\u5426\u542f\u7528\u68c0\u67e5\u7528\u6237\u7684\u72b6\u6001
IsEnableCheckUseSta=0 
#\u5982\u679c\u628accflow\u653e\u5165\u524d\u53f0\u5e94\u7528\u7a0b\u5e8f\u7684\u8ddf\u76ee\u5f55\u4e0b\u914d\u7f6e\u4fe1\u606f\u8def\u5f84
DataDirPath=WF
#\u7528\u6237\u6570\u636e\u8def\u5f84
DataUserDirPath=
#\u5e94\u7528\u7a0b\u5e8f\u76ee\u5f55
CCFlowAppPath=/
#\u662f\u5426\u663e\u793a\u7528\u6237\u540d\u4e0e\u5728\u8f68\u8ff9\u8282\u70b9\u91cc,\u9ed8\u8ba4\u4e3a1.
FlowDesignerIsShowUserNameIsNode=1
#\u662f\u5426\u542f\u7528\u5f00\u59cb\u4e0e\u865a\u62df\u8282\u70b9,\u9ed8\u8ba4\u4e3a1.
FlowDesignerIsEnableStartEndNode=1
#\u53d1\u9001\u90ae\u4ef6\u7684\u8bbe\u7f6e\uff0c\u4e3a\u6d41\u7a0b\u670d\u52a1\u8bbe\u7f6e\u7684\u90ae\u4ef6\u53d1\u9001\u3002
SendEmailHost=smtp.tom.com
SendEMailPort=25
SendEmailAddress=<EMAIL>
SendEmailPass=ccbpm123
#\u662f\u5426\u542f\u7528ssl \u52a0\u5bc6? \u6709\u7684\u90ae\u4ef6\u670d\u52a1\u5668\u4e0d\u9700\u8981\u52a0\u5bc6\uff0c\u5c31\u8bbe\u7f6e\u4e3a0.
SendEamilEnableSsl=1
#\u9ed8\u8ba4\u8bed\u8a00 CH \u4e3a\u7b80\u4f53\u4e2d\u6587,\u5176\u5b83\u8bed\u8a00\u8bf7\u53c2\u8003\u679a\u4e3e
SysLanguage=CH
#\u521d\u59cb\u5316\u7684\u6570\u636e\u5e93\u7f13\u5b58\u6c60\u6570\u91cf\u6839\u636e\u81ea\u5df1\u7684\u670d\u52a1\u5668\u6027\u80fd\uff0c\u4e0e\u8bbf\u95ee\u8005\u591a\u5c11\u7684\u9700\u8981,\u8bbe\u7f6e\u76f8\u5e94\u7684\u53c2\u6570,\u4ee5\u63d0\u9ad8\u5e76\u53d1\u8bbf\u95ee\u6548\u7387.
InitConnNum=10
#\u7cfb\u7edf\u7f16\u53f7:\u4e0d\u8981\u4fee\u6539\u5b83.
SysNo=CCFlow
SysName=\u9a70\u9a8b\u5de5\u4f5c\u6d41\u7a0b\u7ba1\u7406\u7cfb\u7edf
#\u5ba2\u6237\u7f16\u53f7:\u6b64\u7f16\u53f7\u7528\u6237\u5904\u7406\u4e0d\u540c\u7684\u5ba2\u6237\u4e2a\u6027\u5316\u7684\u8981\u6c42.
CustomerNo=ccflow
CustomerName=\u6d4e\u5357\u9a70\u9a8b\u4fe1\u606f\u6280\u672f\u6709\u9650\u516c\u53f8
#\u6bcf\u6b21\u53d6\u6700\u5927\u6570\u636e\u884c\u6570(\u4e0d\u8981\u4fee\u6539)
TopNum=999999
#\u7fa4\u96c6IP: \u5bf9\u4e8e\u7fa4\u96c6\u6709\u6548
CIP=127.0.0.1
#*\u5c4f\u853d\u7684\u5173\u952e\u5b57*ftp\u670d\u52a1\u5668\u914d\u7f6e:\u5982\u679c\u6ca1\u6709\u7528\u5230\u5c31\u53ef\u4ee5\u5ffd\u7565\u6389.
FTPServerType=FTP
FTPServerIP=ccflow.org
FTPUserNo=FtpTest
FTPUserPassword=asdfasd3**$232d00932a1ldw
#\u9644\u4ef6\u4e0a\u4f20\u662f\u5426\u52a0\u5bc6 1 \u52a0\u5bc6
IsEnableAthEncrypt=0
#\u6587\u4ef6\u662f\u5426\u4e0a\u4f20\u5230FTP
IsUploadFileToFTP=0
#\u670d\u52a1\u4fe1\u606f: \u5f53\u7a0b\u5e8f\u51fa\u73b0\u5f02\u5e38\u5c31\u4f1a\u663e\u793a\u8be5\u4fe1\u606f,\u4ee5\u65b9\u4fbf\u901a\u77e5\u7ba1\u7406\u5458\u5904\u7406.
ServiceMail=<EMAIL>
ServiceTel=0531-82374939
#\u7528\u6765\u60df\u4e00\u6807\u8bc6\u7528\u6237\uff0c\u4e3b\u8981\u7528\u6765\u6839\u636e\u4e0d\u540c\u7684\u7528\u6237\u663e\u793a\u4e0d\u540c\u7684Logo
CompanyID=CCFlow
About=\u6d4e\u5357\u9a70\u9a8b\u4fe1\u606f\u6280\u672f\u516c\u53f8\u7814\u53d1@2003-2018
#\u4f7f\u7528\u7684\u670d\u52a1\u5668tomcat,jetty
Service=tomcat
#FTP \u76f8\u5173\u7684
#\u5728FTP\u4e0a\u7684\u60df\u4e00\u6807\u8bc6,\u6700\u597d\u6362\u6210\u516c\u53f8\u6216\u4e2a\u4eba\u7684\u82f1\u6587\u540d(\u53ea\u6709ccflow\u7684VIP\u5ba2\u6237\u624d\u5141\u8bb8)
UserIdentifier=CCFlow
#\u5355\u72ec\u8fd0\u884c\u6a21\u5f0fSingle, \u57df\u8fd0\u884c\u6a21\u5f0fDomain.
AppCenterDBModel=Single
#----------------------------------------------------------------------------------------
ShortMessageWriteTo=3
#PDF\u6253\u5370\u662f\u5426\u663e\u793a\u6c34\u5370
IsShowShuiYin=0
#PDF\u6253\u5370\u662f\u5426\u663e\u793a\u4e8c\u7ef4\u7801
IsShowQrCode=1
#SpringBoot\u53d1\u5e03\u65b9\u5f0f
IsStartJarPackage=1
#\u9759\u6001\u5730\u5740  \u4fee\u6539\u5230 DataUser \u548c Wf \u6587\u4ef6\u5939
#ServicePath=D:/work2020/goboncall-vue/gobon-contact-center/goboncallcenter/callcenter-admin/src/main/resources/JFlow/
