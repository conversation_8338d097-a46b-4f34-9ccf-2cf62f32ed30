{"properties": [{"name": "spring.datasource.phoenix.driverClassName", "type": "java.lang.String", "description": "Description for spring.datasource.phoenix.driverClassName."}, {"name": "spring.datasource.phoenix.connection-test-query", "type": "java.lang.String", "description": "Description for spring.datasource.phoenix.connection-test-query."}, {"name": "gobon.domain", "type": "java.lang.String", "description": "Description for gobon.domain."}]}