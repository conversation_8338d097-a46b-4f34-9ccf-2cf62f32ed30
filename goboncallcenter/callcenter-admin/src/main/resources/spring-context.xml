<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx" xmlns:cache="http://www.springframework.org/schema/cache"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
	http://www.springframework.org/schema/cache
	http://www.springframework.org/schema/cache/spring-cache-3.1.xsd
	http://www.springframework.org/schema/context
	http://www.springframework.org/schema/context/spring-context-3.1.xsd
	http://www.springframework.org/schema/aop
	http://www.springframework.org/schema/aop/spring-aop-3.1.xsd
	http://www.springframework.org/schema/tx
	http://www.springframework.org/schema/tx/spring-tx-3.1.xsd">

    <bean id="propertyConfigurer"
          class="com.gobon.BP.Difference.GvtvPropertyPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>classpath:jflow.properties</value>
            </list>
        </property>
    </bean>

    <!-- 默认数据源配置, 使用 Druid 数据库连接池 -->
    <bean id="dataSourceJflow" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">
        <!-- 数据源驱动类可不写，Druid默认会自动根据URL识别DriverClass
        <property name="driverClassName" value="${jdbc.driver}" /> -->

        <!-- 基本属性 url、user、password -->
        <property name="url" value="${AppCenterDSN}"/>
        <property name="username" value="${JflowUser}"/>
        <property name="password" value="${JflowPassword}"/>

        <!-- 配置初始化大小、最小、最大 -->
        <property name="initialSize" value="1"/>
        <property name="minIdle" value="3"/>
        <property name="maxActive" value="20"/>

        <!-- 配置获取连接等待超时的时间 -->
        <property name="maxWait" value="60000"/>

        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <property name="timeBetweenEvictionRunsMillis" value="60000"/>

        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <!-- 泄露的连接可以被删除的超时值，单位秒
        <property name="removeAbandoned" value="true" />
        <property name="removeAbandonedTimeout" value="1800" />-->

        <!-- 该选项用来验证数据库连接池的有效性 , 如果是 -->
        <property name="validationQuery" value="${JflowTestSql}"/>
        <property name="testWhileIdle" value="false"/>
        <!-- testOnBorrow true 指明是否在从池中取出连接前进行检验,如果检验失败,则从池中去除连接并尝试取出另一个. -->
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <!-- 打开PSCache，并且指定每个连接上PSCache的大小（Oracle使用）
        <property name="poolPreparedStatements" value="true" />
        <property name="maxPoolPreparedStatementPerConnectionSize" value="20" /> -->

        <!-- 配置监控统计拦截的filters -->
        <property name="filters" value="stat"/>
    </bean>

</beans>
