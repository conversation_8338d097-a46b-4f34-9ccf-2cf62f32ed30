# 项目相关配置
gobon:
  #redis域
  domain: gd12348.com
  # 名称
  name: gobon
  # 版本
  version: 2.1.0
  # 版权年份
  copyrightYear: 2019
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/goboncallcenter/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /home/<USER>/goboncall/uploadPath/
  # 获取ip地址开关
  addressEnabled: true

  # ftp文件外部链接
  ftpNetUrl: https://bank.easemob.top:8881/fileweb/

  # 语音转文字
  cc:
    api:
      # 调用队列api地址
      queue: http://**************:9982
    speech:
      host-url: https://iat-api.xfyun.cn/v2/iat
      api-secret: e594c60ede5631dc307025c7dc193126
      api-key: af457a4808071db4c35d107dd206bc51
      app-id: 5f338acb
      target-path: callcenter-speechtotext/src/main/resources/tempCut/
    # 文字转语音
    text:
      host-url: https://tts-api.xfyun.cn/v2/tts
      api-secret: e594c60ede5631dc307025c7dc193126
      api-key: af457a4808071db4c35d107dd206bc51
      app-id: 5f338acb
      target-path: /home/<USER>/goboncall/uploadPath/audio/
# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8085
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30
  max-http-header-size: 1024000


# 日志配置
logging:
  level:
    com.gobon: debug
    org.springframework: warn
# Spring配置
spring:
  mvc:
    ### request url .do后缀支持
    pathmatch:
      use-suffix-pattern: true
      #      静态文件
      static-path-pattern: /JFlow/**
    resources:
      static-locations: classpath:/JFlow/
    view:
      prefix: /JFlow/
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size:  200MB
      # 设置总上传的文件大小
      max-request-size:  500MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: false

  # redis 配置
  redis:
    host: **************
    port: 6377
    database: 0
    password: gobon888
    lettuce:
      pool:
        max-active: 100 #连接池最大连接数（使用负值表示没有限制）
        max-idle: 20 # 连接池中的最大空闲连接
        min-idle: 10 # 连接池中的最小空闲连接
        max-wait: -1 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  rabbitmq:
    #    host: **************
    #    port: 5662
    addresses: **************:5672
    username: gd12348
    password: Gobon888
    #虚拟机
    virtual-host: /
    #连接超时时间
    connection-timeout: 15000
    #producer  生产者
    #confirmslistener 消息是否投递成功
    publisher-confirms: true
    #没有队列接收消息时，返回一个状态
    publisher-returns: true
    #true当消息无法被正常送达的时候被返回给生产者，false丢弃
    template:
      mandatory: true
    #consumer 消费者
    listener:
      simple:
        #手动确认消息
        acknowledge-mode: manual
        #最小的消费者数量
        concurrency: 1
        #最大的消费者数量
        max-concurrency: 5
    jk:
      username: gd12348
      password: Gobon888
      virtual-host: /
    fw:
      username: gd12348
      password: Gobon888
      virtual-host: liaoning
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: ************************************************************************************************************************************************************************************
        username: root
        password: Gobon888
      # 从库数据源Gd12348@test
      slave:
        # 从数据源开关/默认关闭
        enabled: true
        url: ******************************************************************************************************************************************************
        username: root
        password: Gobon888
      # oracle
      fworacle:
        # 从数据源开关/默认关闭
        driverClassName: oracle.jdbc.OracleDriver
        enabled: true
        url: jdbc:oracle:thin:@*************6:1521:ORCL
        username: gdfwhxzc
        password: gdfwhxzcDb1
      sound:
        enabled: true
        url: ******************************************************************************************************************************************************************************************
        username: root
        password: Gobon888
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 300
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: false
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username:
        login-password:
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 30000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
        #jpa:
        #hibernate:
        #ddl-auto: create
        #properties:
        #hibernate:
        #dialect: org.hibernate.dialect.MySQL5Dialect
        #current_session_context_class: org.springframework.orm.hibernate5.SpringSessionContext
      # 超过时间限制强制回收 3分钟
      remove-abandoned: true
      remove-abandoned-timeout: 180
# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌秘钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 360

# MyBatis配置
mybatis-plus:
  mapper-locations: classpath*:mybatis/**/*Mapper.xml
  typeAliasesPackage: com.gobon.project.**.domain.** #实体扫描，多个package用逗号或者分号分隔
  configuration:
    cache-enabled: true #缓存
    #log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true #配置返回数据库(column下划线命名&&返回java实体是驼峰命名)，自动匹配无需as（没开启这个，SQL需要写as： select user_id as userId）
  global-config:
    db-column-underline: true #驼峰下划线转换
    #主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
    id-type: 3
    #机器 ID 部分(影响雪花ID)
    workerId: 1
    #数据标识 ID 部分(影响雪花ID)(workerId 和 datacenterId 一起配置才能重新初始化 Sequence)
    datacenterId: 18

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql
  autoRuntimeDialect: true

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# 代码生成
gen:
  # 作者
  author: gobon
  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool
  packageName: com.gobon.project.system
  # 自动去除表前缀，默认是true
  autoRemovePre: false
  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）
  tablePrefix: sys_

ureport:
  disableHttpSessionReportCache: true
  # 将disableFileProvider改成true,即可禁用默认提供的文件存储机制
  mydisableFileProvider: true
  myfileStoreDir: /home/<USER>/goboncall/ureport
  debug: true
  mysql:
    provider:
      # 前缀
      prefix: ureport
      # 是否开启mysql存储，false为开启
      disabled: false
minio:
  endpoint: http://**************:9000
  accesskey: minioadmin
  secretKey: Gobon@888
  localpath: /home/<USER>/data/
netty-websocket:
  host: 0.0.0.0
  port: 8186

exe-websocket:
  host: 0.0.0.0
  port: 8187

# exewebsocket 地址
exe-websocketurl: wss://gd12348.gobon.top/callcenterexe/exewebsocket/

# 接口管理网关
open:
  api:
    common:
      key:
        # 是否校验签名
        isCheckSign: true
        #  开放接口公钥
        publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwdK0le7UypaYWEWBoQkGTpu2nlYnM+iX8pa7Gz9neSnANfcuxrMgmmXrG+Dw6f3OQpiHl4mbKelyVjJTBLh4cvo1am2OSZvBjefZNshphx4ctBtx6BpGIRwlTvJRsjajMCY3RyF6px+Ehz0zeDBf7w2M6GZnSv2YhPp2YIZZo/01GYVJ4RgzzfkEEKyC+96+shqANHVOaiiG4byMJL8zv9q3kshSNCA1NT8r7toq8wPYhUKwCas/i5GauyRCIX+KhCpD9+/HTkFmr0PUWoNIZ61lRpTMbiTfDWU/5tJ3UPwdk6oVM3ZwkLoBAO8HXHvPk6avCupXq63u4wGrn30eiwIDAQAB
        # 开放接口私钥
        privateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDB0rSV7tTKlphYRYGhCQZOm7aeVicz6JfylrsbP2d5KcA19y7GsyCaZesb4PDp/c5CmIeXiZsp6XJWMlMEuHhy+jVqbY5Jm8GN59k2yGmHHhy0G3HoGkYhHCVO8lGyNqMwJjdHIXqnH4SHPTN4MF/vDYzoZmdK/ZiE+nZghlmj/TUZhUnhGDPN+QQQrIL73r6yGoA0dU5qKIbhvIwkvzO/2reSyFI0IDU1Pyvu2irzA9iFQrAJqz+LkZq7JEIhf4qEKkP378dOQWavQ9Rag0hnrWVGlMxuJN8NZT/m0ndQ/B2TqhUzdnCQugEA7wdce8+Tpq8K6lerre7jAauffR6LAgMBAAECggEBAIv3vF9V5KcUD5oXP6BqIvrbagp3zsGmoywVe7MWm4OdCeguw8HME6xME3fDflaL6cqf2bMuNTYUFnR2zQroqFrno3FjAlDXwPPYTT1JhyODNFlARIbHioNYjvyu8x5OZJRd1KdyXt+XXB5JrQSLcovwbiRZ5xf5gI3vTVMxUkSgTxf2P2smaXLZ2k6epSlvFr8u+SJaGOgjKCvbGf+jXyL0L8kntukNLocnSXU3sfFmAmd87DxPFdXAFDnS09tWOcjHfIZmwjHMX3qVP/2jj1DWOjIW0Ow/VRegbYTpLmSQTMcOUaFRprvwd0ZKaZ0aQMNPqPrqkHzrfQsnfjxY+akCgYEA+vbt40rIBDsN5HCPGbyDBU0/+A3wsGh4nqvY9JKACaMpg/FvyMz37GpL8AOMy/mUCVXjVyMoNUFZf/fEhMblBuYQBmgMVk1IQaVESvUlZ33Vgot0TU8YHY2Hpk541e3vKL0X0X6XLgS6CZ63cMx04uZxoFEWlJJm/qqLru3MQp8CgYEAxbZFVgnQ9XTQlHHgpUiS/R7qHo5joBDzlF+m29CYplI5nJUmntoChnHZ6RBoiPW58A3NJOlfIL6J2+Mwwd6kHWD2DSwVDRfk7Hb5Dw6o+tOf+In+zuPZtopr6L7oiKQtwXtGV88ZhDdqX9z5ge9EYP4Psd0Mfchv5vkJENreqJUCgYEAw8easTQHcXV4UvuURymOtLYc7zBA0f3OC0pYiAM5q0sD+hCBeg6cYmxSLT03u3BKEjZUkge1OEZwwanSPxrCVG1plvXYmgLUGZIKAsfXlDLQO3T7F8xaLcPZTN3u2kUxy4Aocp/k5Ft/nj2ZMX/ut4u6nKxlhyXm/0igi6irLlUCgYAWpalNkLRJ2Yam6mB0Llr/+ZGRzHem9yofndFMLpm9u39z6zXQTmKpqdLvOnzu607QK5SLHNxTsN+zu1NzcaBU6S1mFt2WcV08pOgkjGZYzPLvEkeIxVrD6RkxQOT7+epv1kIZftSKa5qYvoQqGRE5FwEPO6XZpqMCzxX1w0xr/QKBgDeom9MiI9a125jr/n9ghSWfvaxCXgPrdojr8QZrlo028iT711ND8QUwCbb9GDr+pyXesANCm78zhdltfeNFimEUktyS0F8Li2+GbYjTvLNXtwxTKZcOXRR+MC5bMHq4hY/+71NhnGazy3yidHn0doReezqGvkotJuRJSr+l1qmU

callcenter-py:
  url: http://**************:6399

elasticsearch:
  host: **************
  port: 9200
  clustername: elasticsearch
  username: elastic
  password: Gobon888

# 文件上传服务器路径
filePath: /home/<USER>/data/

#localFilePath: D:/home/<USER>/goboncall/uploadPath/
localFilePath: /home/<USER>/data/


ftp:
  host: *************
  port: 2121
  username: ftpadmin
  password: ftpadmin
  basePath: /home/<USER>/data/

ai:
  api-base-url: http://openui.easemob.top:3100
  model: qwen2.5
  summary-model: qwen2.5
  api-key: sk-mRWjBQgCOQbeopTDB74724Cd463749F78f5b377e5d2dBc7c

newai:
  api-base-url: http://**************:3100
  api-key: sk-IXALofbbLnhKfOMncbYsEYT7XIDib2B2jCm7FmVYDRSHbLlc
  model: deepseek-chat
  summary-model: gpt-4o
  embedding-model: text-embedding-ada-002
  function-call-model: gpt-4o
  max-tokens: 4096

rsa:
  call:
    private-key: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAN9TAhDhXdspwWRgkcJQLYRCs4G8AakWAuQ9hY/mDXjDywZNHmIwl6+YUvq1WuIqfAbkAoak5MMo5elS+pQfSTn7Vxv05DBKMrTlGRGn5bgu/r0Q0FfHqTwz5TQpRa7XVY1sg14HJVPRBqEmeNmfGxMR9tvOV7GfP3Jn/KC43PAFAgMBAAECgYAGxOranSItqvnYM48nfyuqByWOyKaaO/cReQXr4zLEVtb4FW1W15c5dNoLDGU5VsVUpiOndicS/VHevSuoQryC7WmN7Iv2Vuji0D5xw8rgulolQmta6Xfb2LTTVk/lTZ2FlAQGCYncyva27CtY+6CQz3JQ/wSUulk95eDikv4GIQJBAPYolnJwrW2QiVzASgetEB65xWQSQ1IrthhVCPFLAYRTfsxb/bppGwFtJXfJDv2uQRGAxhMcimg1rI8nmzzS77UCQQDoQLaYkSp5PulXV8/oTj1UZLgysB6Il7VUgGBsl7CsZt90Uy9iuHEmQ3omoTWpBasEYB61jQSv24qNJiavMBERAkA01Q1FTq3KYEU7JQI+l4RoM9ML4WeVkVAp9MZQk5qlvg3u6qeQhNKJAdYS2ZJgHDBXA15/55ZFO4/nwcZtzZApAkAp+viGX0b0H4EB/qdf8DR5urLoxpjeS73FBmdneYu/veCKIDciewBI0E2WCfGvGY0t/0nNuy2rs905Yjn/1IphAkEAhiIr9klVQ5lWYa78gLmuJ5Wo5u7/doBmFv9BPhwFzhgdeoiQgJBml465IZqFuB4ZwlUXfaF4sGcThgqlWCAK4Q==
