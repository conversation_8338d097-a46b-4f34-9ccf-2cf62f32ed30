<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>goboncallcenter</artifactId>
        <groupId>com.gonbon</groupId>
        <version>2.1.0</version>
    </parent>
    <packaging>war</packaging>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>callcenter-admin</artifactId>

    <properties>
        <mybaits-plus.version>3.4.0</mybaits-plus.version>
    </properties>


    <dependencies>
        <!--添加JFlow插件 -->
        <!--<dependency>
            <groupId>com.gonbon</groupId>
            <artifactId>jflow-core</artifactId>
            <version>2.1.0</version>
        </dependency>-->
        <dependency>
            <groupId>com.github.kevinsawicki</groupId>
            <artifactId>http-request</artifactId>
            <version>6.0</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.1.5</version>
        </dependency>
        <dependency>
            <groupId>com.gonbon</groupId>
            <artifactId>callcenter-common</artifactId>
            <version>2.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.gonbon</groupId>
            <artifactId>callcenter-speechtotext</artifactId>
            <version>2.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.gonbon</groupId>
            <artifactId>callcenter-texttospeech</artifactId>
            <version>2.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.gonbon</groupId>
            <artifactId>callcenter-openapi</artifactId>
            <version>2.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>6.8.7</version>
            <!--<exclusions>
                <exclusion>
                    <groupId>org.elasticsearch</groupId>
                    <artifactId>elasticsearch</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.elasticsearch.client</groupId>
                    <artifactId>elasticsearch-rest-client</artifactId>
                </exclusion>
            </exclusions>-->
        </dependency>
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>6.8.7</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>transport</artifactId>
            <version>6.8.7</version>
        </dependency>
        <!--phoenix core-->
<!--        <dependency>-->
<!--            <groupId>org.apache.phoenix</groupId>-->
<!--            <artifactId>phoenix-core</artifactId>-->
<!--            <version>${phoenix.version}</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>org.slf4j</groupId>-->
<!--                    <artifactId>slf4j-log4j12</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>log4j</groupId>-->
<!--                    <artifactId>log4j</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.apache.hadoop</groupId>-->
<!--            <artifactId>hadoop-client</artifactId>-->
<!--            <version>${hadoop.version}</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>org.slf4j</groupId>-->
<!--                    <artifactId>slf4j-log4j12</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
        <!-- https://mvnrepository.com/artifact/org.apache.hadoop/hadoop-common -->
<!--        <dependency>-->
<!--            <groupId>org.apache.hadoop</groupId>-->
<!--            <artifactId>hadoop-common</artifactId>-->
<!--            <version>${hadoop.version}</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>org.slf4j</groupId>-->
<!--                    <artifactId>slf4j-log4j12</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>log4j</groupId>-->
<!--                    <artifactId>log4j</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
        <!-- https://mvnrepository.com/artifact/org.apache.phoenix/phoenix-queryserver-client -->
<!--        <dependency>-->
<!--            <groupId>org.apache.phoenix</groupId>-->
<!--            <artifactId>phoenix-queryserver-client</artifactId>-->
<!--            <version>${phoenix.version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>${protobuf-java.version}</version>
        </dependency>


        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybaits-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis.caches</groupId>
            <artifactId>mybatis-ehcache</artifactId>
            <version>1.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-typehandlers-jsr310</artifactId>
            <version>1.0.2</version>
        </dependency>
        <!--MP代码自动生成-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>${mybaits-plus.version}</version>
        </dependency>
        <!-- 模板引擎 -->
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
            <version>2.4</version>
        </dependency>
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc11</artifactId>
            <version>21.7.0.0</version>
        </dependency>
        <!--tencent 语音转文字 add by zhang 20240726-->
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java</artifactId>
            <!-- go to https://search.maven.org/search?q=tencentcloud-sdk-java and get the latest version. -->
            <!-- 请到https://search.maven.org/search?q=tencentcloud-sdk-java查询所有版本，最新版本如下 -->
            <version>3.1.1000</version>
        </dependency>
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-common</artifactId>
            <version>3.1.1000</version>
        </dependency>


        <dependency>
            <groupId>io.github.sashirestela</groupId>
            <artifactId>simple-openai</artifactId>
            <version>3.3.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <profiles>
        <!--广东12348生产-->
        <profile>
            <id>gd12348Prod</id>
            <properties>
                <activeProfile>gd12348Prod</activeProfile>
            </properties>
        </profile>
        <!--广东12348预生产-->
        <profile>
            <id>gd12348Pre</id>
            <properties>
                <activeProfile>gd12348Pre</activeProfile>
            </properties>
        </profile>
        <!--广东12348测试-->
        <profile>
            <id>gd12348Test</id>
            <properties>
                <activeProfile>gd12348Test</activeProfile>
            </properties>
        </profile>
        <!--广东12348测试-->
        <profile>
            <id>gd12348Yun</id>
            <properties>
                <activeProfile>gd12348Yun</activeProfile>
            </properties>
        </profile>
        <!--广东12348开发-->
        <profile>
            <id>gd12348Dev</id>
            <properties>
                <activeProfile>gd12348Dev</activeProfile>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
    </profiles>

    <build>
       <!-- <finalName>${project.artifactId}</finalName>-->
        <finalName>ROOT</finalName>
        <plugins>
            <!-- 在这里添加 springloader plugin,热部署-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.gobon.GobonCallCenterServletInitializer</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>

                <dependencies>
                    <dependency>
                        <groupId>org.springframework</groupId>
                        <artifactId>springloaded</artifactId>
                        <version>1.2.4.RELEASE</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
           </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/bin</directory>
                <includes>
                    <include>*.sh</include>
                </includes>
                <filtering>true</filtering>
            </resource>

            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                    <include>**/*.yml</include>
                    <include>**/*.htm</include>
                    <include>**/*.js</include>
                    <include>**/*.css</include>
                    <include>**/*.png</include>
                    <include>**/*.gif</include>
                    <include>**/*.jpg</include>
                    <include>**/*.map</include>
                    <include>**/*.json</include>
                </includes>
                <filtering>true</filtering>
            </resource>

            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>mybatis/**</include>
                    <include>vm/**</include>
                    <include>i18n/**</include>
                    <include>*.json</include>
                    <include>*.key</include>
                    <include>*.p12</include>
                    <include>**/*.woff</include>
                    <include>**/*.woff2</include>
                    <include>**/*.ttf</include>
                    <include>**/*.dat</include>
                </includes>
                <filtering>false</filtering>
            </resource>

        </resources>
    </build>
</project>
