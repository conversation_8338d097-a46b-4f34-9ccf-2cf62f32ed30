package com.gobon.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @program: goboncallcenter
 * @description:
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2020/4/21 15:48
 **/
@Data
public class WebSpeechProperties {

    /**
     * 中英文，http url 不支持解析 ws/wss schema
     */
    private String hostUrl;

    /**
     * 在控制台-我的应用-语音听写（流式版）获取
     */
    private String apiSecret;

    /**
     * 在控制台-我的应用-语音听写（流式版）获取
     */
    private String apiKey;

    /**
     * 在控制台-我的应用获取
     */
    private String appId;

    /**
     * 录音截取地址
     */
    private String targetPath;

    /**
     *
     */
    private String file;
    private int StatusFirstFrame = 0;
    private int StatusContinueFrame = 1;
    private int StatusLastFrame = 2;
}
