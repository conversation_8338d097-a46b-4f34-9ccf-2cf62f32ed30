package com.gobon.utils;

import it.sauronsoftware.jave.Encoder;
import it.sauronsoftware.jave.EncoderException;
import it.sauronsoftware.jave.MultimediaInfo;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Slf4j
public class WavUtil {
    public static void main(String[] args) throws IOException, InterruptedException {
//        Process process = run.exec("ffprobe -i D:/Chromedownload/2023-08-27-15-47-53_2113_13075289625.wav -show_entries format=duration -v quiet -of csv=\"p=0\"");
//        System.out.println(p.);
        String ffmpegPath = "ffmpeg"; // FFmpeg可执行文件路径
        String inputFilePath = "D:/Chromedownload/2023-08-27-15-47-53_2113_13075289625.wav"; // 输入WAV文件路径

        try {
            // 构建FFmpeg命令
            String[] command = { ffmpegPath, "-i", inputFilePath };

            // 执行命令并获取输出结果
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            Process process = processBuilder.start();
            InputStream inputStream = process.getErrorStream();
            InputStreamReader inputStreamReader = new InputStreamReader(inputStream);
            BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

            String line;
            String duration = null;

            // 读取FFmpeg输出结果中的时长信息
            while ((line = bufferedReader.readLine()) != null) {
                if (line.contains("Duration:")) {
                    int start = line.indexOf("Duration:") + 10;
                    int end = line.indexOf(",", start);
                    duration = line.substring(start, end);
                    break;
                }
            }

            // 将时长格式转换为秒数
            if (duration != null) {
                String[] timeComponents = duration.split(":");
                int hours = Integer.parseInt(timeComponents[0]);
                int minutes = Integer.parseInt(timeComponents[1]);
                float seconds = Float.parseFloat(timeComponents[2]);

                float totalSeconds = (hours * 3600) + (minutes * 60) + seconds;
                System.out.println("WAV文件时长（秒数）：" + new Float(totalSeconds).longValue());
            } else {
                System.out.println("无法获取WAV文件时长。");
            }

            // 关闭流和进程
            bufferedReader.close();
            inputStreamReader.close();
            inputStream.close();
            process.destroy();

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 删除文件夹下文件名包含指定字符的文件
     *
     * @param folder     文件夹路径 D:/audio
     * @param containStr 包含字符串
     */
    public static void deleteTempFile(File folder, String containStr) {
        List<File> result = new ArrayList<File>();
        if (folder.isFile())
            result.add(folder);

        File[] subFolders = folder.listFiles(new FileFilter() {
            @Override
            public boolean accept(File file) {
                if (file.isDirectory()) {
                    return true;
                }
                if (file.getName().toLowerCase().contains(containStr)) {
                    return true;
                }
                return false;
            }
        });
        System.out.println(subFolders);
        for (File subFolder : subFolders) {
            subFolder.delete();
        }

    }

    /**
     * 截取文件
     *
     * @param sourceFilePath 源文件 D:/audio/周杰伦+-+枫.wav
     * @param targetPath     截取存放路径 D:/audio/
     * @param cutFileName    截取文件命名
     * @param timeLong       截取时长
     * @param delete         是否删除源文件
     */
    public static List<String> cutAudio(String sourceFilePath, String targetPath, String cutFileName, Integer timeLong, boolean delete) throws IOException {
        int start = 0;
        int end = 0;
        long time = getTimeLen(new File(sourceFilePath));
        int newTime = (int) time;
        int internal = newTime - end;
        List<String> fileList = new ArrayList<>();
        while (end <= newTime) {
            end += timeLong;
            if (end > newTime) {
                end = newTime;
            }
            cut(sourceFilePath, targetPath + cutFileName + "-cut_" + start + "_" + end + ".wav", start, end);
            fileList.add(targetPath + cutFileName + "-cut_" + start + "_" + end + ".wav");
            if (end == newTime) {
                break;
            }
            start += timeLong;
            internal = newTime - end;
        }
        //截取完删除远端下载后的文件
        if (delete) {
            File sourceFile = new File(sourceFilePath);
            if (sourceFile.exists()) {
//                sourceFile.delete();
            }
        }
        return fileList;
    }

    /**
     * 截取wav音频文件
     *
     * @param sourcefile 源文件地址
     * @param targetfile 目标文件地址
     * @param start      截取开始时间（秒）
     * @param end        截取结束时间（秒）
     *                   <p>
     *                   return  截取成功返回true，否则返回false
     */
    public static boolean cut(String sourcefile, String targetfile, int start, int end) throws IOException {
        String cutCmdStr = "ffmpeg -i " + sourcefile + " -ss " + start + " -to " + end + " -y " + targetfile;
        log.error("------------剪切音频开始：{}", cutCmdStr);
        Runtime run = Runtime.getRuntime();
        Process p = run.exec(cutCmdStr);
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        log.error("------------剪切音频结束：{}", cutCmdStr);
        return true;
    }

    /**
     * 获取音频文件总时长
     *
     * @param file 文件路径
     * @return
     */
    public static long getTimeLen(File file) {
        long tlen = 0;
        if (file != null && file.exists()) {
//            String
//            Encoder encoder = new Encoder();
//            try {
//                log.error("获取音频时长：" + file);
//                MultimediaInfo m = encoder.getInfo(file);
//                long ls = m.getDuration();
//                tlen = ls / 1000;
//            } catch (Exception e) {
//                e.printStackTrace();
//                log.error("获取音频文件总时长异常：", e);
//            }

            String ffmpegPath = "ffmpeg"; // FFmpeg可执行文件路径
            String inputFilePath = file.getAbsolutePath(); // 输入WAV文件路径

            try {
                // 构建FFmpeg命令
                String[] command = { ffmpegPath, "-i", inputFilePath };

                // 执行命令并获取输出结果
                ProcessBuilder processBuilder = new ProcessBuilder(command);
                Process process = processBuilder.start();
                InputStream inputStream = process.getErrorStream();
                InputStreamReader inputStreamReader = new InputStreamReader(inputStream);
                BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

                String line;
                String duration = null;

                // 读取FFmpeg输出结果中的时长信息
                while ((line = bufferedReader.readLine()) != null) {
                    if (line.contains("Duration:")) {
                        int start = line.indexOf("Duration:") + 10;
                        int end = line.indexOf(",", start);
                        duration = line.substring(start, end);
                        break;
                    }
                }

                // 将时长格式转换为秒数
                if (duration != null) {
                    String[] timeComponents = duration.split(":");
                    int hours = Integer.parseInt(timeComponents[0]);
                    int minutes = Integer.parseInt(timeComponents[1]);
                    float seconds = Float.parseFloat(timeComponents[2]);

                    float totalSeconds = (hours * 3600) + (minutes * 60) + seconds;

                    System.out.println("WAV文件时长（秒数）：" + new Float(totalSeconds).longValue());
                    return new Float(totalSeconds).longValue();
                } else {
                    System.out.println("无法获取WAV文件时长。");
                }

                // 关闭流和进程
                bufferedReader.close();
                inputStreamReader.close();
                inputStream.close();
                process.destroy();

            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return tlen;
    }

    /**
     * 数组反转
     *
     * @param array
     */
    public static byte[] reverse(byte[] array) {
        byte temp;
        int len = array.length;
        for (int i = 0; i < len / 2; i++) {
            temp = array[i];
            array[i] = array[len - 1 - i];
            array[len - 1 - i] = temp;
        }
        return array;
    }


}