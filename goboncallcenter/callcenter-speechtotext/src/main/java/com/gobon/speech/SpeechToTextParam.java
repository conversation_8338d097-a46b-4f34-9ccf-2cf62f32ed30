package com.gobon.speech;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @program: goboncallcenter
 * @description:
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2020/8/28 11:41
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SpeechToTextParam implements Serializable {
    private static final long serialVersionUID = -7425492251503182875L;

    /**
     * 业务主键id
     */
    @NotNull(message = "业务主键id不能为空")
    private Long serviceId;
    /**
     * 来源 (1:语音分割转文字)
     */
    private String source;
    /**
     * 录音地址
     */
    @NotBlank(message = "录音地址不能为空")
    private String audioUrl;
}
